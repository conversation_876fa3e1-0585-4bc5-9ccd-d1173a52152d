{
  "include": ["**/*.ts", "**/*.tsx"],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"],
      "~/components/*": ["./app/core/components/*"],
      "~/utils/*": ["./app/core/utils/*"],
      "~/user/*": ["./app/modules/user/*"],
      "~/auth/*": ["./app/modules/auth/*"],
      "~/category/*": ["./app/modules/category/*"],
      "~/brand/*": ["./app/modules/brand/*"],
      "~/measurement-unit/*": ["./app/modules/measurement-unit/*"],
      "~/container/*": ["./app/modules/container/*"],
      "~/product/*": ["./app/modules/product/*"],
      "~/worker/*": ["./app/modules/worker/*"],
      "~/client/*": ["./app/modules/client/*"],
      "~/person/*": ["./app/modules/person/*"],
    },

    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "module": "ESNext",
    "target": "ES2022",
    "skipLibCheck": true,
    "strictNullChecks": true,
  }
}
