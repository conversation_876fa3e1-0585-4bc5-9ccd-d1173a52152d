import{h as z,p as _,i as f,s as Q,k as O,n as x,l as T,m as k,t as V,o as W,q as N,v as L,w as j,r as p,x as K,c as G}from"./client--LJeqQnE.js";var J=class extends z{constructor(t,e){super(),this.options=e,this.#s=t,this.#r=null,this.#i=_(),this.options.experimental_prefetchInRender||this.#i.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#s;#t=void 0;#p=void 0;#e=void 0;#a;#c;#i;#r;#v;#l;#d;#o;#h;#n;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.#t.addObserver(this),B(this.#t,this.options)?this.#u():this.updateResult(),this.#g())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return w(this.#t,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return w(this.#t,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#m(),this.#O(),this.#t.removeObserver(this)}setOptions(t){const e=this.options,s=this.#t;if(this.options=this.#s.defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof f(this.options.enabled,this.#t)!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#C(),this.#t.setOptions(this.options),e._defaulted&&!Q(this.options,e)&&this.#s.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#t,observer:this});const i=this.hasListeners();i&&A(this.#t,s,this.options,e)&&this.#u(),this.updateResult(),i&&(this.#t!==s||f(this.options.enabled,this.#t)!==f(e.enabled,this.#t)||O(this.options.staleTime,this.#t)!==O(e.staleTime,this.#t))&&this.#R();const a=this.#b();i&&(this.#t!==s||f(this.options.enabled,this.#t)!==f(e.enabled,this.#t)||a!==this.#n)&&this.#y(a)}getOptimisticResult(t){const e=this.#s.getQueryCache().build(this.#s,t),s=this.createResult(e,t);return Y(this,s)&&(this.#e=s,this.#c=this.options,this.#a=this.#t.state),s}getCurrentResult(){return this.#e}trackResult(t,e){return new Proxy(t,{get:(s,i)=>(this.trackProp(i),e?.(i),Reflect.get(s,i))})}trackProp(t){this.#f.add(t)}getCurrentQuery(){return this.#t}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=this.#s.defaultQueryOptions(t),s=this.#s.getQueryCache().build(this.#s,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#u({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#e))}#u(t){this.#C();let e=this.#t.fetch(this.options,t);return t?.throwOnError||(e=e.catch(x)),e}#R(){this.#m();const t=O(this.options.staleTime,this.#t);if(T||this.#e.isStale||!k(t))return;const s=V(this.#e.dataUpdatedAt,t)+1;this.#o=setTimeout(()=>{this.#e.isStale||this.updateResult()},s)}#b(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.#t):this.options.refetchInterval)??!1}#y(t){this.#O(),this.#n=t,!(T||f(this.options.enabled,this.#t)===!1||!k(this.#n)||this.#n===0)&&(this.#h=setInterval(()=>{(this.options.refetchIntervalInBackground||W.isFocused())&&this.#u()},this.#n))}#g(){this.#R(),this.#y(this.#b())}#m(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}#O(){this.#h&&(clearInterval(this.#h),this.#h=void 0)}createResult(t,e){const s=this.#t,i=this.options,a=this.#e,c=this.#a,r=this.#c,o=t!==s?t.state:this.#p,{state:l}=t;let n={...l},y=!1,h;if(e._optimisticResults){const u=this.hasListeners(),g=!u&&B(t,e),b=u&&A(t,s,e,i);(g||b)&&(n={...n,...N(l.data,t.options)}),e._optimisticResults==="isRestoring"&&(n.fetchStatus="idle")}let{error:F,errorUpdatedAt:P,status:v}=n;h=n.data;let U=!1;if(e.placeholderData!==void 0&&h===void 0&&v==="pending"){let u;a?.isPlaceholderData&&e.placeholderData===r?.placeholderData?(u=a.data,U=!0):u=typeof e.placeholderData=="function"?e.placeholderData(this.#d?.state.data,this.#d):e.placeholderData,u!==void 0&&(v="success",h=L(a?.data,u,e),y=!0)}if(e.select&&h!==void 0&&!U)if(a&&h===c?.data&&e.select===this.#v)h=this.#l;else try{this.#v=e.select,h=e.select(h),h=L(a?.data,h,e),this.#l=h,this.#r=null}catch(u){this.#r=u}this.#r&&(F=this.#r,h=this.#l,P=Date.now(),v="error");const C=n.fetchStatus==="fetching",S=v==="pending",E=v==="error",D=S&&C,M=h!==void 0,d={status:v,fetchStatus:n.fetchStatus,isPending:S,isSuccess:v==="success",isError:E,isInitialLoading:D,isLoading:D,data:h,dataUpdatedAt:n.dataUpdatedAt,error:F,errorUpdatedAt:P,failureCount:n.fetchFailureCount,failureReason:n.fetchFailureReason,errorUpdateCount:n.errorUpdateCount,isFetched:n.dataUpdateCount>0||n.errorUpdateCount>0,isFetchedAfterMount:n.dataUpdateCount>o.dataUpdateCount||n.errorUpdateCount>o.errorUpdateCount,isFetching:C,isRefetching:C&&!S,isLoadingError:E&&!M,isPaused:n.fetchStatus==="paused",isPlaceholderData:y,isRefetchError:E&&M,isStale:I(t,e),refetch:this.refetch,promise:this.#i};if(this.options.experimental_prefetchInRender){const u=m=>{d.status==="error"?m.reject(d.error):d.data!==void 0&&m.resolve(d.data)},g=()=>{const m=this.#i=d.promise=_();u(m)},b=this.#i;switch(b.status){case"pending":t.queryHash===s.queryHash&&u(b);break;case"fulfilled":(d.status==="error"||d.data!==b.value)&&g();break;case"rejected":(d.status!=="error"||d.error!==b.reason)&&g();break}}return d}updateResult(){const t=this.#e,e=this.createResult(this.#t,this.options);if(this.#a=this.#t.state,this.#c=this.options,this.#a.data!==void 0&&(this.#d=this.#t),Q(e,t))return;this.#e=e;const s=()=>{if(!t)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!this.#f.size)return!0;const c=new Set(a??this.#f);return this.options.throwOnError&&c.add("error"),Object.keys(this.#e).some(r=>{const R=r;return this.#e[R]!==t[R]&&c.has(R)})};this.#S({listeners:s()})}#C(){const t=this.#s.getQueryCache().build(this.#s,this.options);if(t===this.#t)return;const e=this.#t;this.#t=t,this.#p=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#g()}#S(t){j.batch(()=>{t.listeners&&this.listeners.forEach(e=>{e(this.#e)}),this.#s.getQueryCache().notify({query:this.#t,type:"observerResultsUpdated"})})}};function X(t,e){return f(e.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&e.retryOnMount===!1)}function B(t,e){return X(t,e)||t.state.data!==void 0&&w(t,e,e.refetchOnMount)}function w(t,e,s){if(f(e.enabled,t)!==!1){const i=typeof s=="function"?s(t):s;return i==="always"||i!==!1&&I(t,e)}return!1}function A(t,e,s,i){return(t!==e||f(i.enabled,t)===!1)&&(!s.suspense||t.state.status!=="error")&&I(t,s)}function I(t,e){return f(e.enabled,t)!==!1&&t.isStaleByTime(O(e.staleTime,t))}function Y(t,e){return!Q(t.getCurrentResult(),e)}var q=p.createContext(!1),Z=()=>p.useContext(q);q.Provider;function $(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var tt=p.createContext($()),et=()=>p.useContext(tt),st=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))},it=t=>{p.useEffect(()=>{t.clearReset()},[t])},rt=({result:t,errorResetBoundary:e,throwOnError:s,query:i,suspense:a})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&(a&&t.data===void 0||K(s,[t.error,i])),nt=t=>{const e=t.staleTime;t.suspense&&(t.staleTime=typeof e=="function"?(...s)=>Math.max(e(...s),1e3):Math.max(e??1e3,1e3),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3)))},at=(t,e)=>t.isLoading&&t.isFetching&&!e,ot=(t,e)=>t?.suspense&&e.isPending,H=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function ht(t,e,s){const i=G(),a=Z(),c=et(),r=i.defaultQueryOptions(t);i.getDefaultOptions().queries?._experimental_beforeQuery?.(r),r._optimisticResults=a?"isRestoring":"optimistic",nt(r),st(r,c),it(c);const R=!i.getQueryCache().get(r.queryHash),[o]=p.useState(()=>new e(i,r)),l=o.getOptimisticResult(r),n=!a&&t.subscribed!==!1;if(p.useSyncExternalStore(p.useCallback(y=>{const h=n?o.subscribe(j.batchCalls(y)):x;return o.updateResult(),h},[o,n]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),p.useEffect(()=>{o.setOptions(r)},[r,o]),ot(r,l))throw H(r,o,c);if(rt({result:l,errorResetBoundary:c,throwOnError:r.throwOnError,query:i.getQueryCache().get(r.queryHash),suspense:r.suspense}))throw l.error;return i.getDefaultOptions().queries?._experimental_afterQuery?.(r,l),r.experimental_prefetchInRender&&!T&&at(l,a)&&(R?H(r,o,c):i.getQueryCache().get(r.queryHash)?.promise)?.catch(x).finally(()=>{o.updateResult()}),r.notifyOnChangeProps?l:o.trackResult(l)}function lt(t,e){return ht(t,J)}function dt(t){return t}export{dt as q,lt as u};
