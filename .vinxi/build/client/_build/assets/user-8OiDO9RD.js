import{h as f,s as v,z as h,A as y,w as l,c as C,r as i,n as g,x as w}from"./client--LJeqQnE.js";var O=class extends f{#s;#r=void 0;#t;#e;constructor(t,e){super(),this.#s=t,this.setOptions(e),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const e=this.options;this.options=this.#s.defaultMutationOptions(t),v(this.options,e)||this.#s.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#t,observer:this}),e?.mutationKey&&this.options.mutationKey&&h(e.mutationKey)!==h(this.options.mutationKey)?this.reset():this.#t?.state.status==="pending"&&this.#t.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#t?.removeObserver(this)}onMutationUpdate(t){this.#i(),this.#o(t)}getCurrentResult(){return this.#r}reset(){this.#t?.removeObserver(this),this.#t=void 0,this.#i(),this.#o()}mutate(t,e){return this.#e=e,this.#t?.removeObserver(this),this.#t=this.#s.getMutationCache().build(this.#s,this.options),this.#t.addObserver(this),this.#t.execute(t)}#i(){const t=this.#t?.state??y();this.#r={...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset}}#o(t){l.batch(()=>{if(this.#e&&this.hasListeners()){const e=this.#r.variables,r=this.#r.context;t?.type==="success"?(this.#e.onSuccess?.(t.data,e,r),this.#e.onSettled?.(t.data,null,e,r)):t?.type==="error"&&(this.#e.onError?.(t.error,e,r),this.#e.onSettled?.(void 0,t.error,e,r))}this.listeners.forEach(e=>{e(this.#r)})})}};function U(t,e){const r=C(),[s]=i.useState(()=>new O(r,t));i.useEffect(()=>{s.setOptions(t)},[s,t]);const o=i.useSyncExternalStore(i.useCallback(n=>s.subscribe(l.batchCalls(n)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),a=i.useCallback((n,u)=>{s.mutate(n,u).catch(g)},[s]);if(o.error&&w(s.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:a,mutateAsync:o.mutate}}/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),E=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),c=t=>{const e=E(t);return e.charAt(0).toUpperCase()+e.slice(1)},d=(...t)=>t.filter((e,r,s)=>!!e&&e.trim()!==""&&s.indexOf(e)===r).join(" ").trim(),M=t=>{for(const e in t)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var k={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=i.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:o="",children:a,iconNode:n,...u},p)=>i.createElement("svg",{ref:p,...k,width:e,height:e,stroke:t,strokeWidth:s?Number(r)*24/Number(e):r,className:d("lucide",o),...!a&&!M(u)&&{"aria-hidden":"true"},...u},[...n.map(([m,b])=>i.createElement(m,b)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=(t,e)=>{const r=i.forwardRef(({className:s,...o},a)=>i.createElement(A,{ref:a,iconNode:e,className:d(`lucide-${x(c(t))}`,`lucide-${t}`,s),...o}));return r.displayName=c(t),r};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],K=S("user",L);export{K as U,S as c,U as u};
