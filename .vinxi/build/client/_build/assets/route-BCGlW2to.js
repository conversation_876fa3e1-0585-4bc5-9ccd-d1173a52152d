import{a as m,r as i,y as n,j as e,O as p,N as f}from"./client--LJeqQnE.js";import{q as g,u as x}from"./queryOptions-DcASEHgW.js";import{A as d}from"./runtimes-RPMEsgVm.js";import{g as l}from"./effectErrors-DvJbF9aL.js";const y=({auth:s})=>g({queryKey:["authenticated"],queryFn:()=>d.runPromise(s.getSession())}),v=function(){const u=m(),{isLoading:a,isSuccess:r,isError:t,error:o}=x({...y(u)});return i.useEffect(()=>{if(t){const c=l(o);n.error(c.error.message)}},[t,o]),i.useEffect(()=>{r&&n.success("Usuario autenticado")},[r]),a?e.jsx("div",{children:"Verificando autenticación..."}):r?e.jsx(p,{}):e.jsx(f,{to:"/login"})};export{v as component};
