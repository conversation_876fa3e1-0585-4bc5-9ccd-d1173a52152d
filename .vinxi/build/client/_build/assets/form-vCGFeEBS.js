import{d as no,e as de,S as ro,D as zt,r as b,j as x,f as Zt,g as oo,R as bt}from"./client--LJeqQnE.js";import{c as Fe}from"./user-8OiDO9RD.js";const en="-",io=e=>{const t=ao(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:i=>{const a=i.split(en);return a[0]===""&&a.length!==1&&a.shift(),ur(a,t)||so(i)},getConflictingClassGroupIds:(i,a)=>{const l=r[i]||[];return a&&n[i]?[...l,...n[i]]:l}}},ur=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),o=n?ur(e.slice(1),n):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(en);return t.validators.find(({validator:i})=>i(s))?.classGroupId},zn=/^\[(.+)\]$/,so=e=>{if(zn.test(e)){const t=zn.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},ao=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const o in r)Kt(r[o],n,o,t);return n},Kt=(e,t,r,n)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Kn(t,o);s.classGroupId=r;return}if(typeof o=="function"){if(lo(o)){Kt(o(n),t,r,n);return}t.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([s,i])=>{Kt(i,Kn(t,s),r,n)})})},Kn=(e,t)=>{let r=e;return t.split(en).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},lo=e=>e.isThemeGetter,co=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const o=(s,i)=>{r.set(s,i),t++,t>e&&(t=0,n=r,r=new Map)};return{get(s){let i=r.get(s);if(i!==void 0)return i;if((i=n.get(s))!==void 0)return o(s,i),i},set(s,i){r.has(s)?r.set(s,i):o(s,i)}}},Nt="!",Bt=":",uo=Bt.length,fo=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=o=>{const s=[];let i=0,a=0,l=0,c;for(let p=0;p<o.length;p++){let v=o[p];if(i===0&&a===0){if(v===Bt){s.push(o.slice(l,p)),l=p+uo;continue}if(v==="/"){c=p;continue}}v==="["?i++:v==="]"?i--:v==="("?a++:v===")"&&a--}const u=s.length===0?o:o.substring(l),d=ho(u),f=d!==u,m=c&&c>l?c-l:void 0;return{modifiers:s,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:m}};if(t){const o=t+Bt,s=n;n=i=>i.startsWith(o)?s(i.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const o=n;n=s=>r({className:s,parseClassName:o})}return n},ho=e=>e.endsWith(Nt)?e.substring(0,e.length-1):e.startsWith(Nt)?e.substring(1):e,po=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const o=[];let s=[];return n.forEach(i=>{i[0]==="["||t[i]?(o.push(...s.sort(),i),s=[]):s.push(i)}),o.push(...s.sort()),o}},mo=e=>({cache:co(e.cacheSize),parseClassName:fo(e),sortModifiers:po(e),...io(e)}),vo=/\s+/,go=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:s}=t,i=[],a=e.trim().split(vo);let l="";for(let c=a.length-1;c>=0;c-=1){const u=a[c],{isExternal:d,modifiers:f,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:v}=r(u);if(d){l=u+(l.length>0?" "+l:l);continue}let h=!!v,y=n(h?p.substring(0,v):p);if(!y){if(!h){l=u+(l.length>0?" "+l:l);continue}if(y=n(p),!y){l=u+(l.length>0?" "+l:l);continue}h=!1}const g=s(f).join(":"),A=m?g+Nt:g,R=A+y;if(i.includes(R))continue;i.push(R);const F=o(y,h);for(let P=0;P<F.length;++P){const N=F[P];i.push(A+N)}l=u+(l.length>0?" "+l:l)}return l};function bo(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=dr(t))&&(n&&(n+=" "),n+=r);return n}const dr=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=dr(e[n]))&&(r&&(r+=" "),r+=t);return r};function yo(e,...t){let r,n,o,s=i;function i(l){const c=t.reduce((u,d)=>d(u),e());return r=mo(c),n=r.cache.get,o=r.cache.set,s=a,a(l)}function a(l){const c=n(l);if(c)return c;const u=go(l,r);return o(l,u),u}return function(){return s(bo.apply(null,arguments))}}const Q=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},fr=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,hr=/^\((?:(\w[\w-]*):)?(.+)\)$/i,wo=/^\d+\/\d+$/,Mo=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Io=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,xo=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,So=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ko=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,De=e=>wo.test(e),K=e=>!!e&&!Number.isNaN(Number(e)),Me=e=>!!e&&Number.isInteger(Number(e)),Ot=e=>e.endsWith("%")&&K(e.slice(0,-1)),me=e=>Mo.test(e),Co=()=>!0,Eo=e=>Io.test(e)&&!xo.test(e),pr=()=>!1,Fo=e=>So.test(e),Vo=e=>ko.test(e),Oo=e=>!C(e)&&!E(e),Ao=e=>Le(e,gr,pr),C=e=>fr.test(e),ke=e=>Le(e,br,Eo),At=e=>Le(e,To,K),Nn=e=>Le(e,mr,pr),Ro=e=>Le(e,vr,Vo),ot=e=>Le(e,yr,Fo),E=e=>hr.test(e),Ue=e=>He(e,br),Do=e=>He(e,_o),Bn=e=>He(e,mr),jo=e=>He(e,gr),Po=e=>He(e,vr),it=e=>He(e,yr,!0),Le=(e,t,r)=>{const n=fr.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},He=(e,t,r=!1)=>{const n=hr.exec(e);return n?n[1]?t(n[1]):r:!1},mr=e=>e==="position"||e==="percentage",vr=e=>e==="image"||e==="url",gr=e=>e==="length"||e==="size"||e==="bg-size",br=e=>e==="length",To=e=>e==="number",_o=e=>e==="family-name",yr=e=>e==="shadow",zo=()=>{const e=Q("color"),t=Q("font"),r=Q("text"),n=Q("font-weight"),o=Q("tracking"),s=Q("leading"),i=Q("breakpoint"),a=Q("container"),l=Q("spacing"),c=Q("radius"),u=Q("shadow"),d=Q("inset-shadow"),f=Q("text-shadow"),m=Q("drop-shadow"),p=Q("blur"),v=Q("perspective"),h=Q("aspect"),y=Q("ease"),g=Q("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],R=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],F=()=>[...R(),E,C],P=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],M=()=>[E,C,l],I=()=>[De,"full","auto",...M()],S=()=>[Me,"none","subgrid",E,C],O=()=>["auto",{span:["full",Me,E,C]},Me,E,C],$=()=>[Me,"auto",E,C],G=()=>["auto","min","max","fr",E,C],T=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],X=()=>["start","end","center","stretch","center-safe","end-safe"],U=()=>["auto",...M()],ee=()=>[De,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...M()],w=()=>[e,E,C],q=()=>[...R(),Bn,Nn,{position:[E,C]}],ie=()=>["no-repeat",{repeat:["","x","y","space","round"]}],se=()=>["auto","cover","contain",jo,Ao,{size:[E,C]}],j=()=>[Ot,Ue,ke],k=()=>["","none","full",c,E,C],V=()=>["",K,Ue,ke],_=()=>["solid","dashed","dotted","double"],H=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],z=()=>[K,Ot,Bn,Nn],J=()=>["","none",p,E,C],te=()=>["none",K,E,C],Z=()=>["none",K,E,C],ne=()=>[K,E,C],re=()=>[De,"full",...M()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[me],breakpoint:[me],color:[Co],container:[me],"drop-shadow":[me],ease:["in","out","in-out"],font:[Oo],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[me],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[me],shadow:[me],spacing:["px",K],text:[me],"text-shadow":[me],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",De,C,E,h]}],container:["container"],columns:[{columns:[K,C,E,a]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:F()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:I()}],"inset-x":[{"inset-x":I()}],"inset-y":[{"inset-y":I()}],start:[{start:I()}],end:[{end:I()}],top:[{top:I()}],right:[{right:I()}],bottom:[{bottom:I()}],left:[{left:I()}],visibility:["visible","invisible","collapse"],z:[{z:[Me,"auto",E,C]}],basis:[{basis:[De,"full","auto",a,...M()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[K,De,"auto","initial","none",C]}],grow:[{grow:["",K,E,C]}],shrink:[{shrink:["",K,E,C]}],order:[{order:[Me,"first","last","none",E,C]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:O()}],"col-start":[{"col-start":$()}],"col-end":[{"col-end":$()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:O()}],"row-start":[{"row-start":$()}],"row-end":[{"row-end":$()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":G()}],"auto-rows":[{"auto-rows":G()}],gap:[{gap:M()}],"gap-x":[{"gap-x":M()}],"gap-y":[{"gap-y":M()}],"justify-content":[{justify:[...T(),"normal"]}],"justify-items":[{"justify-items":[...X(),"normal"]}],"justify-self":[{"justify-self":["auto",...X()]}],"align-content":[{content:["normal",...T()]}],"align-items":[{items:[...X(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...X(),{baseline:["","last"]}]}],"place-content":[{"place-content":T()}],"place-items":[{"place-items":[...X(),"baseline"]}],"place-self":[{"place-self":["auto",...X()]}],p:[{p:M()}],px:[{px:M()}],py:[{py:M()}],ps:[{ps:M()}],pe:[{pe:M()}],pt:[{pt:M()}],pr:[{pr:M()}],pb:[{pb:M()}],pl:[{pl:M()}],m:[{m:U()}],mx:[{mx:U()}],my:[{my:U()}],ms:[{ms:U()}],me:[{me:U()}],mt:[{mt:U()}],mr:[{mr:U()}],mb:[{mb:U()}],ml:[{ml:U()}],"space-x":[{"space-x":M()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":M()}],"space-y-reverse":["space-y-reverse"],size:[{size:ee()}],w:[{w:[a,"screen",...ee()]}],"min-w":[{"min-w":[a,"screen","none",...ee()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...ee()]}],h:[{h:["screen","lh",...ee()]}],"min-h":[{"min-h":["screen","lh","none",...ee()]}],"max-h":[{"max-h":["screen","lh",...ee()]}],"font-size":[{text:["base",r,Ue,ke]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,E,At]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ot,C]}],"font-family":[{font:[Do,C,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,E,C]}],"line-clamp":[{"line-clamp":[K,"none",E,At]}],leading:[{leading:[s,...M()]}],"list-image":[{"list-image":["none",E,C]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",E,C]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:w()}],"text-color":[{text:w()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[..._(),"wavy"]}],"text-decoration-thickness":[{decoration:[K,"from-font","auto",E,ke]}],"text-decoration-color":[{decoration:w()}],"underline-offset":[{"underline-offset":[K,"auto",E,C]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",E,C]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",E,C]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:q()}],"bg-repeat":[{bg:ie()}],"bg-size":[{bg:se()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Me,E,C],radial:["",E,C],conic:[Me,E,C]},Po,Ro]}],"bg-color":[{bg:w()}],"gradient-from-pos":[{from:j()}],"gradient-via-pos":[{via:j()}],"gradient-to-pos":[{to:j()}],"gradient-from":[{from:w()}],"gradient-via":[{via:w()}],"gradient-to":[{to:w()}],rounded:[{rounded:k()}],"rounded-s":[{"rounded-s":k()}],"rounded-e":[{"rounded-e":k()}],"rounded-t":[{"rounded-t":k()}],"rounded-r":[{"rounded-r":k()}],"rounded-b":[{"rounded-b":k()}],"rounded-l":[{"rounded-l":k()}],"rounded-ss":[{"rounded-ss":k()}],"rounded-se":[{"rounded-se":k()}],"rounded-ee":[{"rounded-ee":k()}],"rounded-es":[{"rounded-es":k()}],"rounded-tl":[{"rounded-tl":k()}],"rounded-tr":[{"rounded-tr":k()}],"rounded-br":[{"rounded-br":k()}],"rounded-bl":[{"rounded-bl":k()}],"border-w":[{border:V()}],"border-w-x":[{"border-x":V()}],"border-w-y":[{"border-y":V()}],"border-w-s":[{"border-s":V()}],"border-w-e":[{"border-e":V()}],"border-w-t":[{"border-t":V()}],"border-w-r":[{"border-r":V()}],"border-w-b":[{"border-b":V()}],"border-w-l":[{"border-l":V()}],"divide-x":[{"divide-x":V()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":V()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[..._(),"hidden","none"]}],"divide-style":[{divide:[..._(),"hidden","none"]}],"border-color":[{border:w()}],"border-color-x":[{"border-x":w()}],"border-color-y":[{"border-y":w()}],"border-color-s":[{"border-s":w()}],"border-color-e":[{"border-e":w()}],"border-color-t":[{"border-t":w()}],"border-color-r":[{"border-r":w()}],"border-color-b":[{"border-b":w()}],"border-color-l":[{"border-l":w()}],"divide-color":[{divide:w()}],"outline-style":[{outline:[..._(),"none","hidden"]}],"outline-offset":[{"outline-offset":[K,E,C]}],"outline-w":[{outline:["",K,Ue,ke]}],"outline-color":[{outline:w()}],shadow:[{shadow:["","none",u,it,ot]}],"shadow-color":[{shadow:w()}],"inset-shadow":[{"inset-shadow":["none",d,it,ot]}],"inset-shadow-color":[{"inset-shadow":w()}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:w()}],"ring-offset-w":[{"ring-offset":[K,ke]}],"ring-offset-color":[{"ring-offset":w()}],"inset-ring-w":[{"inset-ring":V()}],"inset-ring-color":[{"inset-ring":w()}],"text-shadow":[{"text-shadow":["none",f,it,ot]}],"text-shadow-color":[{"text-shadow":w()}],opacity:[{opacity:[K,E,C]}],"mix-blend":[{"mix-blend":[...H(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":H()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[K]}],"mask-image-linear-from-pos":[{"mask-linear-from":z()}],"mask-image-linear-to-pos":[{"mask-linear-to":z()}],"mask-image-linear-from-color":[{"mask-linear-from":w()}],"mask-image-linear-to-color":[{"mask-linear-to":w()}],"mask-image-t-from-pos":[{"mask-t-from":z()}],"mask-image-t-to-pos":[{"mask-t-to":z()}],"mask-image-t-from-color":[{"mask-t-from":w()}],"mask-image-t-to-color":[{"mask-t-to":w()}],"mask-image-r-from-pos":[{"mask-r-from":z()}],"mask-image-r-to-pos":[{"mask-r-to":z()}],"mask-image-r-from-color":[{"mask-r-from":w()}],"mask-image-r-to-color":[{"mask-r-to":w()}],"mask-image-b-from-pos":[{"mask-b-from":z()}],"mask-image-b-to-pos":[{"mask-b-to":z()}],"mask-image-b-from-color":[{"mask-b-from":w()}],"mask-image-b-to-color":[{"mask-b-to":w()}],"mask-image-l-from-pos":[{"mask-l-from":z()}],"mask-image-l-to-pos":[{"mask-l-to":z()}],"mask-image-l-from-color":[{"mask-l-from":w()}],"mask-image-l-to-color":[{"mask-l-to":w()}],"mask-image-x-from-pos":[{"mask-x-from":z()}],"mask-image-x-to-pos":[{"mask-x-to":z()}],"mask-image-x-from-color":[{"mask-x-from":w()}],"mask-image-x-to-color":[{"mask-x-to":w()}],"mask-image-y-from-pos":[{"mask-y-from":z()}],"mask-image-y-to-pos":[{"mask-y-to":z()}],"mask-image-y-from-color":[{"mask-y-from":w()}],"mask-image-y-to-color":[{"mask-y-to":w()}],"mask-image-radial":[{"mask-radial":[E,C]}],"mask-image-radial-from-pos":[{"mask-radial-from":z()}],"mask-image-radial-to-pos":[{"mask-radial-to":z()}],"mask-image-radial-from-color":[{"mask-radial-from":w()}],"mask-image-radial-to-color":[{"mask-radial-to":w()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":R()}],"mask-image-conic-pos":[{"mask-conic":[K]}],"mask-image-conic-from-pos":[{"mask-conic-from":z()}],"mask-image-conic-to-pos":[{"mask-conic-to":z()}],"mask-image-conic-from-color":[{"mask-conic-from":w()}],"mask-image-conic-to-color":[{"mask-conic-to":w()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:q()}],"mask-repeat":[{mask:ie()}],"mask-size":[{mask:se()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",E,C]}],filter:[{filter:["","none",E,C]}],blur:[{blur:J()}],brightness:[{brightness:[K,E,C]}],contrast:[{contrast:[K,E,C]}],"drop-shadow":[{"drop-shadow":["","none",m,it,ot]}],"drop-shadow-color":[{"drop-shadow":w()}],grayscale:[{grayscale:["",K,E,C]}],"hue-rotate":[{"hue-rotate":[K,E,C]}],invert:[{invert:["",K,E,C]}],saturate:[{saturate:[K,E,C]}],sepia:[{sepia:["",K,E,C]}],"backdrop-filter":[{"backdrop-filter":["","none",E,C]}],"backdrop-blur":[{"backdrop-blur":J()}],"backdrop-brightness":[{"backdrop-brightness":[K,E,C]}],"backdrop-contrast":[{"backdrop-contrast":[K,E,C]}],"backdrop-grayscale":[{"backdrop-grayscale":["",K,E,C]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[K,E,C]}],"backdrop-invert":[{"backdrop-invert":["",K,E,C]}],"backdrop-opacity":[{"backdrop-opacity":[K,E,C]}],"backdrop-saturate":[{"backdrop-saturate":[K,E,C]}],"backdrop-sepia":[{"backdrop-sepia":["",K,E,C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":M()}],"border-spacing-x":[{"border-spacing-x":M()}],"border-spacing-y":[{"border-spacing-y":M()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",E,C]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[K,"initial",E,C]}],ease:[{ease:["linear","initial",y,E,C]}],delay:[{delay:[K,E,C]}],animate:[{animate:["none",g,E,C]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,E,C]}],"perspective-origin":[{"perspective-origin":F()}],rotate:[{rotate:te()}],"rotate-x":[{"rotate-x":te()}],"rotate-y":[{"rotate-y":te()}],"rotate-z":[{"rotate-z":te()}],scale:[{scale:Z()}],"scale-x":[{"scale-x":Z()}],"scale-y":[{"scale-y":Z()}],"scale-z":[{"scale-z":Z()}],"scale-3d":["scale-3d"],skew:[{skew:ne()}],"skew-x":[{"skew-x":ne()}],"skew-y":[{"skew-y":ne()}],transform:[{transform:[E,C,"","none","gpu","cpu"]}],"transform-origin":[{origin:F()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:re()}],"translate-x":[{"translate-x":re()}],"translate-y":[{"translate-y":re()}],"translate-z":[{"translate-z":re()}],"translate-none":["translate-none"],accent:[{accent:w()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:w()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",E,C]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",E,C]}],fill:[{fill:["none",...w()]}],"stroke-w":[{stroke:[K,Ue,ke,At]}],stroke:[{stroke:["none",...w()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ko=yo(zo),Ke=(...e)=>Ko(no(e));/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const No=[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]],wr=Fe("arrow-down",No);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bo=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],Mr=Fe("arrow-up",Bo);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $o=[["path",{d:"m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21",key:"182aya"}],["path",{d:"M22 21H7",key:"t4ddhn"}],["path",{d:"m5 11 9 9",key:"1mo9qw"}]],Ir=Fe("eraser",$o);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lo=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Ho=Fe("eye-off",Lo);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wo=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Go=Fe("eye",Wo);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uo=[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]],qo=Fe("key-round",Uo);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yo=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Xo=Fe("x",Yo);function Ct(e,t){return typeof e=="function"?e(t):e}function qe(e,t){return tn(t).reduce((n,o)=>{if(n===null)return null;if(typeof n<"u")return n[o]},e)}function st(e,t,r){const n=tn(t);function o(s){if(!n.length)return Ct(r,s);const i=n.shift();if(typeof i=="string"||typeof i=="number"&&!Array.isArray(s))return typeof s=="object"?(s===null&&(s={}),{...s,[i]:o(s[i])}):{[i]:o()};if(Array.isArray(s)&&typeof i=="number"){const a=s.slice(0,i);return[...a.length?a:new Array(i),o(s[i]),...s.slice(i+1)]}return[...new Array(i),o()]}return o(e)}function Jo(e,t){const r=tn(t);function n(o){if(!o)return;if(r.length===1){const i=r[0];if(Array.isArray(o)&&typeof i=="number")return o.filter((c,u)=>u!==i);const{[i]:a,...l}=o;return l}const s=r.shift();if(typeof s=="string"&&typeof o=="object")return{...o,[s]:n(o[s])};if(typeof s=="number"&&Array.isArray(o)){if(s>=o.length)return o;const i=o.slice(0,s);return[...i.length?i:new Array(s),n(o[s]),...o.slice(s+1)]}throw new Error("It seems we have created an infinite loop in deleteBy. ")}return n(e)}const Qo=/^(\d+)$/gm,Zo=/\.(\d+)(?=\.)/gm,ei=/^(\d+)\./gm,ti=/\.(\d+$)/gm,ni=/\.{2,}/gm,$t="__int__",at=`${$t}$1`;function tn(e){if(Array.isArray(e))return[...e];if(typeof e!="string")throw new Error("Path must be a string.");return e.replace(/(^\[)|]/gm,"").replace(/\[/g,".").replace(Qo,at).replace(Zo,`.${at}.`).replace(ei,`${at}.`).replace(ti,`.${at}`).replace(ni,".").split(".").map(t=>t.indexOf($t)===0?parseInt(t.substring($t.length),10):t)}function ri(e){return!(Array.isArray(e)&&e.length===0)}function Lt(e,t){const{asyncDebounceMs:r}=t,{onChangeAsync:n,onBlurAsync:o,onSubmitAsync:s,onBlurAsyncDebounceMs:i,onChangeAsyncDebounceMs:a}=t.validators||{},l=r??0,c={cause:"change",validate:n,debounceMs:a??l},u={cause:"blur",validate:o,debounceMs:i??l},d={cause:"submit",validate:s,debounceMs:0},f=m=>({...m,debounceMs:0});switch(e){case"submit":return[f(c),f(u),d];case"blur":return[u];case"change":return[c];case"server":default:return[]}}function Ht(e,t){const{onChange:r,onBlur:n,onSubmit:o,onMount:s}=t.validators||{},i={cause:"change",validate:r},a={cause:"blur",validate:n},l={cause:"submit",validate:o},c={cause:"mount",validate:s},u={cause:"server",validate:()=>{}};switch(e){case"mount":return[c];case"submit":return[i,a,l,u];case"server":return[u];case"blur":return[a,u];case"change":default:return[i,u]}}const Wt=e=>!!e&&typeof e=="object"&&"fields"in e;function je(e,t){if(Object.is(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[o,s]of e)if(!t.has(o)||!Object.is(s,t.get(o)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const o of e)if(!t.has(o))return!1;return!0}const r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(const o of r)if(!n.includes(o)||!je(e[o],t[o]))return!1;return!0}const $n=({newFormValidatorError:e,isPreviousErrorFromFormValidator:t,previousErrorValue:r})=>e?{newErrorValue:e,newSource:"form"}:t?{newErrorValue:void 0,newSource:void 0}:r?{newErrorValue:r,newSource:"field"}:{newErrorValue:void 0,newSource:void 0},Ln=({formLevelError:e,fieldLevelError:t})=>t?{newErrorValue:t,newSource:"field"}:e?{newErrorValue:e,newSource:"form"}:{newErrorValue:void 0,newSource:void 0};function oi(e){const t=new Map;for(const r of e){const n=[...r.path??[]].map(o=>{const s=typeof o=="object"?o.key:o;return typeof s=="number"?`[${s}]`:s}).join(".").replace(/\.\[/g,"[");t.set(n,(t.get(n)??[]).concat(r))}return Object.fromEntries(t)}const Hn=e=>{const t=oi(e);return{form:t,fields:t}},Ne={validate({value:e,validationSource:t},r){const n=r["~standard"].validate(e);if(n instanceof Promise)throw new Error("async function passed to sync validator");if(n.issues)return t==="field"?n.issues:Hn(n.issues)},async validateAsync({value:e,validationSource:t},r){const n=await r["~standard"].validate(e);if(n.issues)return t==="field"?n.issues:Hn(n.issues)}},xr=e=>!!e&&"~standard"in e,yt={isValidating:!1,isTouched:!1,isBlurred:!1,isDirty:!1,isPristine:!0,isValid:!0,isDefaultValue:!0,errors:[],errorMap:{},errorSourceMap:{}};function lt(e){function t(d,f,m,p){const v=n(d,f,m,p);({insert:()=>a(v,d,f),remove:()=>l(v),swap:()=>p!==void 0&&u(v,d,f,p),move:()=>p!==void 0&&c(v,d,f,p)})[m]()}function r(d,f){return`${d}[${f}]`}function n(d,f,m,p){const v=[r(d,f)];if(m==="swap")v.push(r(d,p));else if(m==="move"){const[h,y]=[Math.min(f,p),Math.max(f,p)];for(let g=h;g<=y;g++)v.push(r(d,g))}else{const h=e.getFieldValue(d),y=Array.isArray(h)?h.length:0;for(let g=f+1;g<y;g++)v.push(r(d,g))}return Object.keys(e.fieldInfo).filter(h=>v.some(y=>h.startsWith(y)))}function o(d,f){return d.replace(/\[(\d+)\]/,(m,p)=>{const v=parseInt(p,10);return`[${f==="up"?v+1:Math.max(0,v-1)}]`})}function s(d,f){(f==="up"?d:[...d].reverse()).forEach(p=>{const v=o(p.toString(),f),h=e.getFieldMeta(v);h?e.setFieldMeta(p,h):e.setFieldMeta(p,i())})}const i=()=>yt,a=(d,f,m)=>{s(d,"down"),d.forEach(p=>{p.toString().startsWith(r(f,m))&&e.setFieldMeta(p,i())})},l=d=>{s(d,"up")},c=(d,f,m,p)=>{const v=new Map(Object.keys(e.fieldInfo).filter(h=>h.startsWith(r(f,m))).map(h=>[h,e.getFieldMeta(h)]));s(d,m<p?"up":"down"),Object.keys(e.fieldInfo).filter(h=>h.startsWith(r(f,p))).forEach(h=>{const y=h.replace(r(f,p),r(f,m)),g=v.get(y);g&&e.setFieldMeta(h,g)})},u=(d,f,m,p)=>{d.forEach(v=>{if(!v.toString().startsWith(r(f,m)))return;const h=v.toString().replace(r(f,m),r(f,p)),[y,g]=[e.getFieldMeta(v),e.getFieldMeta(h)];y&&e.setFieldMeta(h,y),g&&e.setFieldMeta(v,g)})};return{handleArrayFieldMetaShift:t}}function Rt(e){return{values:e.values??{},errorMap:e.errorMap??{},fieldMetaBase:e.fieldMetaBase??{},isSubmitted:e.isSubmitted??!1,isSubmitting:e.isSubmitting??!1,isValidating:e.isValidating??!1,submissionAttempts:e.submissionAttempts??0,isSubmitSuccessful:e.isSubmitSuccessful??!1,validationMetaMap:e.validationMetaMap??{onChange:void 0,onBlur:void 0,onSubmit:void 0,onMount:void 0,onServer:void 0}}}class ii{constructor(t){var r;this.options={},this.fieldInfo={},this.prevTransformArray=[],this.mount=()=>{var n,o;const s=this.fieldMetaDerived.mount(),i=this.store.mount(),a=()=>{s(),i()};(o=(n=this.options.listeners)==null?void 0:n.onMount)==null||o.call(n,{formApi:this});const{onMount:l}=this.options.validators||{};return l&&this.validateSync("mount"),a},this.update=n=>{var o,s;if(!n)return;const i=this.options;this.options=n;const a=!!((s=(o=n.transform)==null?void 0:o.deps)!=null&&s.some((u,d)=>u!==this.prevTransformArray[d])),l=n.defaultValues&&!je(n.defaultValues,i.defaultValues)&&!this.state.isTouched,c=!je(n.defaultState,i.defaultState)&&!this.state.isTouched;!l&&!c&&!a||de(()=>{this.baseStore.setState(()=>Rt(Object.assign({},this.state,c?n.defaultState:{},l?{values:n.defaultValues}:{},a?{_force_re_eval:!this.state._force_re_eval}:{})))})},this.reset=(n,o)=>{const{fieldMeta:s}=this.state,i=this.resetFieldMeta(s);n&&!o?.keepDefaultValues&&(this.options={...this.options,defaultValues:n}),this.baseStore.setState(()=>{var a;return Rt({...this.options.defaultState,values:n??this.options.defaultValues??((a=this.options.defaultState)==null?void 0:a.values),fieldMetaBase:i})})},this.validateAllFields=async n=>{const o=[];return de(()=>{Object.values(this.fieldInfo).forEach(i=>{if(!i.instance)return;const a=i.instance;o.push(Promise.resolve().then(()=>a.validate(n,{skipFormValidation:!0}))),i.instance.state.meta.isTouched||i.instance.setMeta(l=>({...l,isTouched:!0}))})}),(await Promise.all(o)).flat()},this.validateArrayFieldsStartingFrom=async(n,o,s)=>{const i=this.getFieldValue(n),a=Array.isArray(i)?Math.max(i.length-1,0):null,l=[`${n}[${o}]`];for(let f=o+1;f<=(a??0);f++)l.push(`${n}[${f}]`);const c=Object.keys(this.fieldInfo).filter(f=>l.some(m=>f.startsWith(m))),u=[];return de(()=>{c.forEach(f=>{u.push(Promise.resolve().then(()=>this.validateField(f,s)))})}),(await Promise.all(u)).flat()},this.validateField=(n,o)=>{var s;const i=(s=this.fieldInfo[n])==null?void 0:s.instance;return i?(i.state.meta.isTouched||i.setMeta(a=>({...a,isTouched:!0})),i.validate(o)):[]},this.validateSync=n=>{const o=Ht(n,this.options);let s=!1;const i={};return de(()=>{var a,l;for(const u of o){if(!u.validate)continue;const d=this.runValidator({validate:u.validate,value:{value:this.state.values,formApi:this,validationSource:"form"},type:"validate"}),{formError:f,fieldErrors:m}=pt(d),p=ct(u.cause);for(const v of Object.keys(this.state.fieldMeta)){const h=this.getFieldMeta(v);if(!h)continue;const{errorMap:y,errorSourceMap:g}=h,A=m?.[v],{newErrorValue:R,newSource:F}=$n({newFormValidatorError:A,isPreviousErrorFromFormValidator:g?.[p]==="form",previousErrorValue:y?.[p]});F==="form"&&(i[v]={...i[v],[p]:A}),y?.[p]!==R&&this.setFieldMeta(v,P=>({...P,errorMap:{...P.errorMap,[p]:R},errorSourceMap:{...P.errorSourceMap,[p]:F}}))}((a=this.state.errorMap)==null?void 0:a[p])!==f&&this.baseStore.setState(v=>({...v,errorMap:{...v.errorMap,[p]:f}})),(f||m)&&(s=!0)}const c=ct("submit");(l=this.state.errorMap)!=null&&l[c]&&n!=="submit"&&!s&&this.baseStore.setState(u=>({...u,errorMap:{...u.errorMap,[c]:void 0}}))}),{hasErrored:s,fieldsErrorMap:i}},this.validateAsync=async n=>{const o=Lt(n,this.options);this.state.isFormValidating||this.baseStore.setState(c=>({...c,isFormValidating:!0}));const s=[];let i;for(const c of o){if(!c.validate)continue;const u=ct(c.cause),d=this.state.validationMetaMap[u];d?.lastAbortController.abort();const f=new AbortController;this.state.validationMetaMap[u]={lastAbortController:f},s.push(new Promise(async m=>{let p;try{p=await new Promise((g,A)=>{setTimeout(async()=>{if(f.signal.aborted)return g(void 0);try{g(await this.runValidator({validate:c.validate,value:{value:this.state.values,formApi:this,validationSource:"form",signal:f.signal},type:"validateAsync"}))}catch(R){A(R)}},c.debounceMs)})}catch(g){p=g}const{formError:v,fieldErrors:h}=pt(p);h&&(i=i?{...i,...h}:h);const y=ct(c.cause);for(const g of Object.keys(this.state.fieldMeta)){const A=this.getFieldMeta(g);if(!A)continue;const{errorMap:R,errorSourceMap:F}=A,P=i?.[g],{newErrorValue:N,newSource:M}=$n({newFormValidatorError:P,isPreviousErrorFromFormValidator:F?.[y]==="form",previousErrorValue:R?.[y]});R?.[y]!==N&&this.setFieldMeta(g,I=>({...I,errorMap:{...I.errorMap,[y]:N},errorSourceMap:{...I.errorSourceMap,[y]:M}}))}this.baseStore.setState(g=>({...g,errorMap:{...g.errorMap,[y]:v}})),m(i?{fieldErrors:i,errorMapKey:y}:void 0)}))}let a=[];const l={};if(s.length){a=await Promise.all(s);for(const c of a)if(c?.fieldErrors){const{errorMapKey:u}=c;for(const[d,f]of Object.entries(c.fieldErrors)){const p={...l[d]||{},[u]:f};l[d]=p}}}return this.baseStore.setState(c=>({...c,isFormValidating:!1})),l},this.validate=n=>{const{hasErrored:o,fieldsErrorMap:s}=this.validateSync(n);return o&&!this.options.asyncAlways?s:this.validateAsync(n)},this.getFieldValue=n=>qe(this.state.values,n),this.getFieldMeta=n=>this.state.fieldMeta[n],this.getFieldInfo=n=>{var o;return(o=this.fieldInfo)[n]||(o[n]={instance:null,validationMetaMap:{onChange:void 0,onBlur:void 0,onSubmit:void 0,onMount:void 0,onServer:void 0}})},this.setFieldMeta=(n,o)=>{this.baseStore.setState(s=>({...s,fieldMetaBase:{...s.fieldMetaBase,[n]:Ct(o,s.fieldMetaBase[n])}}))},this.resetFieldMeta=n=>Object.keys(n).reduce((o,s)=>{const i=s;return o[i]=yt,o},{}),this.setFieldValue=(n,o,s)=>{const i=s?.dontUpdateMeta??!1;de(()=>{i||this.setFieldMeta(n,a=>({...a,isTouched:!0,isDirty:!0,errorMap:{...a?.errorMap,onMount:void 0}})),this.baseStore.setState(a=>({...a,values:st(a.values,n,o)}))})},this.deleteField=n=>{const s=[...Object.keys(this.fieldInfo).filter(i=>{const a=n.toString();return i!==a&&i.startsWith(a)}),n];this.baseStore.setState(i=>{const a={...i};return s.forEach(l=>{a.values=Jo(a.values,l),delete this.fieldInfo[l],delete a.fieldMetaBase[l]}),a})},this.pushFieldValue=(n,o,s)=>{this.setFieldValue(n,i=>[...Array.isArray(i)?i:[],o],s),this.validateField(n,"change")},this.insertFieldValue=async(n,o,s,i)=>{this.setFieldValue(n,a=>[...a.slice(0,o),s,...a.slice(o)],i),await this.validateField(n,"change"),lt(this).handleArrayFieldMetaShift(n,o,"insert"),await this.validateArrayFieldsStartingFrom(n,o,"change")},this.replaceFieldValue=async(n,o,s,i)=>{this.setFieldValue(n,a=>a.map((l,c)=>c===o?s:l),i),await this.validateField(n,"change"),await this.validateArrayFieldsStartingFrom(n,o,"change")},this.removeFieldValue=async(n,o,s)=>{const i=this.getFieldValue(n),a=Array.isArray(i)?Math.max(i.length-1,0):null;if(this.setFieldValue(n,l=>l.filter((c,u)=>u!==o),s),lt(this).handleArrayFieldMetaShift(n,o,"remove"),a!==null){const l=`${n}[${a}]`;this.deleteField(l)}await this.validateField(n,"change"),await this.validateArrayFieldsStartingFrom(n,o,"change")},this.swapFieldValues=(n,o,s,i)=>{this.setFieldValue(n,a=>{const l=a[o],c=a[s];return st(st(a,`${o}`,c),`${s}`,l)},i),lt(this).handleArrayFieldMetaShift(n,o,"swap",s),this.validateField(n,"change"),this.validateField(`${n}[${o}]`,"change"),this.validateField(`${n}[${s}]`,"change")},this.moveFieldValues=(n,o,s,i)=>{this.setFieldValue(n,a=>(a.splice(s,0,a.splice(o,1)[0]),a),i),lt(this).handleArrayFieldMetaShift(n,o,"move",s),this.validateField(n,"change"),this.validateField(`${n}[${o}]`,"change"),this.validateField(`${n}[${s}]`,"change")},this.resetField=n=>{this.baseStore.setState(o=>({...o,fieldMetaBase:{...o.fieldMetaBase,[n]:yt},values:this.options.defaultValues?st(o.values,n,qe(this.options.defaultValues,n)):o.values}))},this.getAllErrors=()=>({form:{errors:this.state.errors,errorMap:this.state.errorMap},fields:Object.entries(this.state.fieldMeta).reduce((n,[o,s])=>(Object.keys(s).length&&s.errors.length&&(n[o]={errors:s.errors,errorMap:s.errorMap}),n),{})}),this.parseValuesWithSchema=n=>Ne.validate({value:this.state.values,validationSource:"form"},n),this.parseValuesWithSchemaAsync=n=>Ne.validateAsync({value:this.state.values,validationSource:"form"},n),this.baseStore=new ro(Rt({...t?.defaultState,values:t?.defaultValues??((r=t?.defaultState)==null?void 0:r.values)})),this.fieldMetaDerived=new zt({deps:[this.baseStore],fn:({prevDepVals:n,currDepVals:o,prevVal:s})=>{var i,a,l;const c=s,u=n?.[0],d=o[0];let f=0;const m={};for(const p of Object.keys(d.fieldMetaBase)){const v=d.fieldMetaBase[p],h=u?.fieldMetaBase[p],y=c?.[p],g=qe(d.values,p);let A=y?.errors;if(!h||v.errorMap!==h.errorMap){A=Object.values(v.errorMap??{}).filter(M=>M!==void 0);const N=(i=this.getFieldInfo(p))==null?void 0:i.instance;N&&!N.options.disableErrorFlat&&(A=A?.flat(1))}const R=!ri(A??[]),F=!v.isDirty,P=je(g,qe(this.options.defaultValues,p))||je(g,(l=(a=this.getFieldInfo(p))==null?void 0:a.instance)==null?void 0:l.options.defaultValue);if(y&&y.isPristine===F&&y.isValid===R&&y.isDefaultValue===P&&y.errors===A&&v===h){m[p]=y,f++;continue}m[p]={...v,errors:A,isPristine:F,isValid:R,isDefaultValue:P}}return Object.keys(d.fieldMetaBase).length&&c&&f===Object.keys(d.fieldMetaBase).length?c:m}}),this.store=new zt({deps:[this.baseStore,this.fieldMetaDerived],fn:({prevDepVals:n,currDepVals:o,prevVal:s})=>{var i,a,l,c;const u=s,d=n?.[0],f=o[0],m=o[1],p=Object.values(m).filter(Boolean),v=p.some(w=>w.isValidating),h=p.every(w=>w.isValid),y=p.some(w=>w.isTouched),g=p.some(w=>w.isBlurred),A=p.every(w=>w.isDefaultValue),R=y&&((i=f.errorMap)==null?void 0:i.onMount),F=p.some(w=>w.isDirty),P=!F,N=!!((a=f.errorMap)!=null&&a.onMount||p.some(w=>{var q;return(q=w?.errorMap)==null?void 0:q.onMount})),M=!!v;let I=u?.errors??[];(!d||f.errorMap!==d.errorMap)&&(I=Object.values(f.errorMap).reduce((w,q)=>q===void 0?w:q&&Wt(q)?(w.push(q.form),w):(w.push(q),w),[]));const S=I.length===0,O=h&&S,$=this.options.canSubmitWhenInvalid??!1,G=f.submissionAttempts===0&&!y&&!N||!M&&!f.isSubmitting&&O||$;let T=f.errorMap;if(R&&(I=I.filter(w=>w!==f.errorMap.onMount),T=Object.assign(T,{onMount:void 0})),u&&d&&u.errorMap===T&&u.fieldMeta===this.fieldMetaDerived.state&&u.errors===I&&u.isFieldsValidating===v&&u.isFieldsValid===h&&u.isFormValid===S&&u.isValid===O&&u.canSubmit===G&&u.isTouched===y&&u.isBlurred===g&&u.isPristine===P&&u.isDefaultValue===A&&u.isDirty===F&&je(d,f))return u;let X={...f,errorMap:T,fieldMeta:this.fieldMetaDerived.state,errors:I,isFieldsValidating:v,isFieldsValid:h,isFormValid:S,isValid:O,canSubmit:G,isTouched:y,isBlurred:g,isPristine:P,isDefaultValue:A,isDirty:F};const U=((l=this.options.transform)==null?void 0:l.deps)??[];if(U.length!==this.prevTransformArray.length||U.some((w,q)=>w!==this.prevTransformArray[q])){const w=Object.assign({},this,{state:X});(c=this.options.transform)==null||c.fn(w),X=w.state,this.prevTransformArray=U}return X}}),this.handleSubmit=this.handleSubmit.bind(this),this.update(t||{})}get state(){return this.store.state}runValidator(t){return xr(t.validate)?Ne[t.type](t.value,t.validate):t.validate(t.value)}async handleSubmit(t){var r,n,o,s,i,a,l,c;if(this.baseStore.setState(d=>({...d,isSubmitted:!1,submissionAttempts:d.submissionAttempts+1,isSubmitSuccessful:!1})),de(()=>{Object.values(this.fieldInfo).forEach(d=>{d.instance&&(d.instance.state.meta.isTouched||d.instance.setMeta(f=>({...f,isTouched:!0})))})}),!this.state.canSubmit)return;this.baseStore.setState(d=>({...d,isSubmitting:!0}));const u=()=>{this.baseStore.setState(d=>({...d,isSubmitting:!1}))};if(await this.validateAllFields("submit"),!this.state.isFieldsValid){u(),(n=(r=this.options).onSubmitInvalid)==null||n.call(r,{value:this.state.values,formApi:this});return}if(await this.validate("submit"),!this.state.isValid){u(),(s=(o=this.options).onSubmitInvalid)==null||s.call(o,{value:this.state.values,formApi:this});return}de(()=>{Object.values(this.fieldInfo).forEach(d=>{var f,m,p;(p=(m=(f=d.instance)==null?void 0:f.options.listeners)==null?void 0:m.onSubmit)==null||p.call(m,{value:d.instance.state.value,fieldApi:d.instance})})}),(a=(i=this.options.listeners)==null?void 0:i.onSubmit)==null||a.call(i,{formApi:this});try{await((c=(l=this.options).onSubmit)==null?void 0:c.call(l,{value:this.state.values,formApi:this,meta:t??this.options.onSubmitMeta})),de(()=>{this.baseStore.setState(d=>({...d,isSubmitted:!0,isSubmitSuccessful:!0})),u()})}catch(d){throw this.baseStore.setState(f=>({...f,isSubmitSuccessful:!1})),u(),d}}setErrorMap(t){de(()=>{Object.entries(t).forEach(([r,n])=>{const o=r;if(Wt(n)){const{formError:s,fieldErrors:i}=pt(n);for(const a of Object.keys(this.fieldInfo))this.getFieldMeta(a)&&this.setFieldMeta(a,c=>({...c,errorMap:{...c.errorMap,[o]:i?.[a]},errorSourceMap:{...c.errorSourceMap,[o]:"form"}}));this.baseStore.setState(a=>({...a,errorMap:{...a.errorMap,[o]:s}}))}else this.baseStore.setState(s=>({...s,errorMap:{...s.errorMap,[o]:n}}))})})}}function pt(e){if(e){if(Wt(e)){const t=pt(e.form).formError,r=e.fields;return{formError:t,fieldErrors:r}}return{formError:e}}return{formError:void 0}}function ct(e){switch(e){case"submit":return"onSubmit";case"blur":return"onBlur";case"mount":return"onMount";case"server":return"onServer";case"change":default:return"onChange"}}class si{constructor(t){this.options={},this.mount=()=>{var r,n;const o=this.store.mount();this.options.defaultValue!==void 0&&this.form.setFieldValue(this.name,this.options.defaultValue,{dontUpdateMeta:!0});const s=this.getInfo();s.instance=this,this.update(this.options);const{onMount:i}=this.options.validators||{};if(i){const a=this.runValidator({validate:i,value:{value:this.state.value,fieldApi:this,validationSource:"field"},type:"validate"});a&&this.setMeta(l=>({...l,errorMap:{...l?.errorMap,onMount:a},errorSourceMap:{...l?.errorSourceMap,onMount:"field"}}))}return(n=(r=this.options.listeners)==null?void 0:r.onMount)==null||n.call(r,{value:this.state.value,fieldApi:this}),o},this.update=r=>{this.options=r;const n=this.name!==r.name;if(this.name=r.name,this.state.value===void 0){const o=qe(r.form.options.defaultValues,r.name),s=r.defaultValue??o;n?this.setValue(i=>i||s,{dontUpdateMeta:!0}):s!==void 0&&this.setValue(s,{dontUpdateMeta:!0})}this.form.getFieldMeta(this.name)===void 0&&this.setMeta(this.state.meta)},this.getValue=()=>this.form.getFieldValue(this.name),this.setValue=(r,n)=>{this.form.setFieldValue(this.name,r,n),this.triggerOnChangeListener(),this.validate("change")},this.getMeta=()=>this.store.state.meta,this.setMeta=r=>this.form.setFieldMeta(this.name,r),this.getInfo=()=>this.form.getFieldInfo(this.name),this.pushValue=(r,n)=>{this.form.pushFieldValue(this.name,r,n),this.triggerOnChangeListener()},this.insertValue=(r,n,o)=>{this.form.insertFieldValue(this.name,r,n,o),this.triggerOnChangeListener()},this.replaceValue=(r,n,o)=>{this.form.replaceFieldValue(this.name,r,n,o),this.triggerOnChangeListener()},this.removeValue=(r,n)=>{this.form.removeFieldValue(this.name,r,n),this.triggerOnChangeListener()},this.swapValues=(r,n,o)=>{this.form.swapFieldValues(this.name,r,n,o),this.triggerOnChangeListener()},this.moveValue=(r,n,o)=>{this.form.moveFieldValues(this.name,r,n,o),this.triggerOnChangeListener()},this.getLinkedFields=r=>{const n=Object.values(this.form.fieldInfo),o=[];for(const s of n){if(!s.instance)continue;const{onChangeListenTo:i,onBlurListenTo:a}=s.instance.options.validators||{};r==="change"&&i?.includes(this.name)&&o.push(s.instance),r==="blur"&&a?.includes(this.name)&&o.push(s.instance)}return o},this.validateSync=(r,n)=>{var o;const s=Ht(r,this.options),a=this.getLinkedFields(r).reduce((u,d)=>{const f=Ht(r,d.options);return f.forEach(m=>{m.field=d}),u.concat(f)},[]);let l=!1;de(()=>{const u=(d,f)=>{var m;const p=ut(f.cause),v=f.validate?Wn(d.runValidator({validate:f.validate,value:{value:d.store.state.value,validationSource:"field",fieldApi:d},type:"validate"})):void 0,h=n[p],{newErrorValue:y,newSource:g}=Ln({formLevelError:h,fieldLevelError:v});((m=d.state.meta.errorMap)==null?void 0:m[p])!==y&&d.setMeta(A=>({...A,errorMap:{...A.errorMap,[p]:y},errorSourceMap:{...A.errorSourceMap,[p]:g}})),y&&(l=!0)};for(const d of s)u(this,d);for(const d of a)d.validate&&u(d.field,d)});const c=ut("submit");return(o=this.state.meta.errorMap)!=null&&o[c]&&r!=="submit"&&!l&&this.setMeta(u=>({...u,errorMap:{...u.errorMap,[c]:void 0},errorSourceMap:{...u.errorSourceMap,[c]:void 0}})),{hasErrored:l}},this.validateAsync=async(r,n)=>{const o=Lt(r,this.options),s=await n,i=this.getLinkedFields(r),a=i.reduce((f,m)=>{const p=Lt(r,m.options);return p.forEach(v=>{v.field=m}),f.concat(p)},[]);this.state.meta.isValidating||this.setMeta(f=>({...f,isValidating:!0}));for(const f of i)f.setMeta(m=>({...m,isValidating:!0}));const l=[],c=[],u=(f,m,p)=>{const v=ut(m.cause),h=f.getInfo().validationMetaMap[v];h?.lastAbortController.abort();const y=new AbortController;this.getInfo().validationMetaMap[v]={lastAbortController:y},p.push(new Promise(async g=>{var A;let R;try{R=await new Promise((I,S)=>{this.timeoutIds.validations[m.cause]&&clearTimeout(this.timeoutIds.validations[m.cause]),this.timeoutIds.validations[m.cause]=setTimeout(async()=>{if(y.signal.aborted)return I(void 0);try{I(await this.runValidator({validate:m.validate,value:{value:f.store.state.value,fieldApi:f,signal:y.signal,validationSource:"field"},type:"validateAsync"}))}catch(O){S(O)}},m.debounceMs)})}catch(I){R=I}if(y.signal.aborted)return g(void 0);const F=Wn(R),P=(A=s[this.name])==null?void 0:A[v],{newErrorValue:N,newSource:M}=Ln({formLevelError:P,fieldLevelError:F});f.setMeta(I=>({...I,errorMap:{...I?.errorMap,[v]:N},errorSourceMap:{...I.errorSourceMap,[v]:M}})),g(N)}))};for(const f of o)f.validate&&u(this,f,l);for(const f of a)f.validate&&u(f.field,f,c);let d=[];(l.length||c.length)&&(d=await Promise.all(l),await Promise.all(c)),this.setMeta(f=>({...f,isValidating:!1}));for(const f of i)f.setMeta(m=>({...m,isValidating:!1}));return d.filter(Boolean)},this.validate=(r,n)=>{var o;if(!this.state.meta.isTouched)return[];const{fieldsErrorMap:s}=n?.skipFormValidation?{fieldsErrorMap:{}}:this.form.validateSync(r),{hasErrored:i}=this.validateSync(r,s[this.name]??{});if(i&&!this.options.asyncAlways)return(o=this.getInfo().validationMetaMap[ut(r)])==null||o.lastAbortController.abort(),this.state.meta.errors;const a=n?.skipFormValidation?Promise.resolve({}):this.form.validateAsync(r);return this.validateAsync(r,a)},this.handleChange=r=>{this.setValue(r)},this.handleBlur=()=>{this.state.meta.isTouched||(this.setMeta(n=>({...n,isTouched:!0})),this.validate("change")),this.state.meta.isBlurred||this.setMeta(n=>({...n,isBlurred:!0})),this.validate("blur"),this.triggerOnBlurListener()},this.parseValueWithSchema=r=>Ne.validate({value:this.state.value,validationSource:"field"},r),this.parseValueWithSchemaAsync=r=>Ne.validateAsync({value:this.state.value,validationSource:"field"},r),this.form=t.form,this.name=t.name,this.timeoutIds={validations:{},listeners:{},formListeners:{}},this.store=new zt({deps:[this.form.store],fn:()=>{const r=this.form.getFieldValue(this.name),n=this.form.getFieldMeta(this.name)??{...yt,...t.defaultMeta};return{value:r,meta:n}}}),this.options=t}get state(){return this.store.state}runValidator(t){return xr(t.validate)?Ne[t.type](t.value,t.validate):t.validate(t.value)}setErrorMap(t){this.setMeta(r=>({...r,errorMap:{...r.errorMap,...t}}))}triggerOnBlurListener(){var t,r,n,o,s,i;const a=(t=this.form.options.listeners)==null?void 0:t.onBlurDebounceMs;a&&a>0?(this.timeoutIds.formListeners.blur&&clearTimeout(this.timeoutIds.formListeners.blur),this.timeoutIds.formListeners.blur=setTimeout(()=>{var c,u;(u=(c=this.form.options.listeners)==null?void 0:c.onBlur)==null||u.call(c,{formApi:this.form,fieldApi:this})},a)):(n=(r=this.form.options.listeners)==null?void 0:r.onBlur)==null||n.call(r,{formApi:this.form,fieldApi:this});const l=(o=this.options.listeners)==null?void 0:o.onBlurDebounceMs;l&&l>0?(this.timeoutIds.listeners.blur&&clearTimeout(this.timeoutIds.listeners.blur),this.timeoutIds.listeners.blur=setTimeout(()=>{var c,u;(u=(c=this.options.listeners)==null?void 0:c.onBlur)==null||u.call(c,{value:this.state.value,fieldApi:this})},l)):(i=(s=this.options.listeners)==null?void 0:s.onBlur)==null||i.call(s,{value:this.state.value,fieldApi:this})}triggerOnChangeListener(){var t,r,n,o,s,i;const a=(t=this.form.options.listeners)==null?void 0:t.onChangeDebounceMs;a&&a>0?(this.timeoutIds.formListeners.blur&&clearTimeout(this.timeoutIds.formListeners.blur),this.timeoutIds.formListeners.blur=setTimeout(()=>{var c,u;(u=(c=this.form.options.listeners)==null?void 0:c.onChange)==null||u.call(c,{formApi:this.form,fieldApi:this})},a)):(n=(r=this.form.options.listeners)==null?void 0:r.onChange)==null||n.call(r,{formApi:this.form,fieldApi:this});const l=(o=this.options.listeners)==null?void 0:o.onChangeDebounceMs;l&&l>0?(this.timeoutIds.listeners.change&&clearTimeout(this.timeoutIds.listeners.change),this.timeoutIds.listeners.change=setTimeout(()=>{var c,u;(u=(c=this.options.listeners)==null?void 0:c.onChange)==null||u.call(c,{value:this.state.value,fieldApi:this})},l)):(i=(s=this.options.listeners)==null?void 0:s.onChange)==null||i.call(s,{value:this.state.value,fieldApi:this})}}function Wn(e){if(e)return e}function ut(e){switch(e){case"submit":return"onSubmit";case"blur":return"onBlur";case"mount":return"onMount";case"server":return"onServer";case"change":default:return"onChange"}}const wt=typeof window<"u"?b.useLayoutEffect:b.useEffect;function ai(e){const[t]=b.useState(()=>{const n=new si({...e,form:e.form,name:e.name});return n.Field=Sr,n});return wt(t.mount,[t]),wt(()=>{t.update(e)}),Zt(t.store,e.mode==="array"?r=>[r.meta,Object.keys(r.value??[]).length]:void 0),t}const Sr=({children:e,...t})=>{const r=ai(t),n=b.useMemo(()=>Ct(e,r),[e,r,r.state.value,r.state.meta]);return x.jsx(x.Fragment,{children:n})};function li({form:e,selector:t,children:r}){const n=Zt(e.store,t);return Ct(r,n)}function ci(e){const[t]=b.useState(()=>{const r=new ii(e),n=r;return n.Field=function(s){return x.jsx(Sr,{...s,form:r})},n.Subscribe=o=>x.jsx(li,{form:r,selector:o.selector,children:o.children}),n});return wt(t.mount,[]),Zt(t.store,r=>r.isSubmitting),wt(()=>{t.update(e)}),t}function ui(){const e=b.createContext(null);function t(){const o=b.useContext(e);if(!o)throw new Error("`fieldContext` only works when within a `fieldComponent` passed to `createFormHook`");return o}const r=b.createContext(null);function n(){const o=b.useContext(r);if(!o)throw new Error("`formContext` only works when within a `formComponent` passed to `createFormHook`");return o}return{fieldContext:e,useFieldContext:t,useFormContext:n,formContext:r}}function di({fieldComponents:e,fieldContext:t,formContext:r,formComponents:n}){function o(i){const a=ci(i),l=b.useMemo(()=>({children:d})=>x.jsx(r.Provider,{value:a,children:d}),[a]),c=b.useMemo(()=>({children:d,...f})=>x.jsx(a.Field,{...f,children:m=>x.jsx(t.Provider,{value:m,children:d(Object.assign(m,e))})}),[a]);return b.useMemo(()=>Object.assign(a,{AppField:c,AppForm:l,...n}),[a,c,l])}function s({render:i,props:a}){return l=>i({...a,...l})}return{useAppForm:o,withForm:s}}function _e(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},W.apply(null,arguments)}var Dt={exports:{}},jt,Gn;function fi(){if(Gn)return jt;Gn=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return jt=e,jt}var Pt,Un;function hi(){if(Un)return Pt;Un=1;var e=fi();function t(){}function r(){}return r.resetWarningCache=t,Pt=function(){function n(i,a,l,c,u,d){if(d!==e){var f=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw f.name="Invariant Violation",f}}n.isRequired=n;function o(){return n}var s={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:o,element:n,elementType:n,instanceOf:o,node:n,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:r,resetWarningCache:t};return s.PropTypes=s,s},Pt}var qn;function pi(){return qn||(qn=1,Dt.exports=hi()()),Dt.exports}var mi=pi();const D=oo(mi);var Tt={exports:{}},L={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yn;function vi(){if(Yn)return L;Yn=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),i=Symbol.for("react.context"),a=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),u=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen"),p;p=Symbol.for("react.module.reference");function v(h){if(typeof h=="object"&&h!==null){var y=h.$$typeof;switch(y){case e:switch(h=h.type,h){case r:case o:case n:case c:case u:return h;default:switch(h=h&&h.$$typeof,h){case a:case i:case l:case f:case d:case s:return h;default:return y}}case t:return y}}}return L.ContextConsumer=i,L.ContextProvider=s,L.Element=e,L.ForwardRef=l,L.Fragment=r,L.Lazy=f,L.Memo=d,L.Portal=t,L.Profiler=o,L.StrictMode=n,L.Suspense=c,L.SuspenseList=u,L.isAsyncMode=function(){return!1},L.isConcurrentMode=function(){return!1},L.isContextConsumer=function(h){return v(h)===i},L.isContextProvider=function(h){return v(h)===s},L.isElement=function(h){return typeof h=="object"&&h!==null&&h.$$typeof===e},L.isForwardRef=function(h){return v(h)===l},L.isFragment=function(h){return v(h)===r},L.isLazy=function(h){return v(h)===f},L.isMemo=function(h){return v(h)===d},L.isPortal=function(h){return v(h)===t},L.isProfiler=function(h){return v(h)===o},L.isStrictMode=function(h){return v(h)===n},L.isSuspense=function(h){return v(h)===c},L.isSuspenseList=function(h){return v(h)===u},L.isValidElementType=function(h){return typeof h=="string"||typeof h=="function"||h===r||h===o||h===n||h===c||h===u||h===m||typeof h=="object"&&h!==null&&(h.$$typeof===f||h.$$typeof===d||h.$$typeof===s||h.$$typeof===i||h.$$typeof===l||h.$$typeof===p||h.getModuleId!==void 0)},L.typeOf=v,L}var Xn;function gi(){return Xn||(Xn=1,Tt.exports=vi()),Tt.exports}gi();const Jn=e=>typeof e=="object"&&e!=null&&e.nodeType===1,Qn=(e,t)=>(!t||e!=="hidden")&&e!=="visible"&&e!=="clip",dt=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const r=getComputedStyle(e,null);return Qn(r.overflowY,t)||Qn(r.overflowX,t)||(n=>{const o=(s=>{if(!s.ownerDocument||!s.ownerDocument.defaultView)return null;try{return s.ownerDocument.defaultView.frameElement}catch{return null}})(n);return!!o&&(o.clientHeight<n.scrollHeight||o.clientWidth<n.scrollWidth)})(e)}return!1},ft=(e,t,r,n,o,s,i,a)=>s<e&&i>t||s>e&&i<t?0:s<=e&&a<=r||i>=t&&a>=r?s-e-n:i>t&&a<r||s<e&&a>r?i-t+o:0,bi=e=>{const t=e.parentElement;return t??(e.getRootNode().host||null)},yi=(e,t)=>{var r,n,o,s;if(typeof document>"u")return[];const{inline:i,boundary:a,skipOverflowHiddenElements:l}=t,c=typeof a=="function"?a:G=>G!==a;if(!Jn(e))throw new TypeError("Invalid target");const u=document.scrollingElement||document.documentElement,d=[];let f=e;for(;Jn(f)&&c(f);){if(f=bi(f),f===u){d.push(f);break}f!=null&&f===document.body&&dt(f)&&!dt(document.documentElement)||f!=null&&dt(f,l)&&d.push(f)}const m=(n=(r=window.visualViewport)==null?void 0:r.width)!=null?n:innerWidth,p=(s=(o=window.visualViewport)==null?void 0:o.height)!=null?s:innerHeight,{scrollX:v,scrollY:h}=window,{height:y,width:g,top:A,right:R,bottom:F,left:P}=e.getBoundingClientRect(),{top:N,right:M,left:I}=(G=>{const T=window.getComputedStyle(G);return{top:parseFloat(T.scrollMarginTop)||0,right:parseFloat(T.scrollMarginRight)||0,bottom:parseFloat(T.scrollMarginBottom)||0,left:parseFloat(T.scrollMarginLeft)||0}})(e);let S=A-N,O=i==="center"?P+g/2-I+M:i==="end"?R+M:P-I;const $=[];for(let G=0;G<d.length;G++){const T=d[G],{height:X,width:U,top:ee,right:w,bottom:q,left:ie}=T.getBoundingClientRect();if(A>=0&&P>=0&&F<=p&&R<=m&&(T===u&&!dt(T)||A>=ee&&F<=q&&P>=ie&&R<=w))return $;const se=getComputedStyle(T),j=parseInt(se.borderLeftWidth,10),k=parseInt(se.borderTopWidth,10),V=parseInt(se.borderRightWidth,10),_=parseInt(se.borderBottomWidth,10);let H=0,z=0;const J="offsetWidth"in T?T.offsetWidth-T.clientWidth-j-V:0,te="offsetHeight"in T?T.offsetHeight-T.clientHeight-k-_:0,Z="offsetWidth"in T?T.offsetWidth===0?0:U/T.offsetWidth:0,ne="offsetHeight"in T?T.offsetHeight===0?0:X/T.offsetHeight:0;if(u===T)H=ft(h,h+p,p,k,_,h+S,h+S+y,y),z=i==="start"?O:i==="center"?O-m/2:i==="end"?O-m:ft(v,v+m,m,j,V,v+O,v+O+g,g),H=Math.max(0,H+h),z=Math.max(0,z+v);else{H=ft(ee,q,X,k,_+te,S,S+y,y),z=i==="start"?O-ie-j:i==="center"?O-(ie+U/2)+J/2:i==="end"?O-w+V+J:ft(ie,w,U,j,V+J,O,O+g,g);const{scrollLeft:re,scrollTop:Oe}=T;H=ne===0?0:Math.max(0,Math.min(Oe+H/ne,T.scrollHeight-X/ne+te)),z=Z===0?0:Math.max(0,Math.min(re+z/Z,T.scrollWidth-U/Z+J)),S+=Oe-H,O+=re-z}$.push({el:T,top:H,left:z})}return $};var $e=function(){return $e=Object.assign||function(t){for(var r,n=1,o=arguments.length;n<o;n++){r=arguments[n];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(t[s]=r[s])}return t},$e.apply(this,arguments)};var wi=0;function kr(){}function Mi(e,t){if(e){var r=yi(e,{boundary:t});r.forEach(function(n){var o=n.el,s=n.top,i=n.left;o.scrollTop=s,o.scrollLeft=i})}}function Zn(e,t,r){var n=e===t||t instanceof r.Node&&e.contains&&e.contains(t);return n}function Cr(e,t){var r;function n(){r&&clearTimeout(r)}function o(){for(var s=arguments.length,i=new Array(s),a=0;a<s;a++)i[a]=arguments[a];n(),r=setTimeout(function(){r=null,e.apply(void 0,i)},t)}return o.cancel=n,o}function ae(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(n){for(var o=arguments.length,s=new Array(o>1?o-1:0),i=1;i<o;i++)s[i-1]=arguments[i];return t.some(function(a){return a&&a.apply(void 0,[n].concat(s)),n.preventDownshiftDefault||n.hasOwnProperty("nativeEvent")&&n.nativeEvent.preventDownshiftDefault})}}function ze(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(n){t.forEach(function(o){typeof o=="function"?o(n):o&&(o.current=n)})}}function Ii(){return String(wi++)}function Mt(e,t){return!e||!t?e:Object.keys(e).reduce(function(r,n){return r[n]=Er(t,n)?t[n]:e[n],r},{})}function Er(e,t){return e[t]!==void 0}function Gt(e){var t=e.key,r=e.keyCode;return r>=37&&r<=40&&t.indexOf("Arrow")!==0?"Arrow"+t:t}function ht(e,t,r,n,o){var s=r.length;if(s===0)return-1;var i=s-1;(typeof e!="number"||e<0||e>i)&&(e=t>0?-1:i+1);var a=e+t;a<0?a=i:a>i&&(a=0);var l=It(a,t<0,r,n,o);return l===-1?e>=s?-1:e:l}function It(e,t,r,n,o){o===void 0&&(o=!1);var s=r.length;if(t){for(var i=e;i>=0;i--)if(!n(r[i],i))return i}else for(var a=e;a<s;a++)if(!n(r[a],a))return a;return o?It(t?s-1:0,t,r,n):-1}function er(e,t,r,n){return n===void 0&&(n=!0),r&&t.some(function(o){return o&&(Zn(o,e,r)||n&&Zn(o,r.document.activeElement,r))})}var xi=Cr(function(e){Fr(e).textContent=""},500);function Fr(e){var t=e.getElementById("a11y-status-message");return t||(t=e.createElement("div"),t.setAttribute("id","a11y-status-message"),t.setAttribute("role","status"),t.setAttribute("aria-live","polite"),t.setAttribute("aria-relevant","additions text"),Object.assign(t.style,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px"}),e.body.appendChild(t),t)}function Si(e,t){if(!(!e||!t)){var r=Fr(t);r.textContent=e,xi(t)}}function ki(e){var t=e?.getElementById("a11y-status-message");t&&t.remove()}var Vr={highlightedIndex:-1,isOpen:!1,selectedItem:null,inputValue:""};function Ci(e,t,r){var n=e.props,o=e.type,s={};Object.keys(t).forEach(function(i){Ei(i,e,t,r),r[i]!==t[i]&&(s[i]=r[i])}),n.onStateChange&&Object.keys(s).length&&n.onStateChange(W({type:o},s))}function Ei(e,t,r,n){var o=t.props,s=t.type,i="on"+nn(e)+"Change";o[i]&&n[e]!==void 0&&n[e]!==r[e]&&o[i](W({type:s},n))}function Fi(e,t){return t.changes}var tr=Cr(function(e,t){Si(e,t)},200),Vi=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?b.useLayoutEffect:b.useEffect,Oi="useId"in bt?function(t){var r=t.id,n=t.labelId,o=t.menuId,s=t.getItemId,i=t.toggleButtonId,a=t.inputId,l="downshift-"+bt.useId();r||(r=l);var c=b.useRef({labelId:n||r+"-label",menuId:o||r+"-menu",getItemId:s||function(u){return r+"-item-"+u},toggleButtonId:i||r+"-toggle-button",inputId:a||r+"-input"});return c.current}:function(t){var r=t.id,n=r===void 0?"downshift-"+Ii():r,o=t.labelId,s=t.menuId,i=t.getItemId,a=t.toggleButtonId,l=t.inputId,c=b.useRef({labelId:o||n+"-label",menuId:s||n+"-menu",getItemId:i||function(u){return n+"-item-"+u},toggleButtonId:a||n+"-toggle-button",inputId:l||n+"-input"});return c.current};function Or(e,t,r,n){var o,s;if(e===void 0){if(t===void 0)throw new Error(n);o=r[t],s=t}else s=t===void 0?r.indexOf(e):t,o=e;return[o,s]}function nn(e){return""+e.slice(0,1).toUpperCase()+e.slice(1)}function rn(e){var t=b.useRef(e);return t.current=e,t}function Ar(e,t,r,n){var o=b.useRef(),s=b.useRef(),i=b.useCallback(function(m,p){s.current=p,m=Mt(m,p.props);var v=e(m,p),h=p.props.stateReducer(m,W({},p,{changes:v}));return h},[e]),a=b.useReducer(i,t,r),l=a[0],c=a[1],u=rn(t),d=b.useCallback(function(m){return c(W({props:u.current},m))},[u]),f=s.current;return b.useEffect(function(){var m=Mt(o.current,f?.props),p=f&&o.current&&!n(m,l);p&&Ci(f,m,l),o.current=l},[l,f,n]),[l,d]}function Ai(e,t,r,n){var o=Ar(e,t,r,n),s=o[0],i=o[1];return[Mt(s,t),i]}var Xe={itemToString:function(t){return t?String(t):""},itemToKey:function(t){return t},stateReducer:Fi,scrollIntoView:Mi,environment:typeof window>"u"?void 0:window};function ve(e,t,r){r===void 0&&(r=Vr);var n=e["default"+nn(t)];return n!==void 0?n:r[t]}function Be(e,t,r){r===void 0&&(r=Vr);var n=e[t];if(n!==void 0)return n;var o=e["initial"+nn(t)];return o!==void 0?o:ve(e,t,r)}function Ri(e){var t=Be(e,"selectedItem"),r=Be(e,"isOpen"),n=Ti(e),o=Be(e,"inputValue");return{highlightedIndex:n<0&&t&&r?e.items.findIndex(function(s){return e.itemToKey(s)===e.itemToKey(t)}):n,isOpen:r,selectedItem:t,inputValue:o}}function Je(e,t,r){var n=e.items,o=e.initialHighlightedIndex,s=e.defaultHighlightedIndex,i=e.isItemDisabled,a=e.itemToKey,l=t.selectedItem,c=t.highlightedIndex;return n.length===0?-1:o!==void 0&&c===o&&!i(n[o],o)?o:s!==void 0&&!i(n[s],s)?s:l?n.findIndex(function(u){return a(l)===a(u)}):r<0&&!i(n[n.length-1],n.length-1)?n.length-1:r>0&&!i(n[0],0)?0:-1}function Di(e,t,r){var n=b.useRef({isMouseDown:!1,isTouchMove:!1,isTouchEnd:!1});return b.useEffect(function(){if(!e)return kr;var o=r.map(function(u){return u.current});function s(){n.current.isTouchEnd=!1,n.current.isMouseDown=!0}function i(u){n.current.isMouseDown=!1,er(u.target,o,e)||t()}function a(){n.current.isTouchEnd=!1,n.current.isTouchMove=!1}function l(){n.current.isTouchMove=!0}function c(u){n.current.isTouchEnd=!0,!n.current.isTouchMove&&!er(u.target,o,e,!1)&&t()}return e.addEventListener("mousedown",s),e.addEventListener("mouseup",i),e.addEventListener("touchstart",a),e.addEventListener("touchmove",l),e.addEventListener("touchend",c),function(){e.removeEventListener("mousedown",s),e.removeEventListener("mouseup",i),e.removeEventListener("touchstart",a),e.removeEventListener("touchmove",l),e.removeEventListener("touchend",c)}},[r,e,t]),n.current}var Rr=function(){return kr};function Dr(e,t,r,n){n===void 0&&(n={});var o=n.document,s=Et();b.useEffect(function(){if(!(!e||s||!o)){var i=e(t);tr(i,o)}},r),b.useEffect(function(){return function(){tr.cancel(),ki(o)}},[o])}function ji(e){var t=e.highlightedIndex,r=e.isOpen,n=e.itemRefs,o=e.getItemNodeFromIndex,s=e.menuElement,i=e.scrollIntoView,a=b.useRef(!0);return Vi(function(){t<0||!r||!Object.keys(n.current).length||(a.current===!1?a.current=!0:i(o(t),s))},[t]),a}function nr(e,t,r){var n;r===void 0&&(r=!0);var o=((n=e.items)==null?void 0:n.length)&&t>=0;return W({isOpen:!1,highlightedIndex:-1},o&&W({selectedItem:e.items[t],isOpen:ve(e,"isOpen"),highlightedIndex:ve(e,"highlightedIndex")},r&&{inputValue:e.itemToString(e.items[t])}))}function Pi(e,t){return e.isOpen===t.isOpen&&e.inputValue===t.inputValue&&e.highlightedIndex===t.highlightedIndex&&e.selectedItem===t.selectedItem}function Et(){var e=bt.useRef(!0);return bt.useEffect(function(){return e.current=!1,function(){e.current=!0}},[]),e.current}function Ut(e){var t=ve(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}function Ti(e){var t=Be(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}var mt={environment:D.shape({addEventListener:D.func.isRequired,removeEventListener:D.func.isRequired,document:D.shape({createElement:D.func.isRequired,getElementById:D.func.isRequired,activeElement:D.any.isRequired,body:D.any.isRequired}).isRequired,Node:D.func.isRequired}),itemToString:D.func,itemToKey:D.func,stateReducer:D.func},jr=W({},mt,{getA11yStatusMessage:D.func,highlightedIndex:D.number,defaultHighlightedIndex:D.number,initialHighlightedIndex:D.number,isOpen:D.bool,defaultIsOpen:D.bool,initialIsOpen:D.bool,selectedItem:D.any,initialSelectedItem:D.any,defaultSelectedItem:D.any,id:D.string,labelId:D.string,menuId:D.string,getItemId:D.func,toggleButtonId:D.string,onSelectedItemChange:D.func,onHighlightedIndexChange:D.func,onStateChange:D.func,onIsOpenChange:D.func,scrollIntoView:D.func});function _i(e,t,r){var n=t.type,o=t.props,s;switch(n){case r.ItemMouseMove:s={highlightedIndex:t.disabled?-1:t.index};break;case r.MenuMouseLeave:s={highlightedIndex:-1};break;case r.ToggleButtonClick:case r.FunctionToggleMenu:s={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:Je(o,e,0)};break;case r.FunctionOpenMenu:s={isOpen:!0,highlightedIndex:Je(o,e,0)};break;case r.FunctionCloseMenu:s={isOpen:!1};break;case r.FunctionSetHighlightedIndex:s={highlightedIndex:o.isItemDisabled(o.items[t.highlightedIndex],t.highlightedIndex)?-1:t.highlightedIndex};break;case r.FunctionSetInputValue:s={inputValue:t.inputValue};break;case r.FunctionReset:s={highlightedIndex:Ut(o),isOpen:ve(o,"isOpen"),selectedItem:ve(o,"selectedItem"),inputValue:ve(o,"inputValue")};break;default:throw new Error("Reducer called without proper action type.")}return W({},e,s)}$e($e({},jr),{items:D.array.isRequired,isItemDisabled:D.func});$e($e({},Xe),{isItemDisabled:function(){return!1}});var on=0,sn=1,an=2,ln=3,cn=4,un=5,dn=6,fn=7,hn=8,xt=9,pn=10,Pr=11,Tr=12,mn=13,_r=14,zr=15,Kr=16,Nr=17,Br=18,vn=19,$r=20,Lr=21,gn=22,Hr=Object.freeze({__proto__:null,ControlledPropUpdatedSelectedItem:gn,FunctionCloseMenu:Nr,FunctionOpenMenu:Kr,FunctionReset:Lr,FunctionSelectItem:vn,FunctionSetHighlightedIndex:Br,FunctionSetInputValue:$r,FunctionToggleMenu:zr,InputBlur:xt,InputChange:hn,InputClick:pn,InputKeyDownArrowDown:on,InputKeyDownArrowUp:sn,InputKeyDownEnd:cn,InputKeyDownEnter:fn,InputKeyDownEscape:an,InputKeyDownHome:ln,InputKeyDownPageDown:dn,InputKeyDownPageUp:un,ItemClick:mn,ItemMouseMove:Tr,MenuMouseLeave:Pr,ToggleButtonClick:_r});function zi(e){var t=Ri(e),r=t.selectedItem,n=t.inputValue;return n===""&&r&&e.defaultInputValue===void 0&&e.initialInputValue===void 0&&e.inputValue===void 0&&(n=e.itemToString(r)),W({},t,{inputValue:n})}W({},jr,{items:D.array.isRequired,isItemDisabled:D.func,inputValue:D.string,defaultInputValue:D.string,initialInputValue:D.string,inputId:D.string,onInputValueChange:D.func});function Ki(e,t,r,n){var o=b.useRef(),s=Ar(e,t,r,n),i=s[0],a=s[1],l=Et();return b.useEffect(function(){if(Er(t,"selectedItem")){if(!l){var c=t.itemToKey(t.selectedItem)!==t.itemToKey(o.current);c&&a({type:gn,inputValue:t.itemToString(t.selectedItem)})}o.current=i.selectedItem===o.current?t.selectedItem:i.selectedItem}},[i.selectedItem,t.selectedItem]),[Mt(i,t),a]}var Ni=W({},Xe,{isItemDisabled:function(){return!1}});function Bi(e,t){var r,n=t.type,o=t.props,s=t.altKey,i;switch(n){case mn:i={isOpen:ve(o,"isOpen"),highlightedIndex:Ut(o),selectedItem:o.items[t.index],inputValue:o.itemToString(o.items[t.index])};break;case on:e.isOpen?i={highlightedIndex:ht(e.highlightedIndex,1,o.items,o.isItemDisabled,!0)}:i={highlightedIndex:s&&e.selectedItem==null?-1:Je(o,e,1),isOpen:o.items.length>=0};break;case sn:e.isOpen?s?i=nr(o,e.highlightedIndex):i={highlightedIndex:ht(e.highlightedIndex,-1,o.items,o.isItemDisabled,!0)}:i={highlightedIndex:Je(o,e,-1),isOpen:o.items.length>=0};break;case fn:i=nr(o,e.highlightedIndex);break;case an:i=W({isOpen:!1,highlightedIndex:-1},!e.isOpen&&{selectedItem:null,inputValue:""});break;case un:i={highlightedIndex:ht(e.highlightedIndex,-10,o.items,o.isItemDisabled,!0)};break;case dn:i={highlightedIndex:ht(e.highlightedIndex,10,o.items,o.isItemDisabled,!0)};break;case ln:i={highlightedIndex:It(0,!1,o.items,o.isItemDisabled)};break;case cn:i={highlightedIndex:It(o.items.length-1,!0,o.items,o.isItemDisabled)};break;case xt:i=W({isOpen:!1,highlightedIndex:-1},e.highlightedIndex>=0&&((r=o.items)==null?void 0:r.length)&&t.selectItem&&{selectedItem:o.items[e.highlightedIndex],inputValue:o.itemToString(o.items[e.highlightedIndex])});break;case hn:i={isOpen:!0,highlightedIndex:Ut(o),inputValue:t.inputValue};break;case pn:i={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:Je(o,e,0)};break;case vn:i={selectedItem:t.selectedItem,inputValue:o.itemToString(t.selectedItem)};break;case gn:i={inputValue:t.inputValue};break;default:return _i(e,t,Hr)}return W({},e,i)}var $i=["onMouseLeave","refKey","ref"],Li=["item","index","refKey","ref","onMouseMove","onMouseDown","onClick","onPress","disabled"],Hi=["onClick","onPress","refKey","ref"],Wi=["onKeyDown","onChange","onInput","onBlur","onChangeText","onClick","refKey","ref"];Ie.stateChangeTypes=Hr;function Ie(e){e===void 0&&(e={});var t=W({},Ni,e),r=t.items,n=t.scrollIntoView,o=t.environment,s=t.getA11yStatusMessage,i=Ki(Bi,t,zi,Pi),a=i[0],l=i[1],c=a.isOpen,u=a.highlightedIndex,d=a.selectedItem,f=a.inputValue,m=b.useRef(null),p=b.useRef({}),v=b.useRef(null),h=b.useRef(null),y=Et(),g=Oi(t),A=b.useRef(),R=rn({state:a,props:t}),F=b.useCallback(function(j){return p.current[g.getItemId(j)]},[g]);Dr(s,a,[c,u,d,f],o);var P=ji({menuElement:m.current,highlightedIndex:u,isOpen:c,itemRefs:p,scrollIntoView:n,getItemNodeFromIndex:F});b.useEffect(function(){var j=Be(t,"isOpen");j&&v.current&&v.current.focus()},[]),b.useEffect(function(){y||(A.current=r.length)});var N=Di(o,b.useCallback(function(){R.current.state.isOpen&&l({type:xt,selectItem:!1})},[l,R]),b.useMemo(function(){return[m,h,v]},[m.current,h.current,v.current])),M=Rr();b.useEffect(function(){c||(p.current={})},[c]),b.useEffect(function(){var j;!c||!(o!=null&&o.document)||!(v!=null&&(j=v.current)!=null&&j.focus)||o.document.activeElement!==v.current&&v.current.focus()},[c,o]);var I=b.useMemo(function(){return{ArrowDown:function(k){k.preventDefault(),l({type:on,altKey:k.altKey})},ArrowUp:function(k){k.preventDefault(),l({type:sn,altKey:k.altKey})},Home:function(k){R.current.state.isOpen&&(k.preventDefault(),l({type:ln}))},End:function(k){R.current.state.isOpen&&(k.preventDefault(),l({type:cn}))},Escape:function(k){var V=R.current.state;(V.isOpen||V.inputValue||V.selectedItem||V.highlightedIndex>-1)&&(k.preventDefault(),l({type:an}))},Enter:function(k){var V=R.current.state;!V.isOpen||k.which===229||(k.preventDefault(),l({type:fn}))},PageUp:function(k){R.current.state.isOpen&&(k.preventDefault(),l({type:un}))},PageDown:function(k){R.current.state.isOpen&&(k.preventDefault(),l({type:dn}))}}},[l,R]),S=b.useCallback(function(j){return W({id:g.labelId,htmlFor:g.inputId},j)},[g]),O=b.useCallback(function(j,k){var V,_=j===void 0?{}:j,H=_.onMouseLeave,z=_.refKey,J=z===void 0?"ref":z,te=_.ref,Z=_e(_,$i),ne=k===void 0?{}:k;return ne.suppressRefError,W((V={},V[J]=ze(te,function(re){m.current=re}),V.id=g.menuId,V.role="listbox",V["aria-labelledby"]=Z&&Z["aria-label"]?void 0:""+g.labelId,V.onMouseLeave=ae(H,function(){l({type:Pr})}),V),Z)},[l,M,g]),$=b.useCallback(function(j){var k,V,_=j===void 0?{}:j,H=_.item,z=_.index,J=_.refKey,te=J===void 0?"ref":J,Z=_.ref,ne=_.onMouseMove,re=_.onMouseDown,Oe=_.onClick;_.onPress;var Ae=_.disabled,Ft=_e(_,Li);Ae!==void 0&&console.warn('Passing "disabled" as an argument to getItemProps is not supported anymore. Please use the isItemDisabled prop from useCombobox.');var he=R.current,tt=he.props,nt=he.state,rt=Or(H,z,tt.items,"Pass either item or index to getItemProps!"),Vt=rt[0],pe=rt[1],Re=tt.isItemDisabled(Vt,pe),ye="onClick",xe=Oe,we=function(){N.isTouchEnd||pe===nt.highlightedIndex||(P.current=!1,l({type:Tr,index:pe,disabled:Re}))},Se=function(){l({type:mn,index:pe})},eo=function(to){return to.preventDefault()};return W((k={},k[te]=ze(Z,function(Ge){Ge&&(p.current[g.getItemId(pe)]=Ge)}),k["aria-disabled"]=Re,k["aria-selected"]=pe===nt.highlightedIndex,k.id=g.getItemId(pe),k.role="option",k),!Re&&(V={},V[ye]=ae(xe,Se),V),{onMouseMove:ae(ne,we),onMouseDown:ae(re,eo)},Ft)},[l,g,R,N,P]),G=b.useCallback(function(j){var k,V=j===void 0?{}:j,_=V.onClick;V.onPress;var H=V.refKey,z=H===void 0?"ref":H,J=V.ref,te=_e(V,Hi),Z=R.current.state,ne=function(){l({type:_r})};return W((k={},k[z]=ze(J,function(re){h.current=re}),k["aria-controls"]=g.menuId,k["aria-expanded"]=Z.isOpen,k.id=g.toggleButtonId,k.tabIndex=-1,k),!te.disabled&&W({},{onClick:ae(_,ne)}),te)},[l,R,g]),T=b.useCallback(function(j,k){var V,_=j===void 0?{}:j,H=_.onKeyDown,z=_.onChange,J=_.onInput,te=_.onBlur;_.onChangeText;var Z=_.onClick,ne=_.refKey,re=ne===void 0?"ref":ne,Oe=_.ref,Ae=_e(_,Wi),Ft=k===void 0?{}:k;Ft.suppressRefError;var he=R.current.state,tt=function(we){var Se=Gt(we);Se&&I[Se]&&I[Se](we)},nt=function(we){l({type:hn,inputValue:we.target.value})},rt=function(we){if(o!=null&&o.document&&he.isOpen&&!N.isMouseDown){var Se=we.relatedTarget===null&&o.document.activeElement!==o.document.body;l({type:xt,selectItem:!Se})}},Vt=function(){l({type:pn})},pe="onChange",Re={};if(!Ae.disabled){var ye;Re=(ye={},ye[pe]=ae(z,J,nt),ye.onKeyDown=ae(H,tt),ye.onBlur=ae(te,rt),ye.onClick=ae(Z,Vt),ye)}return W((V={},V[re]=ze(Oe,function(xe){v.current=xe}),V["aria-activedescendant"]=he.isOpen&&he.highlightedIndex>-1?g.getItemId(he.highlightedIndex):"",V["aria-autocomplete"]="list",V["aria-controls"]=g.menuId,V["aria-expanded"]=he.isOpen,V["aria-labelledby"]=Ae&&Ae["aria-label"]?void 0:g.labelId,V.autoComplete="off",V.id=g.inputId,V.role="combobox",V.value=he.inputValue,V),Re,Ae)},[l,g,o,I,R,N,M]),X=b.useCallback(function(){l({type:zr})},[l]),U=b.useCallback(function(){l({type:Nr})},[l]),ee=b.useCallback(function(){l({type:Kr})},[l]),w=b.useCallback(function(j){l({type:Br,highlightedIndex:j})},[l]),q=b.useCallback(function(j){l({type:vn,selectedItem:j})},[l]),ie=b.useCallback(function(j){l({type:$r,inputValue:j})},[l]),se=b.useCallback(function(){l({type:Lr})},[l]);return{getItemProps:$,getLabelProps:S,getMenuProps:O,getInputProps:T,getToggleButtonProps:G,toggleMenu:X,openMenu:ee,closeMenu:U,setHighlightedIndex:w,setInputValue:ie,selectItem:q,reset:se,highlightedIndex:u,isOpen:c,selectedItem:d,inputValue:f}}var Wr={activeIndex:-1,selectedItems:[]};function rr(e,t){return Be(e,t,Wr)}function or(e,t){return ve(e,t,Wr)}function Gi(e){var t=rr(e,"activeIndex"),r=rr(e,"selectedItems");return{activeIndex:t,selectedItems:r}}function ir(e){if(e.shiftKey||e.metaKey||e.ctrlKey||e.altKey)return!1;var t=e.target;return!(t instanceof HTMLInputElement&&t.value!==""&&(t.selectionStart!==0||t.selectionEnd!==0))}function Ui(e,t){return e.selectedItems===t.selectedItems&&e.activeIndex===t.activeIndex}mt.stateReducer,mt.itemToKey,mt.environment,D.array,D.array,D.array,D.func,D.number,D.number,D.number,D.func,D.func,D.string,D.string;var qi={itemToKey:Xe.itemToKey,stateReducer:Xe.stateReducer,environment:Xe.environment,keyNavigationNext:"ArrowRight",keyNavigationPrevious:"ArrowLeft"},bn=0,yn=1,wn=2,Mn=3,In=4,xn=5,Sn=6,kn=7,Cn=8,En=9,Fn=10,Vn=11,On=12,Yi=Object.freeze({__proto__:null,DropdownClick:kn,DropdownKeyDownBackspace:Sn,DropdownKeyDownNavigationPrevious:xn,FunctionAddSelectedItem:Cn,FunctionRemoveSelectedItem:En,FunctionReset:On,FunctionSetActiveIndex:Vn,FunctionSetSelectedItems:Fn,SelectedItemClick:bn,SelectedItemKeyDownBackspace:wn,SelectedItemKeyDownDelete:yn,SelectedItemKeyDownNavigationNext:Mn,SelectedItemKeyDownNavigationPrevious:In});function Xi(e,t){var r=t.type,n=t.index,o=t.props,s=t.selectedItem,i=e.activeIndex,a=e.selectedItems,l;switch(r){case bn:l={activeIndex:n};break;case In:l={activeIndex:i-1<0?0:i-1};break;case Mn:l={activeIndex:i+1>=a.length?-1:i+1};break;case wn:case yn:{if(i<0)break;var c=i;a.length===1?c=-1:i===a.length-1&&(c=a.length-2),l=W({selectedItems:[].concat(a.slice(0,i),a.slice(i+1))},{activeIndex:c});break}case xn:l={activeIndex:a.length-1};break;case Sn:l={selectedItems:a.slice(0,a.length-1)};break;case Cn:l={selectedItems:[].concat(a,[s])};break;case kn:l={activeIndex:-1};break;case En:{var u=i,d=a.findIndex(function(p){return o.itemToKey(p)===o.itemToKey(s)});if(d<0)break;a.length===1?u=-1:d===a.length-1&&(u=a.length-2),l={selectedItems:[].concat(a.slice(0,d),a.slice(d+1)),activeIndex:u};break}case Fn:{var f=t.selectedItems;l={selectedItems:f};break}case Vn:{var m=t.activeIndex;l={activeIndex:m};break}case On:l={activeIndex:or(o,"activeIndex"),selectedItems:or(o,"selectedItems")};break;default:throw new Error("Reducer called without proper action type.")}return W({},e,l)}var Ji=["refKey","ref","onClick","onKeyDown","selectedItem","index"],Qi=["refKey","ref","onKeyDown","onClick","preventKeyAction"];Pe.stateChangeTypes=Yi;function Pe(e){e===void 0&&(e={});var t=W({},qi,e),r=t.getA11yStatusMessage,n=t.environment,o=t.keyNavigationNext,s=t.keyNavigationPrevious,i=Ai(Xi,t,Gi,Ui),a=i[0],l=i[1],c=a.activeIndex,u=a.selectedItems,d=Et(),f=b.useRef(null),m=b.useRef();m.current=[];var p=rn({state:a,props:t});Dr(r,a,[c,u],n),b.useEffect(function(){d||(c===-1&&f.current?f.current.focus():m.current[c]&&m.current[c].focus())},[c]);var v=Rr(),h=b.useMemo(function(){var I;return I={},I[s]=function(){l({type:In})},I[o]=function(){l({type:Mn})},I.Delete=function(){l({type:yn})},I.Backspace=function(){l({type:wn})},I},[l,o,s]),y=b.useMemo(function(){var I;return I={},I[s]=function(S){ir(S)&&l({type:xn})},I.Backspace=function(O){ir(O)&&l({type:Sn})},I},[l,s]),g=b.useCallback(function(I){var S,O=I===void 0?{}:I,$=O.refKey,G=$===void 0?"ref":$,T=O.ref,X=O.onClick,U=O.onKeyDown,ee=O.selectedItem,w=O.index,q=_e(O,Ji),ie=p.current.state,se=Or(ee,w,ie.selectedItems,"Pass either item or index to getSelectedItemProps!"),j=se[1],k=j>-1&&j===ie.activeIndex,V=function(){l({type:bn,index:j})},_=function(z){var J=Gt(z);J&&h[J]&&h[J](z)};return W((S={},S[G]=ze(T,function(H){H&&m.current.push(H)}),S.tabIndex=k?0:-1,S.onClick=ae(X,V),S.onKeyDown=ae(U,_),S),q)},[l,p,h]),A=b.useCallback(function(I,S){var O,$=I===void 0?{}:I,G=$.refKey,T=G===void 0?"ref":G,X=$.ref,U=$.onKeyDown,ee=$.onClick,w=$.preventKeyAction,q=w===void 0?!1:w,ie=_e($,Qi),se=S===void 0?{}:S;se.suppressRefError;var j=function(_){var H=Gt(_);H&&y[H]&&y[H](_)},k=function(){l({type:kn})};return W((O={},O[T]=ze(X,function(V){V&&(f.current=V)}),O),!q&&{onKeyDown:ae(U,j),onClick:ae(ee,k)},ie)},[l,y,v]),R=b.useCallback(function(I){l({type:Cn,selectedItem:I})},[l]),F=b.useCallback(function(I){l({type:En,selectedItem:I})},[l]),P=b.useCallback(function(I){l({type:Fn,selectedItems:I})},[l]),N=b.useCallback(function(I){l({type:Vn,activeIndex:I})},[l]),M=b.useCallback(function(){l({type:On})},[l]);return{getSelectedItemProps:g,getDropdownProps:A,addSelectedItem:R,removeSelectedItem:F,setSelectedItems:P,setActiveIndex:N,reset:M,selectedItems:u,activeIndex:c}}const Y={Remove:"remove",Replace:"replace",Add:"add"},Gr=Symbol.for("__MUTATIVE_PROXY_DRAFT__"),Zi=Symbol("__MUTATIVE_RAW_RETURN_SYMBOL__"),vt=Symbol.iterator,ce={mutable:"mutable",immutable:"immutable"},An={};function Qe(e,t){return e instanceof Map?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function sr(e,t){if(t in e){let r=Reflect.getPrototypeOf(e);for(;r;){const n=Reflect.getOwnPropertyDescriptor(r,t);if(n)return n;r=Reflect.getPrototypeOf(r)}}}function Rn(e){return Object.getPrototypeOf(e)===Set.prototype}function Dn(e){return Object.getPrototypeOf(e)===Map.prototype}function le(e){var t;return(t=e.copy)!==null&&t!==void 0?t:e.original}function Ee(e){return!!B(e)}function B(e){return typeof e!="object"?null:e?.[Gr]}function jn(e){var t;const r=B(e);return r?(t=r.copy)!==null&&t!==void 0?t:r.original:e}function ue(e,t){if(!e||typeof e!="object")return!1;let r;return Object.getPrototypeOf(e)===Object.prototype||Array.isArray(e)||e instanceof Map||e instanceof Set||!!t?.mark&&((r=t.mark(e,ce))===ce.immutable||typeof r=="function")}function Ur(e,t=[]){if(Object.hasOwnProperty.call(e,"key")){const r=e.parent.copy,n=B(be(r,e.key));if(n!==null&&n?.original!==e.original)return null;const o=e.parent.type===3,s=o?Array.from(e.parent.setMap.keys()).indexOf(e.key):e.key;if(!(o&&r.size>s||Qe(r,s)))return null;t.push(s)}if(e.parent)return Ur(e.parent,t);t.reverse();try{es(e.copy,t)}catch{return null}return t}function Ve(e){return Array.isArray(e)?1:e instanceof Map?2:e instanceof Set?3:0}function be(e,t){return Ve(e)===2?e.get(t):e[t]}function et(e,t,r){Ve(e)===2?e.set(t,r):e[t]=r}function _t(e,t){const r=B(e);return(r?le(r):e)[t]}function ge(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function qt(e){if(e)for(;e.finalities.revoke.length>0;)e.finalities.revoke.pop()()}function Ce(e,t){return t?e:[""].concat(e).map(r=>{const n=`${r}`;return n.indexOf("/")===-1&&n.indexOf("~")===-1?n:n.replace(/~/g,"~0").replace(/\//g,"~1")}).join("/")}function es(e,t){for(let r=0;r<t.length-1;r+=1){const n=t[r];if(e=be(Ve(e)===3?Array.from(e):e,n),typeof e!="object")throw new Error(`Cannot resolve patch at '${t.join("/")}'.`)}return e}function ts(e){const t=Object.create(Object.getPrototypeOf(e));return Reflect.ownKeys(e).forEach(r=>{let n=Reflect.getOwnPropertyDescriptor(e,r);if(n.enumerable&&n.configurable&&n.writable){t[r]=e[r];return}n.writable||(n.writable=!0,n.configurable=!0),(n.get||n.set)&&(n={configurable:!0,writable:!0,enumerable:n.enumerable,value:e[r]}),Reflect.defineProperty(t,r,n)}),t}const ns=Object.prototype.propertyIsEnumerable;function qr(e,t){let r;if(Array.isArray(e))return Array.prototype.concat.call(e);if(e instanceof Set){if(!Rn(e)){const n=Object.getPrototypeOf(e).constructor;return new n(e.values())}return Set.prototype.difference?Set.prototype.difference.call(e,new Set):new Set(e.values())}else if(e instanceof Map){if(!Dn(e)){const n=Object.getPrototypeOf(e).constructor;return new n(e)}return new Map(e)}else if(t?.mark&&(r=t.mark(e,ce),r!==void 0)&&r!==ce.mutable){if(r===ce.immutable)return ts(e);if(typeof r=="function"){if(t.enablePatches||t.enableAutoFreeze)throw new Error("You can't use mark and patches or auto freeze together.");return r()}throw new Error(`Unsupported mark result: ${r}`)}else if(typeof e=="object"&&Object.getPrototypeOf(e)===Object.prototype){const n={};return Object.keys(e).forEach(o=>{n[o]=e[o]}),Object.getOwnPropertySymbols(e).forEach(o=>{ns.call(e,o)&&(n[o]=e[o])}),n}else throw new Error("Please check mark() to ensure that it is a stable marker draftable function.")}function oe(e){e.copy||(e.copy=qr(e.original,e.options))}function Ye(e){if(!ue(e))return jn(e);if(Array.isArray(e))return e.map(Ye);if(e instanceof Map){const r=Array.from(e.entries()).map(([n,o])=>[n,Ye(o)]);if(!Dn(e)){const n=Object.getPrototypeOf(e).constructor;return new n(r)}return new Map(r)}if(e instanceof Set){const r=Array.from(e).map(Ye);if(!Rn(e)){const n=Object.getPrototypeOf(e).constructor;return new n(r)}return new Set(r)}const t=Object.create(Object.getPrototypeOf(e));for(const r in e)t[r]=Ye(e[r]);return t}function gt(e){return Ee(e)?Ye(e):e}function fe(e){var t;e.assignedMap=(t=e.assignedMap)!==null&&t!==void 0?t:new Map,e.operated||(e.operated=!0,e.parent&&fe(e.parent))}function ar(){throw new Error("Cannot modify frozen object")}function Te(e,t,r,n,o){{r=r??new WeakMap,n=n??[],o=o??[];const i=r.has(e)?r.get(e):e;if(n.length>0){const a=n.indexOf(i);if(i&&typeof i=="object"&&a!==-1)throw n[0]===i?new Error("Forbids circular reference"):new Error(`Forbids circular reference: ~/${o.slice(0,a).map((l,c)=>{if(typeof l=="symbol")return`[${l.toString()}]`;const u=n[c];return typeof l=="object"&&(u instanceof Map||u instanceof Set)?Array.from(u.keys()).indexOf(l):l}).join("/")}`);n.push(i),o.push(t)}else n.push(i)}if(Object.isFrozen(e)||Ee(e)){n.pop(),o.pop();return}switch(Ve(e)){case 2:for(const[a,l]of e)Te(a,a,r,n,o),Te(l,a,r,n,o);e.set=e.clear=e.delete=ar;break;case 3:for(const a of e)Te(a,a,r,n,o);e.add=e.clear=e.delete=ar;break;case 1:Object.freeze(e);let i=0;for(const a of e)Te(a,i,r,n,o),i+=1;break;default:Object.freeze(e),Object.keys(e).forEach(a=>{const l=e[a];Te(l,a,r,n,o)})}n.pop(),o.pop()}function Pn(e,t){const r=Ve(e);if(r===0)Reflect.ownKeys(e).forEach(n=>{t(n,e[n],e)});else if(r===1){let n=0;for(const o of e)t(n,o,e),n+=1}else e.forEach((n,o)=>t(o,n,e))}function Yr(e,t,r){if(Ee(e)||!ue(e,r)||t.has(e)||Object.isFrozen(e))return;const n=e instanceof Set,o=n?new Map:void 0;if(t.add(e),Pn(e,(s,i)=>{var a;if(Ee(i)){const l=B(i);oe(l);const c=!((a=l.assignedMap)===null||a===void 0)&&a.size||l.operated?l.copy:l.original;et(n?o:e,s,c)}else Yr(i,t,r)}),o){const s=e,i=Array.from(s);s.clear(),i.forEach(a=>{s.add(o.has(a)?o.get(a):a)})}}function rs(e,t){const r=e.type===3?e.setMap:e.copy;e.finalities.revoke.length>1&&e.assignedMap.get(t)&&r&&Yr(be(r,t),e.finalities.handledSet,e.options)}function Yt(e){e.type===3&&e.copy&&(e.copy.clear(),e.setMap.forEach(t=>{e.copy.add(jn(t))}))}function Xt(e,t,r,n){if(e.operated&&e.assignedMap&&e.assignedMap.size>0&&!e.finalized){if(r&&n){const s=Ur(e);s&&t(e,s,r,n)}e.finalized=!0}}function Tn(e,t,r,n){const o=B(r);o&&(o.callbacks||(o.callbacks=[]),o.callbacks.push((s,i)=>{var a;const l=e.type===3?e.setMap:e.copy;if(ge(be(l,t),r)){let c=o.original;o.copy&&(c=o.copy),Yt(e),Xt(e,n,s,i),e.options.enableAutoFreeze&&(e.options.updatedValues=(a=e.options.updatedValues)!==null&&a!==void 0?a:new WeakMap,e.options.updatedValues.set(c,o.original)),et(l,t,c)}}),e.options.enableAutoFreeze&&o.finalities!==e.finalities&&(e.options.enableAutoFreeze=!1)),ue(r,e.options)&&e.finalities.draft.push(()=>{const s=e.type===3?e.setMap:e.copy;ge(be(s,t),r)&&rs(e,t)})}function os(e,t,r,n,o){let{original:s,assignedMap:i,options:a}=e,l=e.copy;l.length<s.length&&([s,l]=[l,s],[r,n]=[n,r]);for(let c=0;c<s.length;c+=1)if(i.get(c.toString())&&l[c]!==s[c]){const u=t.concat([c]),d=Ce(u,o);r.push({op:Y.Replace,path:d,value:gt(l[c])}),n.push({op:Y.Replace,path:d,value:gt(s[c])})}for(let c=s.length;c<l.length;c+=1){const u=t.concat([c]),d=Ce(u,o);r.push({op:Y.Add,path:d,value:gt(l[c])})}if(s.length<l.length){const{arrayLengthAssignment:c=!0}=a.enablePatches;if(c){const u=t.concat(["length"]),d=Ce(u,o);n.push({op:Y.Replace,path:d,value:s.length})}else for(let u=l.length;s.length<u;u-=1){const d=t.concat([u-1]),f=Ce(d,o);n.push({op:Y.Remove,path:f})}}}function is({original:e,copy:t,assignedMap:r},n,o,s,i){r.forEach((a,l)=>{const c=be(e,l),u=gt(be(t,l)),d=a?Qe(e,l)?Y.Replace:Y.Add:Y.Remove;if(ge(c,u)&&d===Y.Replace)return;const f=n.concat(l),m=Ce(f,i);o.push(d===Y.Remove?{op:d,path:m}:{op:d,path:m,value:u}),s.push(d===Y.Add?{op:Y.Remove,path:m}:d===Y.Remove?{op:Y.Add,path:m,value:c}:{op:Y.Replace,path:m,value:c})})}function ss({original:e,copy:t},r,n,o,s){let i=0;e.forEach(a=>{if(!t.has(a)){const l=r.concat([i]),c=Ce(l,s);n.push({op:Y.Remove,path:c,value:a}),o.unshift({op:Y.Add,path:c,value:a})}i+=1}),i=0,t.forEach(a=>{if(!e.has(a)){const l=r.concat([i]),c=Ce(l,s);n.push({op:Y.Add,path:c,value:a}),o.unshift({op:Y.Remove,path:c,value:a})}i+=1})}function Ze(e,t,r,n){const{pathAsArray:o=!0}=e.options.enablePatches;switch(e.type){case 0:case 2:return is(e,t,r,n,o);case 1:return os(e,t,r,n,o);case 3:return ss(e,t,r,n,o)}}const St=(e,t,r=!1)=>{if(typeof e=="object"&&e!==null&&(!ue(e,t)||r))throw new Error("Strict mode: Mutable data cannot be accessed directly, please use 'unsafe(callback)' wrap.")},Jt={get size(){return le(B(this)).size},has(e){return le(B(this)).has(e)},set(e,t){const r=B(this),n=le(r);return(!n.has(e)||!ge(n.get(e),t))&&(oe(r),fe(r),r.assignedMap.set(e,!0),r.copy.set(e,t),Tn(r,e,t,Ze)),this},delete(e){if(!this.has(e))return!1;const t=B(this);return oe(t),fe(t),t.original.has(e)?t.assignedMap.set(e,!1):t.assignedMap.delete(e),t.copy.delete(e),!0},clear(){const e=B(this);if(this.size){oe(e),fe(e),e.assignedMap=new Map;for(const[t]of e.original)e.assignedMap.set(t,!1);e.copy.clear()}},forEach(e,t){const r=B(this);le(r).forEach((n,o)=>{e.call(t,this.get(o),o,this)})},get(e){var t,r;const n=B(this),o=le(n).get(e),s=((r=(t=n.options).mark)===null||r===void 0?void 0:r.call(t,o,ce))===ce.mutable;if(n.options.strict&&St(o,n.options,s),s||n.finalized||!ue(o,n.options)||o!==n.original.get(e))return o;const i=An.createDraft({original:o,parentDraft:n,key:e,finalities:n.finalities,options:n.options});return oe(n),n.copy.set(e,i),i},keys(){return le(B(this)).keys()},values(){const e=this.keys();return{[vt]:()=>this.values(),next:()=>{const t=e.next();return t.done?t:{done:!1,value:this.get(t.value)}}}},entries(){const e=this.keys();return{[vt]:()=>this.entries(),next:()=>{const t=e.next();if(t.done)return t;const r=this.get(t.value);return{done:!1,value:[t.value,r]}}}},[vt](){return this.entries()}},as=Reflect.ownKeys(Jt),lr=(e,t,{isValuesIterator:r})=>()=>{var n,o;const s=t.next();if(s.done)return s;const i=s.value;let a=e.setMap.get(i);const l=B(a),c=((o=(n=e.options).mark)===null||o===void 0?void 0:o.call(n,a,ce))===ce.mutable;if(e.options.strict&&St(i,e.options,c),!c&&!l&&ue(i,e.options)&&!e.finalized&&e.original.has(i)){const u=An.createDraft({original:i,parentDraft:e,key:i,finalities:e.finalities,options:e.options});e.setMap.set(i,u),a=u}else l&&(a=l.proxy);return{done:!1,value:r?a:[a,a]}},kt={get size(){return B(this).setMap.size},has(e){const t=B(this);if(t.setMap.has(e))return!0;oe(t);const r=B(e);return!!(r&&t.setMap.has(r.original))},add(e){const t=B(this);return this.has(e)||(oe(t),fe(t),t.assignedMap.set(e,!0),t.setMap.set(e,e),Tn(t,e,e,Ze)),this},delete(e){if(!this.has(e))return!1;const t=B(this);oe(t),fe(t);const r=B(e);return r&&t.setMap.has(r.original)?(t.assignedMap.set(r.original,!1),t.setMap.delete(r.original)):(!r&&t.setMap.has(e)?t.assignedMap.set(e,!1):t.assignedMap.delete(e),t.setMap.delete(e))},clear(){if(!this.size)return;const e=B(this);oe(e),fe(e);for(const t of e.original)e.assignedMap.set(t,!1);e.setMap.clear()},values(){const e=B(this);oe(e);const t=e.setMap.keys();return{[Symbol.iterator]:()=>this.values(),next:lr(e,t,{isValuesIterator:!0})}},entries(){const e=B(this);oe(e);const t=e.setMap.keys();return{[Symbol.iterator]:()=>this.entries(),next:lr(e,t,{isValuesIterator:!1})}},keys(){return this.values()},[vt](){return this.values()},forEach(e,t){const r=this.values();let n=r.next();for(;!n.done;)e.call(t,n.value,n.value,this),n=r.next()}};Set.prototype.difference&&Object.assign(kt,{intersection(e){return Set.prototype.intersection.call(new Set(this.values()),e)},union(e){return Set.prototype.union.call(new Set(this.values()),e)},difference(e){return Set.prototype.difference.call(new Set(this.values()),e)},symmetricDifference(e){return Set.prototype.symmetricDifference.call(new Set(this.values()),e)},isSubsetOf(e){return Set.prototype.isSubsetOf.call(new Set(this.values()),e)},isSupersetOf(e){return Set.prototype.isSupersetOf.call(new Set(this.values()),e)},isDisjointFrom(e){return Set.prototype.isDisjointFrom.call(new Set(this.values()),e)}});const ls=Reflect.ownKeys(kt),Xr=new WeakSet,Jr={get(e,t,r){var n,o;const s=(n=e.copy)===null||n===void 0?void 0:n[t];if(s&&Xr.has(s))return s;if(t===Gr)return e;let i;if(e.options.mark){const c=t==="size"&&(e.original instanceof Map||e.original instanceof Set)?Reflect.get(e.original,t):Reflect.get(e.original,t,r);if(i=e.options.mark(c,ce),i===ce.mutable)return e.options.strict&&St(c,e.options,!0),c}const a=le(e);if(a instanceof Map&&as.includes(t)){if(t==="size")return Object.getOwnPropertyDescriptor(Jt,"size").get.call(e.proxy);const c=Jt[t];if(c)return c.bind(e.proxy)}if(a instanceof Set&&ls.includes(t)){if(t==="size")return Object.getOwnPropertyDescriptor(kt,"size").get.call(e.proxy);const c=kt[t];if(c)return c.bind(e.proxy)}if(!Qe(a,t)){const c=sr(a,t);return c?"value"in c?c.value:(o=c.get)===null||o===void 0?void 0:o.call(e.proxy):void 0}const l=a[t];if(e.options.strict&&St(l,e.options),e.finalized||!ue(l,e.options))return l;if(l===_t(e.original,t)){if(oe(e),e.copy[t]=_n({original:e.original[t],parentDraft:e,key:e.type===1?Number(t):t,finalities:e.finalities,options:e.options}),typeof i=="function"){const c=B(e.copy[t]);return oe(c),fe(c),c.copy}return e.copy[t]}return l},set(e,t,r){var n;if(e.type===3||e.type===2)throw new Error("Map/Set draft does not support any property assignment.");let o;if(e.type===1&&t!=="length"&&!(Number.isInteger(o=Number(t))&&o>=0&&(t===0||o===0||String(o)===String(t))))throw new Error("Only supports setting array indices and the 'length' property.");const s=sr(le(e),t);if(s?.set)return s.set.call(e.proxy,r),!0;const i=_t(le(e),t),a=B(i);return a&&ge(a.original,r)?(e.copy[t]=r,e.assignedMap=(n=e.assignedMap)!==null&&n!==void 0?n:new Map,e.assignedMap.set(t,!1),!0):(ge(r,i)&&(r!==void 0||Qe(e.original,t))||(oe(e),fe(e),Qe(e.original,t)&&ge(r,e.original[t])?e.assignedMap.delete(t):e.assignedMap.set(t,!0),e.copy[t]=r,Tn(e,t,r,Ze)),!0)},has(e,t){return t in le(e)},ownKeys(e){return Reflect.ownKeys(le(e))},getOwnPropertyDescriptor(e,t){const r=le(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.type!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},getPrototypeOf(e){return Reflect.getPrototypeOf(e.original)},setPrototypeOf(){throw new Error("Cannot call 'setPrototypeOf()' on drafts")},defineProperty(){throw new Error("Cannot call 'defineProperty()' on drafts")},deleteProperty(e,t){var r;return e.type===1?Jr.set.call(this,e,t,void 0,e.proxy):(_t(e.original,t)!==void 0||t in e.original?(oe(e),fe(e),e.assignedMap.set(t,!1)):(e.assignedMap=(r=e.assignedMap)!==null&&r!==void 0?r:new Map,e.assignedMap.delete(t)),e.copy&&delete e.copy[t],!0)}};function _n(e){const{original:t,parentDraft:r,key:n,finalities:o,options:s}=e,i=Ve(t),a={type:i,finalized:!1,parent:r,original:t,copy:null,proxy:null,finalities:o,options:s,setMap:i===3?new Map(t.entries()):void 0};(n||"key"in e)&&(a.key=n);const{proxy:l,revoke:c}=Proxy.revocable(i===1?Object.assign([],a):a,Jr);if(o.revoke.push(c),Xr.add(l),a.proxy=l,r){const u=r;u.finalities.draft.push((d,f)=>{var m,p;const v=B(l);let h=u.type===3?u.setMap:u.copy;const y=be(h,n),g=B(y);if(g){let A=g.original;g.operated&&(A=jn(y)),Yt(g),Xt(g,Ze,d,f),u.options.enableAutoFreeze&&(u.options.updatedValues=(m=u.options.updatedValues)!==null&&m!==void 0?m:new WeakMap,u.options.updatedValues.set(A,g.original)),et(h,n,A)}(p=v.callbacks)===null||p===void 0||p.forEach(A=>{A(d,f)})})}else{const u=B(l);u.finalities.draft.push((d,f)=>{Yt(u),Xt(u,Ze,d,f)})}return l}An.createDraft=_n;function cs(e,t,r,n,o){var s;const i=B(e),a=(s=i?.original)!==null&&s!==void 0?s:e,l=!!t.length;if(i?.operated)for(;i.finalities.draft.length>0;)i.finalities.draft.pop()(r,n);const c=l?t[0]:i?i.operated?i.copy:i.original:e;return i&&qt(i),o&&Te(c,c,i?.options.updatedValues),[c,r&&l?[{op:Y.Replace,path:[],value:t[0]}]:r,n&&l?[{op:Y.Replace,path:[],value:a}]:n]}function us(e,t){var r;const n={draft:[],revoke:[],handledSet:new WeakSet};let o,s;t.enablePatches&&(o=[],s=[]);const a=((r=t.mark)===null||r===void 0?void 0:r.call(t,e,ce))===ce.mutable||!ue(e,t)?e:_n({original:e,parentDraft:null,finalities:n,options:t});return[a,(l=[])=>{const[c,u,d]=cs(a,l,o,s,t.enableAutoFreeze);return t.enablePatches?[c,u,d]:c}]}function Qt(e){const{rootDraft:t,value:r,useRawReturn:n=!1,isRoot:o=!0}=e;Pn(r,(s,i,a)=>{const l=B(i);if(l&&t&&l.finalities===t.finalities){e.isContainDraft=!0;const c=l.original;if(a instanceof Set){const u=Array.from(a);a.clear(),u.forEach(d=>a.add(s===d?c:d))}else et(a,s,c)}else typeof i=="object"&&i!==null&&(e.value=i,e.isRoot=!1,Qt(e))}),o&&(e.isContainDraft||console.warn("The return value does not contain any draft, please use 'rawReturn()' to wrap the return value to improve performance."),n&&console.warn("The return value contains drafts, please don't use 'rawReturn()' to wrap the return value."))}function Qr(e){var t;const r=B(e);if(!ue(e,r?.options))return e;const n=Ve(e);if(r&&!r.operated)return r.original;let o;function s(){o=n===2?Dn(e)?new Map(e):new(Object.getPrototypeOf(e)).constructor(e):n===3?Array.from(r.setMap.values()):qr(e,r?.options)}if(r){r.finalized=!0;try{s()}finally{r.finalized=!1}}else o=e;if(Pn(o,(i,a)=>{if(r&&ge(be(r.original,i),a))return;const l=Qr(a);l!==a&&(o===e&&s(),et(o,i,l))}),n===3){const i=(t=r?.original)!==null&&t!==void 0?t:o;return Rn(i)?new Set(o):new(Object.getPrototypeOf(i)).constructor(o)}return o}function cr(e){if(!Ee(e))throw new Error(`current() is only used for Draft, parameter: ${e}`);return Qr(e)}const ds=e=>function t(r,n,o){var s,i,a;if(typeof r=="function"&&typeof n!="function")return function(F,...P){return t(F,N=>r.call(this,N,...P),n)};const l=r,c=n;let u=o;if(typeof n!="function"&&(u=n),u!==void 0&&Object.prototype.toString.call(u)!=="[object Object]")throw new Error(`Invalid options: ${u}, 'options' should be an object.`);u=Object.assign(Object.assign({},e),u);const d=Ee(l)?cr(l):l,f=Array.isArray(u.mark)?(F,P)=>{for(const N of u.mark){if(typeof N!="function")throw new Error(`Invalid mark: ${N}, 'mark' should be a function.`);const M=N(F,P);if(M)return M}}:u.mark,m=(s=u.enablePatches)!==null&&s!==void 0?s:!1,p=(i=u.strict)!==null&&i!==void 0?i:!1,h={enableAutoFreeze:(a=u.enableAutoFreeze)!==null&&a!==void 0?a:!1,mark:f,strict:p,enablePatches:m};if(!ue(d,h)&&typeof d=="object"&&d!==null)throw new Error("Invalid base state: create() only supports plain objects, arrays, Set, Map or using mark() to mark the state as immutable.");const[y,g]=us(d,h);if(typeof n!="function"){if(!ue(d,h))throw new Error("Invalid base state: create() only supports plain objects, arrays, Set, Map or using mark() to mark the state as immutable.");return[y,g]}let A;try{A=c(y)}catch(F){throw qt(B(y)),F}const R=F=>{const P=B(y);if(!Ee(F)){if(F!==void 0&&!ge(F,y)&&P?.operated)throw new Error("Either the value is returned as a new non-draft value, or only the draft is modified without returning any value.");const M=F?.[Zi];if(M){const I=M[0];return h.strict&&typeof F=="object"&&F!==null&&Qt({rootDraft:P,value:F,useRawReturn:!0}),g([I])}if(F!==void 0)return typeof F=="object"&&F!==null&&Qt({rootDraft:P,value:F}),g([F])}if(F===y||F===void 0)return g([]);const N=B(F);if(h===N.options){if(N.operated)throw new Error("Cannot return a modified child draft.");return g([cr(F)])}return g([F])};return A instanceof Promise?A.then(R,F=>{throw qt(B(y)),F}):R(A)},fs=ds();Object.prototype.constructor.toString();function hs(e,t){b.useRef({patches:[],inversePatches:[]});const r=b.useRef(0),n=b.useRef(0);let o=r.current;b.useEffect(()=>{r.current=o,n.current=o}),o+=1,n.current+=1;const[s,i]=b.useState(()=>typeof e=="function"?e():e),a=b.useCallback(l=>{i(c=>fs(c,typeof l=="function"?l:()=>l,t))},[]);return b.useEffect(()=>{}),[s,a]}function Zr(e){return e.isMultiple?x.jsx(ps,{...e}):x.jsx(ms,{...e})}function ps({value:e=[],isLoading:t=!1,options:r,defaultSelected:n=[],onChange:o,placeholder:s="Seleccionar...",className:i,hideReset:a=!1,size:l="md"}){const[c,u]=b.useState(n);b.useEffect(()=>{u(n)},[n]);const d=b.useMemo(()=>r.filter(S=>!c.some(O=>O.value===S.value)),[c,r]),f=b.useCallback(S=>{u(S),o(S)},[o]),m=b.useCallback(S=>{const O=c.filter($=>$.value!==S.value);f(O)},[c,f]),p=b.useCallback(()=>{f([])},[f]),{getSelectedItemProps:v,getDropdownProps:h}=Pe({selectedItems:c,defaultSelectedItems:n,onStateChange({selectedItems:S,type:O}){switch(O){case Pe.stateChangeTypes.SelectedItemKeyDownBackspace:case Pe.stateChangeTypes.SelectedItemKeyDownDelete:case Pe.stateChangeTypes.DropdownKeyDownBackspace:case Pe.stateChangeTypes.FunctionRemoveSelectedItem:S&&f(S);break}}}),{isOpen:y,getToggleButtonProps:g,getMenuProps:A,getInputProps:R,highlightedIndex:F,getItemProps:P,setInputValue:N}=Ie({items:d,itemToString:S=>S?.label??"",defaultHighlightedIndex:0,selectedItem:null,stateReducer(S,O){const{changes:$,type:G}=O;switch(G){case Ie.stateChangeTypes.InputKeyDownEnter:case Ie.stateChangeTypes.ItemClick:return{...$,isOpen:!0,highlightedIndex:0};default:return $}},onStateChange({type:S,selectedItem:O}){switch(S){case Ie.stateChangeTypes.InputKeyDownEnter:case Ie.stateChangeTypes.ItemClick:case Ie.stateChangeTypes.InputBlur:if(O){const $=[...c,O];f($),N("")}break}}}),M=b.useCallback((S,O)=>x.jsxs("div",{className:"badge badge-primary pr-0",children:[x.jsx("div",{className:"rounded-l-md",...v({selectedItem:S,index:O}),children:S.label}),x.jsx("button",{type:"button",className:"btn btn-xs btn-circle btn-error btn-ghost",onClick:$=>{$.stopPropagation(),m(S)},children:x.jsx(Xo,{size:16})})]},`selected-item-${S.value}-${O}`),[v,m]),I=b.useCallback((S,O)=>x.jsx("li",{className:Ke("flex cursor-pointer flex-col px-3 py-2 shadow-sm",F===O&&"bg-neutral"),...P({item:S,index:O}),children:x.jsx("span",{children:S.label})},`${S.value}-${O}`),[F,P]);return x.jsxs("div",{className:Ke("dropdown",i),children:[x.jsxs("div",{className:"join input h-fit w-full items-center rounded-md p-1",children:[t?x.jsx("span",{className:"loading loading-dots join-item loading-sm"}):x.jsxs("div",{className:"inline-flex w-full flex-wrap items-center gap-1",children:[c.map(M),x.jsx("input",{placeholder:s,className:"input input-ghost flex-1 focus:outline-none",...R(h({preventKeyAction:y}))})]}),x.jsx("button",{className:"btn btn-sm join-item btn-ghost btn-circle",type:"button",onClick:p,disabled:c.length===0,children:x.jsx(Ir,{})}),x.jsx("button",{"aria-label":"toggle menu",className:"btn btn-sm join-item btn-ghost btn-circle",type:"button",...g(),children:y?x.jsx(Mr,{className:"h-6 w-6"}):x.jsx(wr,{className:"h-6 w-6"})})]}),x.jsx("ul",{className:Ke("dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",(!y||!d.length)&&"hidden"),...A(),children:y&&d.map(I)})]})}function ms({value:e=null,isLoading:t=!1,options:r,defaultSelected:n=null,onChange:o,placeholder:s="Seleccionar...",className:i,size:a="md",label:l="Seleccionar...",hideReset:c=!1}){const[u,d]=hs(r),[f,m]=b.useState(n?.label||e?.label||""),p=b.useCallback(M=>{d(I=>{for(let S=0;S<r.length;S++)I[S]=r[S];if(I.length=r.length,M){let S=0;const O=M.toLowerCase();for(let $=0;$<I.length;$++){const G=I[$];G?.label.toLowerCase().includes(O)&&(I[S]=G,S++)}I.length=S}})},[r,d]),{isOpen:v,getToggleButtonProps:h,getMenuProps:y,getInputProps:g,highlightedIndex:A,getItemProps:R,reset:F,getLabelProps:P}=Ie({items:u,defaultSelectedItem:n||e,inputValue:f,itemToString:M=>M?.label??"",onInputValueChange({inputValue:M}){m(M||""),p(M||"")},onSelectedItemChange:({selectedItem:M})=>{o(M)},onIsOpenChange:({isOpen:M,selectedItem:I})=>{M||m(I?.label||"")}});b.useEffect(()=>{d(r)},[r,d]),b.useEffect(()=>{e===null&&F()},[e,F]);const N=b.useCallback((M,I)=>x.jsx("li",{className:Ke("flex cursor-pointer flex-col px-3 py-2 shadow-sm",A===I&&"bg-neutral",e?.value===M.value&&"font-bold"),...R({item:M,index:I}),children:x.jsx("span",{children:M.label})},`${M.value}-${I}`),[A,e,R]);return x.jsxs("div",{className:Ke("dropdown",i),children:[x.jsx("label",{htmlFor:"combo",className:"label",...P(),children:l}),x.jsxs("div",{className:"join h-fit w-full items-center bg-base-100",children:[t?x.jsx("span",{className:"loading loading-dots join-item loading-sm"}):x.jsx("input",{placeholder:s,className:"input input-sm input-bordered join-item w-full",...g()}),!c&&x.jsx("button",{className:"btn btn-sm join-item",type:"button",onClick:F,disabled:!e,children:x.jsx(Ir,{})}),x.jsx("button",{"aria-label":"toggle menu",className:"btn btn-sm join-item",type:"button",...h(),children:v?x.jsx(Mr,{className:"h-6 w-6"}):x.jsx(wr,{className:"h-6 w-6"})})]}),x.jsx("ul",{className:Ke("dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",(!v||!u.length)&&"hidden"),...y(),children:v&&u.map(N)})]})}function vs({label:e,placeholder:t,options:r,isNumber:n=!1,isMultiple:o=!1}){return x.jsxs(x.Fragment,{children:[x.jsx("label",{htmlFor:"combobox",className:"label",children:e}),o?x.jsx(bs,{placeholder:t,options:r,isNumber:n}):x.jsx(gs,{placeholder:t,options:r})]})}function gs({placeholder:e,options:t,isLoading:r=!1}){const n=We();return x.jsx(Zr,{isMultiple:!1,options:t,onChange:o=>n.handleChange(o?.value||""),placeholder:e,isLoading:r,defaultSelected:t.find(o=>o.value===n.state.value)||null})}function bs({placeholder:e,options:t,isLoading:r=!1}){const n=We();return x.jsx(Zr,{isMultiple:!0,options:t,onChange:o=>n.handleChange(o.map(s=>s.value)),placeholder:e,isLoading:r,defaultSelected:t.filter(o=>n.state.value.includes(o.value))})}function ys({label:e,placeholder:t}){const r=We(),[n,o]=b.useState(!1);return x.jsxs("fieldset",{className:"fieldset",children:[x.jsx("legend",{className:"fieldset-legend",children:e}),x.jsxs("label",{className:"input w-full",children:[x.jsx(qo,{size:16}),x.jsx("input",{type:n?"text":"password",className:"grow",placeholder:t,value:r.state.value,onChange:s=>r.handleChange(s.target.value)}),x.jsx("button",{type:"button",className:"btn btn-sm btn-circle",onClick:()=>o(!n),children:n?x.jsx(Ho,{size:16}):x.jsx(Go,{size:16})})]}),r.state.meta.isTouched&&r.state.meta.errors.length?r.state.meta.errors.map(s=>x.jsx("p",{className:"fieldset-label text-error",children:s.message},s.path)):null]})}function ws({label:e,placeholder:t,options:r,isNumber:n=!1}){const o=We();return x.jsxs("fieldset",{className:"fieldset",children:[x.jsx("legend",{className:"fieldset-legend",children:e}),x.jsxs("select",{className:"select w-full",value:n?o.state.value:o.state.value?.toString(),onChange:s=>o.handleChange(n?Number(s.target.value):s.target.value),children:[x.jsx("option",{disabled:!0,selected:!0,children:t||"Seleccione una opción"}),r.map(s=>x.jsx("option",{value:s.value,children:s.label},s.value))]}),o.state.meta.isTouched&&o.state.meta.errors.length?o.state.meta.errors.flatMap(s=>x.jsx("p",{className:"fieldset-label text-error",children:s.message},s.message)):null]})}function Ms({label:e,placeholder:t,type:r="text",prefixComponent:n,suffixComponent:o}){const s=We();return x.jsxs("fieldset",{className:"fieldset",children:[x.jsx("legend",{className:"fieldset-legend",children:e}),x.jsxs("div",{className:"input w-full",children:[n&&n,x.jsx("input",{type:r,className:"grow",placeholder:t,value:r==="number"?s.state.value?.toString():s.state.value,onChange:i=>s.handleChange(r==="number"?Number(i.target.value):i.target.value)}),o&&o]}),s.state.meta.isTouched&&s.state.meta.errors.length?s.state.meta.errors.flatMap(i=>x.jsx("p",{className:"fieldset-label text-error",children:i.message},i.message)):null]})}function Is({label:e,trueLabel:t,falseLabel:r}){const n=We();return x.jsxs("fieldset",{className:"fieldset",children:[x.jsx("legend",{className:"fieldset-legend",children:e}),x.jsxs("label",{className:"fieldset-label",children:[x.jsx("input",{type:"checkbox",className:"toggle",defaultChecked:!0,checked:n.state.value,onChange:()=>n.handleChange(!n.state.value)}),n.state.value?t:r]}),n.state.meta.isTouched&&n.state.meta.errors.length?n.state.meta.errors.flatMap(o=>x.jsx("p",{className:"fieldset-label text-error",children:o.message},o.message)):null]})}function xs({label:e,className:t="btn btn-neutral"}){const{Subscribe:r}=Cs();return x.jsx(r,{selector:n=>({isSubmitting:n.isSubmitting,canSubmit:n.canSubmit}),children:({isSubmitting:n,canSubmit:o})=>x.jsxs("button",{disabled:n||!o,className:t,children:[n&&x.jsx("span",{className:"loading loading-spinner"}),e]})})}const{fieldContext:Ss,useFieldContext:We,formContext:ks,useFormContext:Cs}=ui(),{useAppForm:Vs}=di({fieldComponents:{FSTextField:Ms,FSPasswordField:ys,FSSelectField:ws,FSToggleField:Is,FSComboBoxField:vs},formComponents:{SubscribeButton:xs},fieldContext:Ss,formContext:ks});export{fs as a,Ke as c,Vs as u};
