{"/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend/app/config/css/app.css": {"file": "assets/app-D8wouJFs.css", "src": "/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend/app/config/css/app.css"}, "__vite-browser-external": {"file": "assets/__vite-browser-external-BIHI7g3E.js", "name": "__vite-browser-external", "src": "__vite-browser-external", "isDynamicEntry": true}, "_client--LJeqQnE.js": {"file": "assets/client--LJeqQnE.js", "name": "client", "dynamicImports": ["__vite-browser-external", "__vite-browser-external", "__vite-browser-external", "__vite-browser-external", "app/routes/login.tsx?tsr-split=component", "app/routes/_authed/route.tsx?tsr-split=component", "app/routes/index.tsx?tsr-split=component", "app/routes/_authed/admin/route.tsx?tsr-split=component", "app/routes/_authed/admin/index.tsx?tsr-split=component", "app/routes/_authed/admin/clients/index.tsx?tsr-split=component"], "assets": ["assets/app-D8wouJFs.css"]}, "_effectErrors-DvJbF9aL.js": {"file": "assets/effectErrors-DvJbF9aL.js", "name": "effectErrors", "imports": ["_runtimes-RPMEsgVm.js"]}, "_form-vCGFeEBS.js": {"file": "assets/form-vCGFeEBS.js", "name": "form", "imports": ["_client--LJeqQnE.js", "_user-8OiDO9RD.js"]}, "_queryOptions-DcASEHgW.js": {"file": "assets/queryOptions-DcASEHgW.js", "name": "queryOptions", "imports": ["_client--LJeqQnE.js"]}, "_runtimes-RPMEsgVm.js": {"file": "assets/runtimes-RPMEsgVm.js", "name": "runtimes", "imports": ["_client--LJeqQnE.js"]}, "_user-8OiDO9RD.js": {"file": "assets/user-8OiDO9RD.js", "name": "user", "imports": ["_client--LJeqQnE.js"]}, "app/routes/_authed/admin/clients/index.tsx?tsr-split=component": {"file": "assets/index-DPvDv-Mn.js", "name": "index", "src": "app/routes/_authed/admin/clients/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["_client--LJeqQnE.js", "_queryOptions-DcASEHgW.js", "_effectErrors-DvJbF9aL.js", "_runtimes-RPMEsgVm.js", "_form-vCGFeEBS.js", "_user-8OiDO9RD.js"]}, "app/routes/_authed/admin/index.tsx?tsr-split=component": {"file": "assets/index-BzyKgCps.js", "name": "index", "src": "app/routes/_authed/admin/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["_client--LJeqQnE.js"]}, "app/routes/_authed/admin/route.tsx?tsr-split=component": {"file": "assets/route-r-1Gfi_b.js", "name": "route", "src": "app/routes/_authed/admin/route.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["_client--LJeqQnE.js", "_user-8OiDO9RD.js", "_runtimes-RPMEsgVm.js"]}, "app/routes/_authed/route.tsx?tsr-split=component": {"file": "assets/route-BCGlW2to.js", "name": "route", "src": "app/routes/_authed/route.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["_client--LJeqQnE.js", "_queryOptions-DcASEHgW.js", "_runtimes-RPMEsgVm.js", "_effectErrors-DvJbF9aL.js"]}, "app/routes/index.tsx?tsr-split=component": {"file": "assets/index-9ef86wc6.js", "name": "index", "src": "app/routes/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["_client--LJeqQnE.js"]}, "app/routes/login.tsx?tsr-split=component": {"file": "assets/login-BXzM-CoY.js", "name": "login", "src": "app/routes/login.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["_client--LJeqQnE.js", "_form-vCGFeEBS.js", "_effectErrors-DvJbF9aL.js", "_user-8OiDO9RD.js", "_runtimes-RPMEsgVm.js"]}, "virtual:$vinxi/handler/client": {"file": "assets/client-DKGam5mS.js", "name": "client", "src": "virtual:$vinxi/handler/client", "isEntry": true, "imports": ["_client--LJeqQnE.js"]}}