import{r as y,j as a,a as T,c as ge,y as H}from"./client--LJeqQnE.js";import{q as Me,u as $e}from"./queryOptions-DcASEHgW.js";import{g as O}from"./effectErrors-DvJbF9aL.js";import{A as q,o as Ue,n as be,p as L,b as Ke,a as Xe,c as ve,u as Ye,m as k,s as V,l as Qe,e as Ze,D as Re}from"./runtimes-RPMEsgVm.js";import{a as de,c as K,u as ye}from"./form-vCGFeEBS.js";import{c as E,u as ce,U as z}from"./user-8OiDO9RD.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Pe=E("calendar",Je);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Ve=E("file-text",We);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Ie=E("mail",et);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tt=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],De=E("map-pin",tt);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nt=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],je=E("phone",nt);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ot=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],rt=E("square-pen",ot);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const it=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]],lt=E("trash",it),X=({client:e})=>Me({queryKey:["clients"],queryFn:()=>q.runPromise(e.getAll())}),st=({client:e},o)=>Me({queryKey:["clients",o],queryFn:()=>q.runPromise(e.getById(o))});/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function ut(){return{accessor:(e,o)=>typeof e=="function"?{...o,accessorFn:e}:{...o,accessorKey:e},display:e=>e,group:e=>e}}function I(e,o){return typeof e=="function"?e(o):e}function F(e,o){return t=>{o.setState(n=>({...n,[e]:I(t,n[e])}))}}function Y(e){return e instanceof Function}function at(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function gt(e,o){const t=[],n=r=>{r.forEach(i=>{t.push(i);const l=o(i);l!=null&&l.length&&n(l)})};return n(e),t}function S(e,o,t){let n=[],r;return i=>{let l;t.key&&t.debug&&(l=Date.now());const s=e(i);if(!(s.length!==n.length||s.some((p,C)=>n[C]!==p)))return r;n=s;let g;if(t.key&&t.debug&&(g=Date.now()),r=o(...s),t==null||t.onChange==null||t.onChange(r),t.key&&t.debug&&t!=null&&t.debug()){const p=Math.round((Date.now()-l)*100)/100,C=Math.round((Date.now()-g)*100)/100,c=C/16,d=(f,m)=>{for(f=String(f);f.length<m;)f=" "+f;return f};console.info(`%c⏱ ${d(C,5)} /${d(p,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*c,120))}deg 100% 31%);`,t?.key)}return r}}function h(e,o,t,n){return{debug:()=>{var r;return(r=e?.debugAll)!=null?r:e[o]},key:!1,onChange:n}}function dt(e,o,t,n){const r=()=>{var l;return(l=i.getValue())!=null?l:e.options.renderFallbackValue},i={id:`${o.id}_${t.id}`,row:o,column:t,getValue:()=>o.getValue(n),renderValue:r,getContext:S(()=>[e,t,o,i],(l,s,u,g)=>({table:l,column:s,row:u,cell:g,getValue:g.getValue,renderValue:g.renderValue}),h(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(i,t,o,e)},{}),i}function ct(e,o,t,n){var r,i;const s={...e._getDefaultColumnDef(),...o},u=s.accessorKey;let g=(r=(i=s.id)!=null?i:u?typeof String.prototype.replaceAll=="function"?u.replaceAll(".","_"):u.replace(/\./g,"_"):void 0)!=null?r:typeof s.header=="string"?s.header:void 0,p;if(s.accessorFn?p=s.accessorFn:u&&(u.includes(".")?p=c=>{let d=c;for(const m of u.split(".")){var f;d=(f=d)==null?void 0:f[m]}return d}:p=c=>c[s.accessorKey]),!g)throw new Error;let C={id:`${String(g)}`,accessorFn:p,parent:n,depth:t,columnDef:s,columns:[],getFlatColumns:S(()=>[!0],()=>{var c;return[C,...(c=C.columns)==null?void 0:c.flatMap(d=>d.getFlatColumns())]},h(e.options,"debugColumns")),getLeafColumns:S(()=>[e._getOrderColumnsFn()],c=>{var d;if((d=C.columns)!=null&&d.length){let f=C.columns.flatMap(m=>m.getLeafColumns());return c(f)}return[C]},h(e.options,"debugColumns"))};for(const c of e._features)c.createColumn==null||c.createColumn(C,e);return C}const x="debugHeaders";function we(e,o,t){var n;let i={id:(n=t.id)!=null?n:o.id,column:o,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],s=u=>{u.subHeaders&&u.subHeaders.length&&u.subHeaders.map(s),l.push(u)};return s(i),l},getContext:()=>({table:e,header:i,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(i,e)}),i}const pt={createTable:e=>{e.getHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>{var i,l;const s=(i=n?.map(C=>t.find(c=>c.id===C)).filter(Boolean))!=null?i:[],u=(l=r?.map(C=>t.find(c=>c.id===C)).filter(Boolean))!=null?l:[],g=t.filter(C=>!(n!=null&&n.includes(C.id))&&!(r!=null&&r.includes(C.id)));return B(o,[...s,...g,...u],e)},h(e.options,x)),e.getCenterHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>(t=t.filter(i=>!(n!=null&&n.includes(i.id))&&!(r!=null&&r.includes(i.id))),B(o,t,e,"center")),h(e.options,x)),e.getLeftHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,t,n)=>{var r;const i=(r=n?.map(l=>t.find(s=>s.id===l)).filter(Boolean))!=null?r:[];return B(o,i,e,"left")},h(e.options,x)),e.getRightHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,t,n)=>{var r;const i=(r=n?.map(l=>t.find(s=>s.id===l)).filter(Boolean))!=null?r:[];return B(o,i,e,"right")},h(e.options,x)),e.getFooterGroups=S(()=>[e.getHeaderGroups()],o=>[...o].reverse(),h(e.options,x)),e.getLeftFooterGroups=S(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),h(e.options,x)),e.getCenterFooterGroups=S(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),h(e.options,x)),e.getRightFooterGroups=S(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),h(e.options,x)),e.getFlatHeaders=S(()=>[e.getHeaderGroups()],o=>o.map(t=>t.headers).flat(),h(e.options,x)),e.getLeftFlatHeaders=S(()=>[e.getLeftHeaderGroups()],o=>o.map(t=>t.headers).flat(),h(e.options,x)),e.getCenterFlatHeaders=S(()=>[e.getCenterHeaderGroups()],o=>o.map(t=>t.headers).flat(),h(e.options,x)),e.getRightFlatHeaders=S(()=>[e.getRightHeaderGroups()],o=>o.map(t=>t.headers).flat(),h(e.options,x)),e.getCenterLeafHeaders=S(()=>[e.getCenterFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),h(e.options,x)),e.getLeftLeafHeaders=S(()=>[e.getLeftFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),h(e.options,x)),e.getRightLeafHeaders=S(()=>[e.getRightFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),h(e.options,x)),e.getLeafHeaders=S(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,t,n)=>{var r,i,l,s,u,g;return[...(r=(i=o[0])==null?void 0:i.headers)!=null?r:[],...(l=(s=t[0])==null?void 0:s.headers)!=null?l:[],...(u=(g=n[0])==null?void 0:g.headers)!=null?u:[]].map(p=>p.getLeafHeaders()).flat()},h(e.options,x))}};function B(e,o,t,n){var r,i;let l=0;const s=function(c,d){d===void 0&&(d=1),l=Math.max(l,d),c.filter(f=>f.getIsVisible()).forEach(f=>{var m;(m=f.columns)!=null&&m.length&&s(f.columns,d+1)},0)};s(e);let u=[];const g=(c,d)=>{const f={depth:d,id:[n,`${d}`].filter(Boolean).join("_"),headers:[]},m=[];c.forEach(R=>{const v=[...m].reverse()[0],_=R.column.depth===f.depth;let w,P=!1;if(_&&R.column.parent?w=R.column.parent:(w=R.column,P=!0),v&&v?.column===w)v.subHeaders.push(R);else{const j=we(t,w,{id:[n,d,w.id,R?.id].filter(Boolean).join("_"),isPlaceholder:P,placeholderId:P?`${m.filter(Z=>Z.column===w).length}`:void 0,depth:d,index:m.length});j.subHeaders.push(R),m.push(j)}f.headers.push(R),R.headerGroup=f}),u.push(f),d>0&&g(m,d-1)},p=o.map((c,d)=>we(t,c,{depth:l,index:d}));g(p,l-1),u.reverse();const C=c=>c.filter(f=>f.column.getIsVisible()).map(f=>{let m=0,R=0,v=[0];f.subHeaders&&f.subHeaders.length?(v=[],C(f.subHeaders).forEach(w=>{let{colSpan:P,rowSpan:j}=w;m+=P,v.push(j)})):m=1;const _=Math.min(...v);return R=R+_,f.colSpan=m,f.rowSpan=R,{colSpan:m,rowSpan:R}});return C((r=(i=u[0])==null?void 0:i.headers)!=null?r:[]),u}const ft=(e,o,t,n,r,i,l)=>{let s={id:o,index:n,original:t,depth:r,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:u=>{if(s._valuesCache.hasOwnProperty(u))return s._valuesCache[u];const g=e.getColumn(u);if(g!=null&&g.accessorFn)return s._valuesCache[u]=g.accessorFn(s.original,n),s._valuesCache[u]},getUniqueValues:u=>{if(s._uniqueValuesCache.hasOwnProperty(u))return s._uniqueValuesCache[u];const g=e.getColumn(u);if(g!=null&&g.accessorFn)return g.columnDef.getUniqueValues?(s._uniqueValuesCache[u]=g.columnDef.getUniqueValues(s.original,n),s._uniqueValuesCache[u]):(s._uniqueValuesCache[u]=[s.getValue(u)],s._uniqueValuesCache[u])},renderValue:u=>{var g;return(g=s.getValue(u))!=null?g:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>gt(s.subRows,u=>u.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let u=[],g=s;for(;;){const p=g.getParentRow();if(!p)break;u.push(p),g=p}return u.reverse()},getAllCells:S(()=>[e.getAllLeafColumns()],u=>u.map(g=>dt(e,s,g,g.id)),h(e.options,"debugRows")),_getAllCellsByColumnId:S(()=>[s.getAllCells()],u=>u.reduce((g,p)=>(g[p.column.id]=p,g),{}),h(e.options,"debugRows"))};for(let u=0;u<e._features.length;u++){const g=e._features[u];g==null||g.createRow==null||g.createRow(s,e)}return s},mt={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Ae=(e,o,t)=>{var n,r;const i=t==null||(n=t.toString())==null?void 0:n.toLowerCase();return!!(!((r=e.getValue(o))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(i))};Ae.autoRemove=e=>M(e);const Ee=(e,o,t)=>{var n;return!!(!((n=e.getValue(o))==null||(n=n.toString())==null)&&n.includes(t))};Ee.autoRemove=e=>M(e);const ze=(e,o,t)=>{var n;return((n=e.getValue(o))==null||(n=n.toString())==null?void 0:n.toLowerCase())===t?.toLowerCase()};ze.autoRemove=e=>M(e);const He=(e,o,t)=>{var n;return(n=e.getValue(o))==null?void 0:n.includes(t)};He.autoRemove=e=>M(e);const Le=(e,o,t)=>!t.some(n=>{var r;return!((r=e.getValue(o))!=null&&r.includes(n))});Le.autoRemove=e=>M(e)||!(e!=null&&e.length);const Ge=(e,o,t)=>t.some(n=>{var r;return(r=e.getValue(o))==null?void 0:r.includes(n)});Ge.autoRemove=e=>M(e)||!(e!=null&&e.length);const Ne=(e,o,t)=>e.getValue(o)===t;Ne.autoRemove=e=>M(e);const Oe=(e,o,t)=>e.getValue(o)==t;Oe.autoRemove=e=>M(e);const pe=(e,o,t)=>{let[n,r]=t;const i=e.getValue(o);return i>=n&&i<=r};pe.resolveFilterValue=e=>{let[o,t]=e,n=typeof o!="number"?parseFloat(o):o,r=typeof t!="number"?parseFloat(t):t,i=o===null||Number.isNaN(n)?-1/0:n,l=t===null||Number.isNaN(r)?1/0:r;if(i>l){const s=i;i=l,l=s}return[i,l]};pe.autoRemove=e=>M(e)||M(e[0])&&M(e[1]);const $={includesString:Ae,includesStringSensitive:Ee,equalsString:ze,arrIncludes:He,arrIncludesAll:Le,arrIncludesSome:Ge,equals:Ne,weakEquals:Oe,inNumberRange:pe};function M(e){return e==null||e===""}const Ct={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:F("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t?.getValue(e.id);return typeof n=="string"?$.includesString:typeof n=="number"?$.inNumberRange:typeof n=="boolean"||n!==null&&typeof n=="object"?$.equals:Array.isArray(n)?$.arrIncludes:$.weakEquals},e.getFilterFn=()=>{var t,n;return Y(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(n=o.options.filterFns)==null?void 0:n[e.columnDef.filterFn])!=null?t:$[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,n,r;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((n=o.options.enableColumnFilters)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=o.getState().columnFilters)==null||(t=t.find(n=>n.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,n;return(t=(n=o.getState().columnFilters)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.setFilterValue=t=>{o.setColumnFilters(n=>{const r=e.getFilterFn(),i=n?.find(p=>p.id===e.id),l=I(t,i?i.value:void 0);if(xe(r,l,e)){var s;return(s=n?.filter(p=>p.id!==e.id))!=null?s:[]}const u={id:e.id,value:l};if(i){var g;return(g=n?.map(p=>p.id===e.id?u:p))!=null?g:[]}return n!=null&&n.length?[...n,u]:[u]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const t=e.getAllLeafColumns(),n=r=>{var i;return(i=I(o,r))==null?void 0:i.filter(l=>{const s=t.find(u=>u.id===l.id);if(s){const u=s.getFilterFn();if(xe(u,l.value,s))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(n)},e.resetColumnFilters=o=>{var t,n;e.setColumnFilters(o?[]:(t=(n=e.initialState)==null?void 0:n.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function xe(e,o,t){return(e&&e.autoRemove?e.autoRemove(o,t):!1)||typeof o>"u"||typeof o=="string"&&!o}const St=(e,o,t)=>t.reduce((n,r)=>{const i=r.getValue(e);return n+(typeof i=="number"?i:0)},0),ht=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}),n},vt=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}),n},Rt=(e,o,t)=>{let n,r;return t.forEach(i=>{const l=i.getValue(e);l!=null&&(n===void 0?l>=l&&(n=r=l):(n>l&&(n=l),r<l&&(r=l)))}),[n,r]},wt=(e,o)=>{let t=0,n=0;if(o.forEach(r=>{let i=r.getValue(e);i!=null&&(i=+i)>=i&&(++t,n+=i)}),t)return n/t},xt=(e,o)=>{if(!o.length)return;const t=o.map(i=>i.getValue(e));if(!at(t))return;if(t.length===1)return t[0];const n=Math.floor(t.length/2),r=t.sort((i,l)=>i-l);return t.length%2!==0?r[n]:(r[n-1]+r[n])/2},_t=(e,o)=>Array.from(new Set(o.map(t=>t.getValue(e))).values()),Ft=(e,o)=>new Set(o.map(t=>t.getValue(e))).size,Mt=(e,o)=>o.length,J={sum:St,min:ht,max:vt,extent:Rt,mean:wt,median:xt,unique:_t,uniqueCount:Ft,count:Mt},$t={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,t;return(o=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:F("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(n=>n!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,n;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((n=o.options.enableGrouping)!=null?n:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t?.getValue(e.id);if(typeof n=="number")return J.sum;if(Object.prototype.toString.call(n)==="[object Date]")return J.extent},e.getAggregationFn=()=>{var t,n;if(!e)throw new Error;return Y(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(n=o.options.aggregationFns)==null?void 0:n[e.columnDef.aggregationFn])!=null?t:J[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var t,n;e.setGrouping(o?[]:(t=(n=e.initialState)==null?void 0:n.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const n=o.getColumn(t);return n!=null&&n.columnDef.getGroupingValue?(e._groupingValuesCache[t]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,o,t,n)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=t.subRows)!=null&&r.length)}}};function yt(e,o,t){if(!(o!=null&&o.length)||!t)return e;const n=e.filter(i=>!o.includes(i.id));return t==="remove"?n:[...o.map(i=>e.find(l=>l.id===i)).filter(Boolean),...n]}const Pt={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:F("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=S(t=>[N(o,t)],t=>t.findIndex(n=>n.id===e.id),h(o.options,"debugColumns")),e.getIsFirstColumn=t=>{var n;return((n=N(o,t)[0])==null?void 0:n.id)===e.id},e.getIsLastColumn=t=>{var n;const r=N(o,t);return((n=r[r.length-1])==null?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var t;e.setColumnOrder(o?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=S(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,t,n)=>r=>{let i=[];if(!(o!=null&&o.length))i=r;else{const l=[...o],s=[...r];for(;s.length&&l.length;){const u=l.shift(),g=s.findIndex(p=>p.id===u);g>-1&&i.push(s.splice(g,1)[0])}i=[...i,...s]}return yt(i,t,n)},h(e.options,"debugTable"))}},W=()=>({left:[],right:[]}),Vt={getInitialState:e=>({columnPinning:W(),...e}),getDefaultOptions:e=>({onColumnPinningChange:F("columnPinning",e)}),createColumn:(e,o)=>{e.pin=t=>{const n=e.getLeafColumns().map(r=>r.id).filter(Boolean);o.setColumnPinning(r=>{var i,l;if(t==="right"){var s,u;return{left:((s=r?.left)!=null?s:[]).filter(C=>!(n!=null&&n.includes(C))),right:[...((u=r?.right)!=null?u:[]).filter(C=>!(n!=null&&n.includes(C))),...n]}}if(t==="left"){var g,p;return{left:[...((g=r?.left)!=null?g:[]).filter(C=>!(n!=null&&n.includes(C))),...n],right:((p=r?.right)!=null?p:[]).filter(C=>!(n!=null&&n.includes(C)))}}return{left:((i=r?.left)!=null?i:[]).filter(C=>!(n!=null&&n.includes(C))),right:((l=r?.right)!=null?l:[]).filter(C=>!(n!=null&&n.includes(C)))}})},e.getCanPin=()=>e.getLeafColumns().some(n=>{var r,i,l;return((r=n.columnDef.enablePinning)!=null?r:!0)&&((i=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(s=>s.id),{left:n,right:r}=o.getState().columnPinning,i=t.some(s=>n?.includes(s)),l=t.some(s=>r?.includes(s));return i?"left":l?"right":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();return r?(t=(n=o.getState().columnPinning)==null||(n=n[r])==null?void 0:n.indexOf(e.id))!=null?t:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(t,n,r)=>{const i=[...n??[],...r??[]];return t.filter(l=>!i.includes(l.column.id))},h(o.options,"debugRows")),e.getLeftVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),h(o.options,"debugRows")),e.getRightVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),h(o.options,"debugRows"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var t,n;return e.setColumnPinning(o?W():(t=(n=e.initialState)==null?void 0:n.columnPinning)!=null?t:W())},e.getIsSomeColumnsPinned=o=>{var t;const n=e.getState().columnPinning;if(!o){var r,i;return!!((r=n.left)!=null&&r.length||(i=n.right)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e.getLeftLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),h(e.options,"debugColumns")),e.getRightLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),h(e.options,"debugColumns")),e.getCenterLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n)=>{const r=[...t??[],...n??[]];return o.filter(i=>!r.includes(i.id))},h(e.options,"debugColumns"))}};function It(e){return e||(typeof document<"u"?document:null)}const U={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},ee=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Dt={getDefaultColumnDef:()=>U,getInitialState:e=>({columnSizing:{},columnSizingInfo:ee(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:F("columnSizing",e),onColumnSizingInfoChange:F("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var t,n,r;const i=o.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:U.minSize,(n=i??e.columnDef.size)!=null?n:U.size),(r=e.columnDef.maxSize)!=null?r:U.maxSize)},e.getStart=S(t=>[t,N(o,t),o.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((r,i)=>r+i.getSize(),0),h(o.options,"debugColumns")),e.getAfter=S(t=>[t,N(o,t),o.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((r,i)=>r+i.getSize(),0),h(o.options,"debugColumns")),e.resetSize=()=>{o.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var t,n;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((n=o.options.enableColumnResizing)!=null?n:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let t=0;const n=r=>{if(r.subHeaders.length)r.subHeaders.forEach(n);else{var i;t+=(i=r.column.getSize())!=null?i:0}};return n(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const n=o.getColumn(e.column.id),r=n?.getCanResize();return i=>{if(!n||!r||(i.persist==null||i.persist(),te(i)&&i.touches&&i.touches.length>1))return;const l=e.getSize(),s=e?e.getLeafHeaders().map(v=>[v.column.id,v.column.getSize()]):[[n.id,n.getSize()]],u=te(i)?Math.round(i.touches[0].clientX):i.clientX,g={},p=(v,_)=>{typeof _=="number"&&(o.setColumnSizingInfo(w=>{var P,j;const Z=o.options.columnResizeDirection==="rtl"?-1:1,Ce=(_-((P=w?.startOffset)!=null?P:0))*Z,Se=Math.max(Ce/((j=w?.startSize)!=null?j:0),-.999999);return w.columnSizingStart.forEach(ke=>{let[Be,he]=ke;g[Be]=Math.round(Math.max(he+he*Se,0)*100)/100}),{...w,deltaOffset:Ce,deltaPercentage:Se}}),(o.options.columnResizeMode==="onChange"||v==="end")&&o.setColumnSizing(w=>({...w,...g})))},C=v=>p("move",v),c=v=>{p("end",v),o.setColumnSizingInfo(_=>({..._,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},d=It(t),f={moveHandler:v=>C(v.clientX),upHandler:v=>{d?.removeEventListener("mousemove",f.moveHandler),d?.removeEventListener("mouseup",f.upHandler),c(v.clientX)}},m={moveHandler:v=>(v.cancelable&&(v.preventDefault(),v.stopPropagation()),C(v.touches[0].clientX),!1),upHandler:v=>{var _;d?.removeEventListener("touchmove",m.moveHandler),d?.removeEventListener("touchend",m.upHandler),v.cancelable&&(v.preventDefault(),v.stopPropagation()),c((_=v.touches[0])==null?void 0:_.clientX)}},R=jt()?{passive:!1}:!1;te(i)?(d?.addEventListener("touchmove",m.moveHandler,R),d?.addEventListener("touchend",m.upHandler,R)):(d?.addEventListener("mousemove",f.moveHandler,R),d?.addEventListener("mouseup",f.upHandler,R)),o.setColumnSizingInfo(v=>({...v,startOffset:u,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:s,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var t;e.setColumnSizing(o?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=o=>{var t;e.setColumnSizingInfo(o?ee():(t=e.initialState.columnSizingInfo)!=null?t:ee())},e.getTotalSize=()=>{var o,t;return(o=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,t;return(o=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,t;return(o=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,t;return(o=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0}}};let b=null;function jt(){if(typeof b=="boolean")return b;let e=!1;try{const o={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,o),window.removeEventListener("test",t)}catch{e=!1}return b=e,b}function te(e){return e.type==="touchstart"}const At={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:F("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=t=>{e.getCanHide()&&o.setColumnVisibility(n=>({...n,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,n;const r=e.columns;return(t=r.length?r.some(i=>i.getIsVisible()):(n=o.getState().columnVisibility)==null?void 0:n[e.id])!=null?t:!0},e.getCanHide=()=>{var t,n;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((n=o.options.enableHiding)!=null?n:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=S(()=>[e.getAllCells(),o.getState().columnVisibility],t=>t.filter(n=>n.column.getIsVisible()),h(o.options,"debugRows")),e.getVisibleCells=S(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,n,r)=>[...t,...n,...r],h(o.options,"debugRows"))},createTable:e=>{const o=(t,n)=>S(()=>[n(),n().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),h(e.options,"debugColumns"));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:(n=e.initialState.columnVisibility)!=null?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=(n=t)!=null?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,i)=>({...r,[i.id]:t||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible((n=t.target)==null?void 0:n.checked)}}};function N(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Et={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},zt={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:F("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var t;const n=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[o.id])==null?void 0:t.getValue();return typeof n=="string"||typeof n=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var t,n,r,i;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((n=o.options.enableGlobalFilter)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&((i=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>$.includesString,e.getGlobalFilterFn=()=>{var o,t;const{globalFilterFn:n}=e.options;return Y(n)?n:n==="auto"?e.getGlobalAutoFilterFn():(o=(t=e.options.filterFns)==null?void 0:t[n])!=null?o:$[n]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},Ht={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:F("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,t=!1;e._autoResetExpanded=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?n:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=n=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(n),e.toggleAllRowsExpanded=n=>{n??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=n=>{var r,i;e.setExpanded(n?{}:(r=(i=e.initialState)==null?void 0:i.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(n=>n.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>n=>{n.persist==null||n.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const n=e.getState().expanded;return n===!0||Object.values(n).some(Boolean)},e.getIsAllRowsExpanded=()=>{const n=e.getState().expanded;return typeof n=="boolean"?n===!0:!(!Object.keys(n).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let n=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const l=i.split(".");n=Math.max(n,l.length)}),n},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=t=>{o.setExpanded(n=>{var r;const i=n===!0?!0:!!(n!=null&&n[e.id]);let l={};if(n===!0?Object.keys(o.getRowModel().rowsById).forEach(s=>{l[s]=!0}):l=n,t=(r=t)!=null?r:!i,!i&&t)return{...l,[e.id]:!0};if(i&&!t){const{[e.id]:s,...u}=l;return u}return n})},e.getIsExpanded=()=>{var t;const n=o.getState().expanded;return!!((t=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?t:n===!0||n?.[e.id])},e.getCanExpand=()=>{var t,n,r;return(t=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?t:((n=o.options.enableExpanding)!=null?n:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let t=!0,n=e;for(;t&&n.parentId;)n=o.getRow(n.parentId,!0),t=n.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},ie=0,le=10,ne=()=>({pageIndex:ie,pageSize:le}),Lt={getInitialState:e=>({...e,pagination:{...ne(),...e?.pagination}}),getDefaultOptions:e=>({onPaginationChange:F("pagination",e)}),createTable:e=>{let o=!1,t=!1;e._autoResetPageIndex=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?n:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=n=>{const r=i=>I(n,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=n=>{var r;e.setPagination(n?ne():(r=e.initialState.pagination)!=null?r:ne())},e.setPageIndex=n=>{e.setPagination(r=>{let i=I(n,r.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,l)),{...r,pageIndex:i}})},e.resetPageIndex=n=>{var r,i;e.setPageIndex(n?ie:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?r:ie)},e.resetPageSize=n=>{var r,i;e.setPageSize(n?le:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?r:le)},e.setPageSize=n=>{e.setPagination(r=>{const i=Math.max(1,I(n,r.pageSize)),l=r.pageSize*r.pageIndex,s=Math.floor(l/i);return{...r,pageIndex:s,pageSize:i}})},e.setPageCount=n=>e.setPagination(r=>{var i;let l=I(n,(i=e.options.pageCount)!=null?i:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...r,pageCount:l}}),e.getPageOptions=S(()=>[e.getPageCount()],n=>{let r=[];return n&&n>0&&(r=[...new Array(n)].fill(null).map((i,l)=>l)),r},h(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:n}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:n<r-1},e.previousPage=()=>e.setPageIndex(n=>n-1),e.nextPage=()=>e.setPageIndex(n=>n+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var n;return(n=e.options.pageCount)!=null?n:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var n;return(n=e.options.rowCount)!=null?n:e.getPrePaginationRowModel().rows.length}}},oe=()=>({top:[],bottom:[]}),Gt={getInitialState:e=>({rowPinning:oe(),...e}),getDefaultOptions:e=>({onRowPinningChange:F("rowPinning",e)}),createRow:(e,o)=>{e.pin=(t,n,r)=>{const i=n?e.getLeafRows().map(u=>{let{id:g}=u;return g}):[],l=r?e.getParentRows().map(u=>{let{id:g}=u;return g}):[],s=new Set([...l,e.id,...i]);o.setRowPinning(u=>{var g,p;if(t==="bottom"){var C,c;return{top:((C=u?.top)!=null?C:[]).filter(m=>!(s!=null&&s.has(m))),bottom:[...((c=u?.bottom)!=null?c:[]).filter(m=>!(s!=null&&s.has(m))),...Array.from(s)]}}if(t==="top"){var d,f;return{top:[...((d=u?.top)!=null?d:[]).filter(m=>!(s!=null&&s.has(m))),...Array.from(s)],bottom:((f=u?.bottom)!=null?f:[]).filter(m=>!(s!=null&&s.has(m)))}}return{top:((g=u?.top)!=null?g:[]).filter(m=>!(s!=null&&s.has(m))),bottom:((p=u?.bottom)!=null?p:[]).filter(m=>!(s!=null&&s.has(m)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:n,enablePinning:r}=o.options;return typeof n=="function"?n(e):(t=n??r)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:n,bottom:r}=o.getState().rowPinning,i=t.some(s=>n?.includes(s)),l=t.some(s=>r?.includes(s));return i?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();if(!r)return-1;const i=(t=r==="top"?o.getTopRows():o.getBottomRows())==null?void 0:t.map(l=>{let{id:s}=l;return s});return(n=i?.indexOf(e.id))!=null?n:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var t,n;return e.setRowPinning(o?oe():(t=(n=e.initialState)==null?void 0:n.rowPinning)!=null?t:oe())},e.getIsSomeRowsPinned=o=>{var t;const n=e.getState().rowPinning;if(!o){var r,i;return!!((r=n.top)!=null&&r.length||(i=n.bottom)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e._getPinnedRows=(o,t,n)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(t??[]).map(l=>{const s=e.getRow(l,!0);return s.getIsAllParentsExpanded()?s:null}):(t??[]).map(l=>o.find(s=>s.id===l))).filter(Boolean).map(l=>({...l,position:n}))},e.getTopRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,t)=>e._getPinnedRows(o,t,"top"),h(e.options,"debugRows")),e.getBottomRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,t)=>e._getPinnedRows(o,t,"bottom"),h(e.options,"debugRows")),e.getCenterRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,t,n)=>{const r=new Set([...t??[],...n??[]]);return o.filter(i=>!r.has(i.id))},h(e.options,"debugRows"))}},Nt={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:F("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var t;return e.setRowSelection(o?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(t=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const n={...t},r=e.getPreGroupedRowModel().flatRows;return o?r.forEach(i=>{i.getCanSelect()&&(n[i.id]=!0)}):r.forEach(i=>{delete n[i.id]}),n})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(t=>{const n=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),r={...t};return e.getRowModel().rows.forEach(i=>{se(r,i.id,n,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=S(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,t)=>Object.keys(o).length?re(e,t):{rows:[],flatRows:[],rowsById:{}},h(e.options,"debugTable")),e.getFilteredSelectedRowModel=S(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,t)=>Object.keys(o).length?re(e,t):{rows:[],flatRows:[],rowsById:{}},h(e.options,"debugTable")),e.getGroupedSelectedRowModel=S(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,t)=>Object.keys(o).length?re(e,t):{rows:[],flatRows:[],rowsById:{}},h(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let n=!!(o.length&&Object.keys(t).length);return n&&o.some(r=>r.getCanSelect()&&!t[r.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:t}=e.getState();let n=!!o.length;return n&&o.some(r=>!t[r.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var o;const t=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(t,n)=>{const r=e.getIsSelected();o.setRowSelection(i=>{var l;if(t=typeof t<"u"?t:!r,e.getCanSelect()&&r===t)return i;const s={...i};return se(s,e.id,t,(l=n?.selectChildren)!=null?l:!0,o),s})},e.getIsSelected=()=>{const{rowSelection:t}=o.getState();return fe(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=o.getState();return ue(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=o.getState();return ue(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(t=o.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(t=o.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(t=o.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected((r=n.target)==null?void 0:r.checked)}}}},se=(e,o,t,n,r)=>{var i;const l=r.getRow(o,!0);t?(l.getCanMultiSelect()||Object.keys(e).forEach(s=>delete e[s]),l.getCanSelect()&&(e[o]=!0)):delete e[o],n&&(i=l.subRows)!=null&&i.length&&l.getCanSelectSubRows()&&l.subRows.forEach(s=>se(e,s.id,t,n,r))};function re(e,o){const t=e.getState().rowSelection,n=[],r={},i=function(l,s){return l.map(u=>{var g;const p=fe(u,t);if(p&&(n.push(u),r[u.id]=u),(g=u.subRows)!=null&&g.length&&(u={...u,subRows:i(u.subRows)}),p)return u}).filter(Boolean)};return{rows:i(o.rows),flatRows:n,rowsById:r}}function fe(e,o){var t;return(t=o[e.id])!=null?t:!1}function ue(e,o,t){var n;if(!((n=e.subRows)!=null&&n.length))return!1;let r=!0,i=!1;return e.subRows.forEach(l=>{if(!(i&&!r)&&(l.getCanSelect()&&(fe(l,o)?i=!0:r=!1),l.subRows&&l.subRows.length)){const s=ue(l,o);s==="all"?i=!0:(s==="some"&&(i=!0),r=!1)}}),r?"all":i?"some":!1}const ae=/([0-9]+)/gm,Ot=(e,o,t)=>Te(D(e.getValue(t)).toLowerCase(),D(o.getValue(t)).toLowerCase()),Tt=(e,o,t)=>Te(D(e.getValue(t)),D(o.getValue(t))),qt=(e,o,t)=>me(D(e.getValue(t)).toLowerCase(),D(o.getValue(t)).toLowerCase()),kt=(e,o,t)=>me(D(e.getValue(t)),D(o.getValue(t))),Bt=(e,o,t)=>{const n=e.getValue(t),r=o.getValue(t);return n>r?1:n<r?-1:0},Ut=(e,o,t)=>me(e.getValue(t),o.getValue(t));function me(e,o){return e===o?0:e>o?1:-1}function D(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Te(e,o){const t=e.split(ae).filter(Boolean),n=o.split(ae).filter(Boolean);for(;t.length&&n.length;){const r=t.shift(),i=n.shift(),l=parseInt(r,10),s=parseInt(i,10),u=[l,s].sort();if(isNaN(u[0])){if(r>i)return 1;if(i>r)return-1;continue}if(isNaN(u[1]))return isNaN(l)?-1:1;if(l>s)return 1;if(s>l)return-1}return t.length-n.length}const G={alphanumeric:Ot,alphanumericCaseSensitive:Tt,text:qt,textCaseSensitive:kt,datetime:Bt,basic:Ut},bt={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:F("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const t=o.getFilteredRowModel().flatRows.slice(10);let n=!1;for(const r of t){const i=r?.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return G.datetime;if(typeof i=="string"&&(n=!0,i.split(ae).length>1))return G.alphanumeric}return n?G.text:G.basic},e.getAutoSortDir=()=>{const t=o.getFilteredRowModel().flatRows[0];return typeof t?.getValue(e.id)=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,n;if(!e)throw new Error;return Y(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(n=o.options.sortingFns)==null?void 0:n[e.columnDef.sortingFn])!=null?t:G[e.columnDef.sortingFn]},e.toggleSorting=(t,n)=>{const r=e.getNextSortingOrder(),i=typeof t<"u"&&t!==null;o.setSorting(l=>{const s=l?.find(d=>d.id===e.id),u=l?.findIndex(d=>d.id===e.id);let g=[],p,C=i?t:r==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&n?s?p="toggle":p="add":l!=null&&l.length&&u!==l.length-1?p="replace":s?p="toggle":p="replace",p==="toggle"&&(i||r||(p="remove")),p==="add"){var c;g=[...l,{id:e.id,desc:C}],g.splice(0,g.length-((c=o.options.maxMultiSortColCount)!=null?c:Number.MAX_SAFE_INTEGER))}else p==="toggle"?g=l.map(d=>d.id===e.id?{...d,desc:C}:d):p==="remove"?g=l.filter(d=>d.id!==e.id):g=[{id:e.id,desc:C}];return g})},e.getFirstSortDir=()=>{var t,n;return((t=(n=e.columnDef.sortDescFirst)!=null?n:o.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var n,r;const i=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==i&&((n=o.options.enableSortingRemoval)==null||n)&&(!(t&&(r=o.options.enableMultiRemove)!=null)||r)?!1:l==="desc"?"asc":"desc":i},e.getCanSort=()=>{var t,n;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((n=o.options.enableSorting)!=null?n:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,n;return(t=(n=e.columnDef.enableMultiSort)!=null?n:o.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const n=(t=o.getState().sorting)==null?void 0:t.find(r=>r.id===e.id);return n?n.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,n;return(t=(n=o.getState().sorting)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.clearSorting=()=>{o.setSorting(t=>t!=null&&t.length?t.filter(n=>n.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return n=>{t&&(n.persist==null||n.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(n):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var t,n;e.setSorting(o?[]:(t=(n=e.initialState)==null?void 0:n.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},Kt=[pt,At,Pt,Vt,mt,Ct,Et,zt,bt,$t,Ht,Lt,Gt,Nt,Dt];function Xt(e){var o,t;const n=[...Kt,...(o=e._features)!=null?o:[]];let r={_features:n};const i=r._features.reduce((c,d)=>Object.assign(c,d.getDefaultOptions==null?void 0:d.getDefaultOptions(r)),{}),l=c=>r.options.mergeOptions?r.options.mergeOptions(i,c):{...i,...c};let u={...{},...(t=e.initialState)!=null?t:{}};r._features.forEach(c=>{var d;u=(d=c.getInitialState==null?void 0:c.getInitialState(u))!=null?d:u});const g=[];let p=!1;const C={_features:n,options:{...i,...e},initialState:u,_queue:c=>{g.push(c),p||(p=!0,Promise.resolve().then(()=>{for(;g.length;)g.shift()();p=!1}).catch(d=>setTimeout(()=>{throw d})))},reset:()=>{r.setState(r.initialState)},setOptions:c=>{const d=I(c,r.options);r.options=l(d)},getState:()=>r.options.state,setState:c=>{r.options.onStateChange==null||r.options.onStateChange(c)},_getRowId:(c,d,f)=>{var m;return(m=r.options.getRowId==null?void 0:r.options.getRowId(c,d,f))!=null?m:`${f?[f.id,d].join("."):d}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(c,d)=>{let f=(d?r.getPrePaginationRowModel():r.getRowModel()).rowsById[c];if(!f&&(f=r.getCoreRowModel().rowsById[c],!f))throw new Error;return f},_getDefaultColumnDef:S(()=>[r.options.defaultColumn],c=>{var d;return c=(d=c)!=null?d:{},{header:f=>{const m=f.header.column.columnDef;return m.accessorKey?m.accessorKey:m.accessorFn?m.id:null},cell:f=>{var m,R;return(m=(R=f.renderValue())==null||R.toString==null?void 0:R.toString())!=null?m:null},...r._features.reduce((f,m)=>Object.assign(f,m.getDefaultColumnDef==null?void 0:m.getDefaultColumnDef()),{}),...c}},h(e,"debugColumns")),_getColumnDefs:()=>r.options.columns,getAllColumns:S(()=>[r._getColumnDefs()],c=>{const d=function(f,m,R){return R===void 0&&(R=0),f.map(v=>{const _=ct(r,v,R,m),w=v;return _.columns=w.columns?d(w.columns,_,R+1):[],_})};return d(c)},h(e,"debugColumns")),getAllFlatColumns:S(()=>[r.getAllColumns()],c=>c.flatMap(d=>d.getFlatColumns()),h(e,"debugColumns")),_getAllFlatColumnsById:S(()=>[r.getAllFlatColumns()],c=>c.reduce((d,f)=>(d[f.id]=f,d),{}),h(e,"debugColumns")),getAllLeafColumns:S(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(c,d)=>{let f=c.flatMap(m=>m.getLeafColumns());return d(f)},h(e,"debugColumns")),getColumn:c=>r._getAllFlatColumnsById()[c]};Object.assign(r,C);for(let c=0;c<r._features.length;c++){const d=r._features[c];d==null||d.createTable==null||d.createTable(r)}return r}function Yt(){return e=>S(()=>[e.options.data],o=>{const t={rows:[],flatRows:[],rowsById:{}},n=function(r,i,l){i===void 0&&(i=0);const s=[];for(let g=0;g<r.length;g++){const p=ft(e,e._getRowId(r[g],g,l),r[g],g,i,void 0,l?.id);if(t.flatRows.push(p),t.rowsById[p.id]=p,s.push(p),e.options.getSubRows){var u;p.originalSubRows=e.options.getSubRows(r[g],g),(u=p.originalSubRows)!=null&&u.length&&(p.subRows=n(p.originalSubRows,i+1,p))}}return s};return t.rows=n(o),t},h(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function _e(e,o){return e?Qt(e)?y.createElement(e,o):e:null}function Qt(e){return Zt(e)||typeof e=="function"||Jt(e)}function Zt(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function Jt(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function Wt(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=y.useState(()=>({current:Xt(o)})),[n,r]=y.useState(()=>t.current.initialState);return t.current.setOptions(i=>({...i,...e,state:{...n,...e.state},onStateChange:l=>{r(l),e.onStateChange==null||e.onStateChange(l)}})),t.current}function en({table:e}){return a.jsxs("table",{className:"table",children:[a.jsx("thead",{children:e.getHeaderGroups().map(o=>a.jsx("tr",{children:o.headers.map(t=>a.jsx("th",{children:t.isPlaceholder?null:_e(t.column.columnDef.header,t.getContext())},t.id))},o.id))}),a.jsx("tbody",{children:e.getRowModel().rows.map(o=>a.jsx("tr",{children:o.getVisibleCells().map(t=>a.jsx("td",{children:_e(t.column.columnDef.cell,t.getContext())},t.id))},o.id))})]})}function Q({onClose:e}){return a.jsx("button",{type:"button",className:"btn btn-sm btn-circle btn-ghost absolute top-2 right-2",onClick:e,children:"✕"})}function tn(){const e=T(),{client:o}=e,t=ge(),n=X(e).queryKey;return ce({mutationFn:r=>q.runPromise(o.delete(r)),onSuccess:(r,i)=>{t.setQueryData(n,l=>de(l??[],s=>{const u=s.findIndex(g=>g.id===i);u!==-1&&s.splice(u,1)}))}})}function nn({isOpen:e,setIsOpen:o,client:t}){const{mutate:n}=tn();return a.jsx("div",{className:K("modal",e&&"modal-open"),children:a.jsxs("div",{className:"modal-box",children:[a.jsx(Q,{onClose:()=>o(!1)}),a.jsx("h3",{className:"font-bold text-lg",children:"Eliminar cliente"}),a.jsx("p",{children:"¿Estás seguro de que quieres eliminar este cliente?"}),a.jsxs("p",{className:"mt-2 text-gray-600 text-sm",children:["Cliente: ",t.person.name," ",t.person.fatherLastName," ",t.person.motherLastName]}),a.jsxs("div",{className:"modal-action",children:[a.jsx("button",{type:"button",className:"btn btn-primary",onClick:()=>o(!1),children:"Cancelar"}),a.jsx("button",{type:"button",className:"btn btn-error",onClick:()=>{n(t.id,{onSuccess:()=>{H.success("Cliente eliminado"),o(!1)},onError:r=>{console.log(r),H.error("Error al eliminar cliente")}})},children:"Eliminar"})]})]})})}function Fe({open:e=!1,title:o,text:t}){const[n,r]=y.useState(e);return a.jsx("div",{className:K("modal",e&&"modal-open"),children:a.jsxs("div",{className:"modal-box",children:[a.jsx(Q,{onClose:()=>r(!1)}),a.jsx("h3",{className:"font-bold text-lg",children:o}),a.jsx("p",{children:t})]})})}function on(){const e=T(),{client:o}=e,t=ge(),n=X(e).queryKey;return ce({mutationFn:r=>q.runPromise(o.update(r)),onSuccess:(r,i)=>{t.setQueryData(n,l=>de(l??[],s=>{const u=s.findIndex(g=>g.id===i.id);u!==-1&&(s[u]={...s[u],person:i.person,updatedAt:new Date().toISOString()})}))}})}const qe=Ue({name:L(V("Debe ingresar un nombre"),k(1,"Debe tener al menos un caracter")),fatherLastName:L(V("Debe ingresar un apellido"),k(1,"Debe tener al menos un caracter")),motherLastName:L(V("Debe ingresar un apellido"),k(1,"Debe tener al menos un caracter")),email:Ye([L(V("Debe ingresar un email"),Ze("Debe ingresar un email valido")),Qe("")]),address:ve(V()),phone:ve(V()),birthDate:Xe(V()),gender:Ke(),document:L(V("Debe ingresar un documento"),k(8,"Debe tener al menos 8 caracteres")),documentType:be()});function rn({setIsOpen:e,client:o}){const{mutate:t}=on(),{id:n,person:{id:r,...i},...l}=o,s=ye({defaultValues:{...i},validators:{onChange:qe},onSubmit:({value:g})=>{t({id:n,person:{...g,id:r}},{onSuccess:()=>{H.success("Cliente actualizado"),u()},onError:p=>{console.log(p);const{error:C}=O(p);H.error(C.message)}})}});function u(){s.reset(),e(!1)}return{form:s,handleClose:u}}function ln({isOpen:e,setIsOpen:o,client:t}){const{form:n,handleClose:r}=rn({setIsOpen:o,client:t});return a.jsx("div",{className:K("modal",e&&"modal-open"),children:a.jsxs("div",{className:"modal-box",children:[a.jsx(Q,{onClose:r}),a.jsx("h3",{className:"font-bold text-lg",children:"Editar Cliente"}),a.jsx("form",{onSubmit:i=>{i.preventDefault(),n.handleSubmit()},children:a.jsxs(n.AppForm,{children:[a.jsxs("fieldset",{className:"fieldset",children:[a.jsx(n.AppField,{name:"name",children:({FSTextField:i})=>a.jsx(i,{label:"Nombre",placeholder:"Nombre",prefixComponent:a.jsx(z,{size:16})})}),a.jsx(n.AppField,{name:"fatherLastName",children:({FSTextField:i})=>a.jsx(i,{label:"Apellido Paterno",placeholder:"Apellido Paterno",prefixComponent:a.jsx(z,{size:16})})}),a.jsx(n.AppField,{name:"motherLastName",children:({FSTextField:i})=>a.jsx(i,{label:"Apellido Materno",placeholder:"Apellido Materno",prefixComponent:a.jsx(z,{size:16})})}),a.jsx(n.AppField,{name:"email",children:({FSTextField:i})=>a.jsx(i,{label:"Correo Electrónico",placeholder:"<EMAIL>",prefixComponent:a.jsx(Ie,{size:16})})}),a.jsx(n.AppField,{name:"address",children:({FSTextField:i})=>a.jsx(i,{label:"Dirección",placeholder:"Dirección",prefixComponent:a.jsx(De,{size:16})})}),a.jsx(n.AppField,{name:"phone",children:({FSTextField:i})=>a.jsx(i,{label:"Teléfono",placeholder:"Teléfono",prefixComponent:a.jsx(je,{size:16})})}),a.jsx(n.AppField,{name:"birthDate",children:({FSTextField:i})=>a.jsx(i,{label:"Fecha de Nacimiento",placeholder:"YYYY-MM-DD",type:"date",prefixComponent:a.jsx(Pe,{size:16})})}),a.jsx(n.AppField,{name:"document",children:({FSTextField:i})=>a.jsx(i,{label:"Documento",placeholder:"Número de Documento",prefixComponent:a.jsx(Ve,{size:16})})}),a.jsx(n.AppField,{name:"documentType",children:({FSSelectField:i})=>a.jsx(i,{label:"Tipo de Documento",placeholder:"Tipo de Documento",isNumber:!0,options:[{value:0,label:"DNI"},{value:1,label:"Pasaporte"},{value:2,label:"RUC"}]})}),a.jsx(n.AppField,{name:"gender",children:({FSToggleField:i})=>a.jsx(i,{label:"Género",trueLabel:"Masculino",falseLabel:"Femenino"})})]}),a.jsx("div",{className:"modal-action",children:a.jsx("button",{type:"submit",className:"btn btn-primary",children:"Actualizar"})})]})})]})})}function sn({isOpen:e,setIsOpen:o,id:t}){const n=T(),{data:r,isError:i,error:l,isPending:s}=$e({...st(n,t),enabled:e});return y.useEffect(()=>{l&&console.log(l)},[l]),s?a.jsx(Fe,{text:"Cargando..."}):i?a.jsx(Fe,{text:"No se pudo cargar el cliente",title:O(l).error.code.toString()}):a.jsx(ln,{isOpen:e,setIsOpen:o,client:r})}const A=ut(),un=[A.accessor("person.name",{header:"Nombre",cell:e=>e.getValue()}),A.accessor("person.fatherLastName",{header:"Apellido Paterno",cell:e=>e.getValue()}),A.accessor("person.motherLastName",{header:"Apellido Materno",cell:e=>e.getValue()}),A.accessor("person.email",{header:"Email",cell:e=>e.getValue()||"N/A"}),A.accessor("person.phone",{header:"Teléfono",cell:e=>e.getValue()||"N/A"}),A.accessor("person.document",{header:"Documento",cell:e=>{const o=e.row.original.person.documentType,t=e.getValue();return`${o===Re.DNI?"DNI":o===Re.PASAPORTE?"Pasaporte":"RUC"}: ${t}`}}),A.display({id:"actions",header:"Acciones",cell:({row:e})=>{const[o,t]=y.useState(!1),[n,r]=y.useState(!1),i=e.original;return a.jsxs("div",{className:"flex gap-2",children:[a.jsx("button",{type:"button",className:"btn btn-sm btn-primary",onClick:()=>t(!0),children:a.jsx(rt,{size:16})}),a.jsx("button",{type:"button",className:"btn btn-sm btn-error",onClick:()=>r(!0),children:a.jsx(lt,{size:16})}),a.jsx(sn,{isOpen:o,setIsOpen:t,id:i.id}),a.jsx(nn,{isOpen:n,setIsOpen:r,client:i})]})}})];function an({clients:e}){const o=Wt({data:e,columns:un,getCoreRowModel:Yt()});return a.jsx(en,{table:o})}function gn(){const e=T(),{data:o,isError:t,error:n,isPending:r}=$e(X(e));return y.useEffect(()=>{n&&console.log(O(n).error)},[n]),t?a.jsxs("div",{children:["Error: ",O(n).error.message]}):r?a.jsx("div",{children:"Loading..."}):a.jsx(an,{clients:o})}function dn(){const e=T(),{client:o}=e,t=ge(),n=X(e).queryKey;return ce({mutationFn:r=>q.runPromise(o.create(r)),onSuccess:(r,i)=>{t.setQueryData(n,l=>de(l??[],s=>{s.push({id:r,person:i.person,createdAt:new Date().toISOString(),updatedAt:null,deletedAt:null})}))}})}const cn={name:"",fatherLastName:"",motherLastName:"",email:"",address:"",phone:"",birthDate:"",gender:!1,document:"",documentType:0};function pn({setIsOpen:e}){const{mutate:o}=dn(),t=ye({defaultValues:cn,validators:{onChange:qe},onSubmit:({value:r})=>{o({person:r},{onSuccess:()=>{H.success("Cliente creado"),n()},onError:i=>{console.log(i);const{error:l}=O(i);H.error(l.message)}})}});function n(){t.reset(),e(!1)}return{form:t,handleClose:n}}function fn({isOpen:e,setIsOpen:o}){const{form:t,handleClose:n}=pn({setIsOpen:o});return a.jsx("div",{className:K("modal",e&&"modal-open"),children:a.jsxs("div",{className:"modal-box",children:[a.jsx(Q,{onClose:n}),a.jsx("h3",{className:"font-bold text-lg",children:"Crear Cliente"}),a.jsx("form",{onSubmit:r=>{r.preventDefault(),t.handleSubmit()},children:a.jsxs(t.AppForm,{children:[a.jsxs("fieldset",{className:"fieldset",children:[a.jsx(t.AppField,{name:"name",children:({FSTextField:r})=>a.jsx(r,{label:"Nombre",placeholder:"Nombre",prefixComponent:a.jsx(z,{size:16})})}),a.jsx(t.AppField,{name:"fatherLastName",children:({FSTextField:r})=>a.jsx(r,{label:"Apellido Paterno",placeholder:"Apellido Paterno",prefixComponent:a.jsx(z,{size:16})})}),a.jsx(t.AppField,{name:"motherLastName",children:({FSTextField:r})=>a.jsx(r,{label:"Apellido Materno",placeholder:"Apellido Materno",prefixComponent:a.jsx(z,{size:16})})}),a.jsx(t.AppField,{name:"email",children:({FSTextField:r})=>a.jsx(r,{label:"Correo Electrónico",placeholder:"<EMAIL>",prefixComponent:a.jsx(Ie,{size:16})})}),a.jsx(t.AppField,{name:"address",children:({FSTextField:r})=>a.jsx(r,{label:"Dirección",placeholder:"Dirección",prefixComponent:a.jsx(De,{size:16})})}),a.jsx(t.AppField,{name:"phone",children:({FSTextField:r})=>a.jsx(r,{label:"Teléfono",placeholder:"Teléfono",prefixComponent:a.jsx(je,{size:16})})}),a.jsx(t.AppField,{name:"birthDate",children:({FSTextField:r})=>a.jsx(r,{label:"Fecha de Nacimiento",placeholder:"YYYY-MM-DD",type:"date",prefixComponent:a.jsx(Pe,{size:16})})}),a.jsx(t.AppField,{name:"document",children:({FSTextField:r})=>a.jsx(r,{label:"Documento",placeholder:"Número de Documento",prefixComponent:a.jsx(Ve,{size:16})})}),a.jsx(t.AppField,{name:"documentType",children:({FSSelectField:r})=>a.jsx(r,{label:"Tipo de Documento",placeholder:"Tipo de Documento",isNumber:!0,options:[{value:0,label:"DNI"},{value:1,label:"Pasaporte"},{value:2,label:"RUC"}]})}),a.jsx(t.AppField,{name:"gender",children:({FSToggleField:r})=>a.jsx(r,{label:"Género",trueLabel:"Masculino",falseLabel:"Femenino"})})]}),a.jsx("div",{className:"modal-action",children:a.jsx("button",{type:"submit",className:"btn btn-primary",children:"Crear"})})]})})]})})}const wn=function(){const[o,t]=y.useState(!1);return a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"container mx-auto",children:a.jsx("div",{className:"card bg-base-300",children:a.jsxs("div",{className:"card-body",children:[a.jsx("div",{children:a.jsx("button",{type:"button",className:"btn btn-primary",onClick:()=>t(!0),children:"Nuevo cliente"})}),a.jsx(gn,{})]})})}),a.jsx(fn,{isOpen:o,setIsOpen:t})]})};export{wn as component};
