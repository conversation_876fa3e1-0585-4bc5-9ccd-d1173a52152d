import{u as m,j as e,a as d,S as p,b as h,y as x}from"./client--LJeqQnE.js";import{c as b,u as f}from"./form-vCGFeEBS.js";import{g}from"./effectErrors-DvJbF9aL.js";import{u as j,U as v}from"./user-8OiDO9RD.js";import{A as w,o as S,p as o,m as l,s as c}from"./runtimes-RPMEsgVm.js";const y=["light","dark","cupcake","bumblebee","emerald","corporate","synthwave","retro","cyberpunk","valentine","halloween","garden","forest","aqua","lofi","pastel","fantasy","wireframe","black","luxury","dracula","cmyk","autumn","business","acid","lemonade","night","coffee","winter","dim","nord","sunset","caramellatte","abyss","silk"];function N({className:a}){const{setTheme:n,theme:r}=m();return e.jsx("select",{defaultValue:"Elige un tema",className:b("select",a),value:r,onChange:s=>n(s.target.value),children:y.map(s=>e.jsx("option",{value:s,children:s},s))})}function A(){const{auth:a}=d();return j({mutationKey:["login"],mutationFn:n=>w.runPromise(a.login(n))})}const i=new p(void 0),C={setUser:a=>i.setState(()=>a),clearUser:()=>i.setState(()=>{})},k=S({username:o(c("Debe ingresar una cuenta"),l(4,"Debe tener al menos 4 caracteres")),password:o(c("Debe ingresar su contraseña"),l(4,"Debe tener al menos 4 caracteres"))});function F(){const{mutateAsync:a}=A(),n=h(),r=f({defaultValues:{username:"",password:""},onSubmit:async({value:s})=>{a(s,{onSuccess(t){C.setUser(t.user),n({to:"/admin"})},onError(t){const u=g(t);x.error(u.error.message)}})},validators:{onChange:k}});return e.jsx("form",{onSubmit:s=>{s.preventDefault(),r.handleSubmit()},children:e.jsxs(r.AppForm,{children:[e.jsx(r.AppField,{name:"username",children:({FSTextField:s})=>e.jsx(s,{label:"Usuario o Correo",placeholder:"<EMAIL>",prefixComponent:e.jsx(v,{size:16})})}),e.jsx(r.AppField,{name:"password",children:({FSPasswordField:s})=>e.jsx(s,{label:"Contraseña",placeholder:"Contraseña"})}),e.jsx(r.SubscribeButton,{label:"Iniciar sesión",className:"btn btn-neutral mt-4 w-full"})]})})}const T=function(){return e.jsx("div",{className:"hero min-h-screen bg-base-200",children:e.jsxs("div",{className:"hero-content flex-col lg:flex-row-reverse",children:[e.jsxs("div",{className:"text-center lg:text-left",children:[e.jsx("h1",{className:"font-bold text-5xl",children:"¡Inicia sesión ahora!"}),e.jsx("p",{className:"py-6",children:"Accede a tu cuenta para gestionar tus ventas, controlar tu inventario y optimizar tus procesos de manufactura. ¡Todo en un solo lugar con FHYONA!"})]}),e.jsx("div",{className:"card w-full max-w-sm shrink-0 bg-base-100 shadow-2xl",children:e.jsxs("div",{className:"card-body",children:[e.jsx(F,{}),e.jsx(N,{className:"w-full"})]})})]})})};export{T as component};
