const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/login-BXzM-CoY.js","assets/form-vCGFeEBS.js","assets/user-8OiDO9RD.js","assets/effectErrors-DvJbF9aL.js","assets/runtimes-RPMEsgVm.js","assets/route-BCGlW2to.js","assets/queryOptions-DcASEHgW.js","assets/route-r-1Gfi_b.js","assets/index-DPvDv-Mn.js"])))=>i.map(i=>d[i]);
function nA(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var np={exports:{}},cl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tS;function aA(){if(tS)return cl;tS=1;var t=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function r(i,l,u){var f=null;if(u!==void 0&&(f=""+u),l.key!==void 0&&(f=""+l.key),"key"in l){u={};for(var h in l)h!=="key"&&(u[h]=l[h])}else u=l;return l=u.ref,{$$typeof:t,type:i,key:f,ref:l!==void 0?l:null,props:u}}return cl.Fragment=n,cl.jsx=r,cl.jsxs=r,cl}var eS;function rA(){return eS||(eS=1,np.exports=aA()),np.exports}var W=rA(),ap={exports:{}},ul={},rp={exports:{}},sp={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nS;function sA(){return nS||(nS=1,function(t){function n(C,I){var st=C.length;C.push(I);t:for(;0<st;){var bt=st-1>>>1,T=C[bt];if(0<l(T,I))C[bt]=I,C[st]=T,st=bt;else break t}}function r(C){return C.length===0?null:C[0]}function i(C){if(C.length===0)return null;var I=C[0],st=C.pop();if(st!==I){C[0]=st;t:for(var bt=0,T=C.length,F=T>>>1;bt<F;){var nt=2*(bt+1)-1,J=C[nt],et=nt+1,ft=C[et];if(0>l(J,st))et<T&&0>l(ft,J)?(C[bt]=ft,C[et]=st,bt=et):(C[bt]=J,C[nt]=st,bt=nt);else if(et<T&&0>l(ft,st))C[bt]=ft,C[et]=st,bt=et;else break t}}return I}function l(C,I){var st=C.sortIndex-I.sortIndex;return st!==0?st:C.id-I.id}if(t.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var u=performance;t.unstable_now=function(){return u.now()}}else{var f=Date,h=f.now();t.unstable_now=function(){return f.now()-h}}var m=[],p=[],g=1,v=null,b=3,O=!1,R=!1,A=!1,j=!1,U=typeof setTimeout=="function"?setTimeout:null,B=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate<"u"?setImmediate:null;function G(C){for(var I=r(p);I!==null;){if(I.callback===null)i(p);else if(I.startTime<=C)i(p),I.sortIndex=I.expirationTime,n(m,I);else break;I=r(p)}}function Z(C){if(A=!1,G(C),!R)if(r(m)!==null)R=!0,K||(K=!0,Y());else{var I=r(p);I!==null&&ut(Z,I.startTime-C)}}var K=!1,Q=-1,N=5,L=-1;function rt(){return j?!0:!(t.unstable_now()-L<N)}function tt(){if(j=!1,K){var C=t.unstable_now();L=C;var I=!0;try{t:{R=!1,A&&(A=!1,B(Q),Q=-1),O=!0;var st=b;try{e:{for(G(C),v=r(m);v!==null&&!(v.expirationTime>C&&rt());){var bt=v.callback;if(typeof bt=="function"){v.callback=null,b=v.priorityLevel;var T=bt(v.expirationTime<=C);if(C=t.unstable_now(),typeof T=="function"){v.callback=T,G(C),I=!0;break e}v===r(m)&&i(m),G(C)}else i(m);v=r(m)}if(v!==null)I=!0;else{var F=r(p);F!==null&&ut(Z,F.startTime-C),I=!1}}break t}finally{v=null,b=st,O=!1}I=void 0}}finally{I?Y():K=!1}}}var Y;if(typeof q=="function")Y=function(){q(tt)};else if(typeof MessageChannel<"u"){var it=new MessageChannel,St=it.port2;it.port1.onmessage=tt,Y=function(){St.postMessage(null)}}else Y=function(){U(tt,0)};function ut(C,I){Q=U(function(){C(t.unstable_now())},I)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(C){C.callback=null},t.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<C?Math.floor(1e3/C):5},t.unstable_getCurrentPriorityLevel=function(){return b},t.unstable_next=function(C){switch(b){case 1:case 2:case 3:var I=3;break;default:I=b}var st=b;b=I;try{return C()}finally{b=st}},t.unstable_requestPaint=function(){j=!0},t.unstable_runWithPriority=function(C,I){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var st=b;b=C;try{return I()}finally{b=st}},t.unstable_scheduleCallback=function(C,I,st){var bt=t.unstable_now();switch(typeof st=="object"&&st!==null?(st=st.delay,st=typeof st=="number"&&0<st?bt+st:bt):st=bt,C){case 1:var T=-1;break;case 2:T=250;break;case 5:T=1073741823;break;case 4:T=1e4;break;default:T=5e3}return T=st+T,C={id:g++,callback:I,priorityLevel:C,startTime:st,expirationTime:T,sortIndex:-1},st>bt?(C.sortIndex=st,n(p,C),r(m)===null&&C===r(p)&&(A?(B(Q),Q=-1):A=!0,ut(Z,st-bt))):(C.sortIndex=T,n(m,C),R||O||(R=!0,K||(K=!0,Y()))),C},t.unstable_shouldYield=rt,t.unstable_wrapCallback=function(C){var I=b;return function(){var st=b;b=I;try{return C.apply(this,arguments)}finally{b=st}}}}(sp)),sp}var aS;function iA(){return aS||(aS=1,rp.exports=sA()),rp.exports}var ip={exports:{}},Mt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rS;function oA(){if(rS)return Mt;rS=1;var t=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function b(T){return T===null||typeof T!="object"?null:(T=v&&T[v]||T["@@iterator"],typeof T=="function"?T:null)}var O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,A={};function j(T,F,nt){this.props=T,this.context=F,this.refs=A,this.updater=nt||O}j.prototype.isReactComponent={},j.prototype.setState=function(T,F){if(typeof T!="object"&&typeof T!="function"&&T!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,T,F,"setState")},j.prototype.forceUpdate=function(T){this.updater.enqueueForceUpdate(this,T,"forceUpdate")};function U(){}U.prototype=j.prototype;function B(T,F,nt){this.props=T,this.context=F,this.refs=A,this.updater=nt||O}var q=B.prototype=new U;q.constructor=B,R(q,j.prototype),q.isPureReactComponent=!0;var G=Array.isArray,Z={H:null,A:null,T:null,S:null,V:null},K=Object.prototype.hasOwnProperty;function Q(T,F,nt,J,et,ft){return nt=ft.ref,{$$typeof:t,type:T,key:F,ref:nt!==void 0?nt:null,props:ft}}function N(T,F){return Q(T.type,F,void 0,void 0,void 0,T.props)}function L(T){return typeof T=="object"&&T!==null&&T.$$typeof===t}function rt(T){var F={"=":"=0",":":"=2"};return"$"+T.replace(/[=:]/g,function(nt){return F[nt]})}var tt=/\/+/g;function Y(T,F){return typeof T=="object"&&T!==null&&T.key!=null?rt(""+T.key):F.toString(36)}function it(){}function St(T){switch(T.status){case"fulfilled":return T.value;case"rejected":throw T.reason;default:switch(typeof T.status=="string"?T.then(it,it):(T.status="pending",T.then(function(F){T.status==="pending"&&(T.status="fulfilled",T.value=F)},function(F){T.status==="pending"&&(T.status="rejected",T.reason=F)})),T.status){case"fulfilled":return T.value;case"rejected":throw T.reason}}throw T}function ut(T,F,nt,J,et){var ft=typeof T;(ft==="undefined"||ft==="boolean")&&(T=null);var lt=!1;if(T===null)lt=!0;else switch(ft){case"bigint":case"string":case"number":lt=!0;break;case"object":switch(T.$$typeof){case t:case n:lt=!0;break;case g:return lt=T._init,ut(lt(T._payload),F,nt,J,et)}}if(lt)return et=et(T),lt=J===""?"."+Y(T,0):J,G(et)?(nt="",lt!=null&&(nt=lt.replace(tt,"$&/")+"/"),ut(et,F,nt,"",function(kt){return kt})):et!=null&&(L(et)&&(et=N(et,nt+(et.key==null||T&&T.key===et.key?"":(""+et.key).replace(tt,"$&/")+"/")+lt)),F.push(et)),1;lt=0;var Dt=J===""?".":J+":";if(G(T))for(var dt=0;dt<T.length;dt++)J=T[dt],ft=Dt+Y(J,dt),lt+=ut(J,F,nt,ft,et);else if(dt=b(T),typeof dt=="function")for(T=dt.call(T),dt=0;!(J=T.next()).done;)J=J.value,ft=Dt+Y(J,dt++),lt+=ut(J,F,nt,ft,et);else if(ft==="object"){if(typeof T.then=="function")return ut(St(T),F,nt,J,et);throw F=String(T),Error("Objects are not valid as a React child (found: "+(F==="[object Object]"?"object with keys {"+Object.keys(T).join(", ")+"}":F)+"). If you meant to render a collection of children, use an array instead.")}return lt}function C(T,F,nt){if(T==null)return T;var J=[],et=0;return ut(T,J,"","",function(ft){return F.call(nt,ft,et++)}),J}function I(T){if(T._status===-1){var F=T._result;F=F(),F.then(function(nt){(T._status===0||T._status===-1)&&(T._status=1,T._result=nt)},function(nt){(T._status===0||T._status===-1)&&(T._status=2,T._result=nt)}),T._status===-1&&(T._status=0,T._result=F)}if(T._status===1)return T._result.default;throw T._result}var st=typeof reportError=="function"?reportError:function(T){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var F=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof T=="object"&&T!==null&&typeof T.message=="string"?String(T.message):String(T),error:T});if(!window.dispatchEvent(F))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",T);return}console.error(T)};function bt(){}return Mt.Children={map:C,forEach:function(T,F,nt){C(T,function(){F.apply(this,arguments)},nt)},count:function(T){var F=0;return C(T,function(){F++}),F},toArray:function(T){return C(T,function(F){return F})||[]},only:function(T){if(!L(T))throw Error("React.Children.only expected to receive a single React element child.");return T}},Mt.Component=j,Mt.Fragment=r,Mt.Profiler=l,Mt.PureComponent=B,Mt.StrictMode=i,Mt.Suspense=m,Mt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Z,Mt.__COMPILER_RUNTIME={__proto__:null,c:function(T){return Z.H.useMemoCache(T)}},Mt.cache=function(T){return function(){return T.apply(null,arguments)}},Mt.cloneElement=function(T,F,nt){if(T==null)throw Error("The argument must be a React element, but you passed "+T+".");var J=R({},T.props),et=T.key,ft=void 0;if(F!=null)for(lt in F.ref!==void 0&&(ft=void 0),F.key!==void 0&&(et=""+F.key),F)!K.call(F,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&F.ref===void 0||(J[lt]=F[lt]);var lt=arguments.length-2;if(lt===1)J.children=nt;else if(1<lt){for(var Dt=Array(lt),dt=0;dt<lt;dt++)Dt[dt]=arguments[dt+2];J.children=Dt}return Q(T.type,et,void 0,void 0,ft,J)},Mt.createContext=function(T){return T={$$typeof:f,_currentValue:T,_currentValue2:T,_threadCount:0,Provider:null,Consumer:null},T.Provider=T,T.Consumer={$$typeof:u,_context:T},T},Mt.createElement=function(T,F,nt){var J,et={},ft=null;if(F!=null)for(J in F.key!==void 0&&(ft=""+F.key),F)K.call(F,J)&&J!=="key"&&J!=="__self"&&J!=="__source"&&(et[J]=F[J]);var lt=arguments.length-2;if(lt===1)et.children=nt;else if(1<lt){for(var Dt=Array(lt),dt=0;dt<lt;dt++)Dt[dt]=arguments[dt+2];et.children=Dt}if(T&&T.defaultProps)for(J in lt=T.defaultProps,lt)et[J]===void 0&&(et[J]=lt[J]);return Q(T,ft,void 0,void 0,null,et)},Mt.createRef=function(){return{current:null}},Mt.forwardRef=function(T){return{$$typeof:h,render:T}},Mt.isValidElement=L,Mt.lazy=function(T){return{$$typeof:g,_payload:{_status:-1,_result:T},_init:I}},Mt.memo=function(T,F){return{$$typeof:p,type:T,compare:F===void 0?null:F}},Mt.startTransition=function(T){var F=Z.T,nt={};Z.T=nt;try{var J=T(),et=Z.S;et!==null&&et(nt,J),typeof J=="object"&&J!==null&&typeof J.then=="function"&&J.then(bt,st)}catch(ft){st(ft)}finally{Z.T=F}},Mt.unstable_useCacheRefresh=function(){return Z.H.useCacheRefresh()},Mt.use=function(T){return Z.H.use(T)},Mt.useActionState=function(T,F,nt){return Z.H.useActionState(T,F,nt)},Mt.useCallback=function(T,F){return Z.H.useCallback(T,F)},Mt.useContext=function(T){return Z.H.useContext(T)},Mt.useDebugValue=function(){},Mt.useDeferredValue=function(T,F){return Z.H.useDeferredValue(T,F)},Mt.useEffect=function(T,F,nt){var J=Z.H;if(typeof nt=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return J.useEffect(T,F)},Mt.useId=function(){return Z.H.useId()},Mt.useImperativeHandle=function(T,F,nt){return Z.H.useImperativeHandle(T,F,nt)},Mt.useInsertionEffect=function(T,F){return Z.H.useInsertionEffect(T,F)},Mt.useLayoutEffect=function(T,F){return Z.H.useLayoutEffect(T,F)},Mt.useMemo=function(T,F){return Z.H.useMemo(T,F)},Mt.useOptimistic=function(T,F){return Z.H.useOptimistic(T,F)},Mt.useReducer=function(T,F,nt){return Z.H.useReducer(T,F,nt)},Mt.useRef=function(T){return Z.H.useRef(T)},Mt.useState=function(T){return Z.H.useState(T)},Mt.useSyncExternalStore=function(T,F,nt){return Z.H.useSyncExternalStore(T,F,nt)},Mt.useTransition=function(){return Z.H.useTransition()},Mt.version="19.1.0",Mt}var sS;function Zl(){return sS||(sS=1,ip.exports=oA()),ip.exports}var op={exports:{}},Pe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var iS;function lA(){if(iS)return Pe;iS=1;var t=Zl();function n(m){var p="https://react.dev/errors/"+m;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)p+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+m+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var i={d:{f:r,r:function(){throw Error(n(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},l=Symbol.for("react.portal");function u(m,p,g){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:l,key:v==null?null:""+v,children:m,containerInfo:p,implementation:g}}var f=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(m,p){if(m==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return Pe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,Pe.createPortal=function(m,p){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(n(299));return u(m,p,null,g)},Pe.flushSync=function(m){var p=f.T,g=i.p;try{if(f.T=null,i.p=2,m)return m()}finally{f.T=p,i.p=g,i.d.f()}},Pe.preconnect=function(m,p){typeof m=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,i.d.C(m,p))},Pe.prefetchDNS=function(m){typeof m=="string"&&i.d.D(m)},Pe.preinit=function(m,p){if(typeof m=="string"&&p&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin),b=typeof p.integrity=="string"?p.integrity:void 0,O=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;g==="style"?i.d.S(m,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:v,integrity:b,fetchPriority:O}):g==="script"&&i.d.X(m,{crossOrigin:v,integrity:b,fetchPriority:O,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},Pe.preinitModule=function(m,p){if(typeof m=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var g=h(p.as,p.crossOrigin);i.d.M(m,{crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&i.d.M(m)},Pe.preload=function(m,p){if(typeof m=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin);i.d.L(m,g,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},Pe.preloadModule=function(m,p){if(typeof m=="string")if(p){var g=h(p.as,p.crossOrigin);i.d.m(m,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else i.d.m(m)},Pe.requestFormReset=function(m){i.d.r(m)},Pe.unstable_batchedUpdates=function(m,p){return m(p)},Pe.useFormState=function(m,p,g){return f.H.useFormState(m,p,g)},Pe.useFormStatus=function(){return f.H.useHostTransitionStatus()},Pe.version="19.1.0",Pe}var oS;function Q1(){if(oS)return op.exports;oS=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(n){console.error(n)}}return t(),op.exports=lA(),op.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lS;function cA(){if(lS)return ul;lS=1;var t=iA(),n=Zl(),r=Q1();function i(e){var a="https://react.dev/errors/"+e;if(1<arguments.length){a+="?args[]="+encodeURIComponent(arguments[1]);for(var s=2;s<arguments.length;s++)a+="&args[]="+encodeURIComponent(arguments[s])}return"Minified React error #"+e+"; visit "+a+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function u(e){var a=e,s=e;if(e.alternate)for(;a.return;)a=a.return;else{e=a;do a=e,(a.flags&4098)!==0&&(s=a.return),e=a.return;while(e)}return a.tag===3?s:null}function f(e){if(e.tag===13){var a=e.memoizedState;if(a===null&&(e=e.alternate,e!==null&&(a=e.memoizedState)),a!==null)return a.dehydrated}return null}function h(e){if(u(e)!==e)throw Error(i(188))}function m(e){var a=e.alternate;if(!a){if(a=u(e),a===null)throw Error(i(188));return a!==e?null:e}for(var s=e,o=a;;){var c=s.return;if(c===null)break;var d=c.alternate;if(d===null){if(o=c.return,o!==null){s=o;continue}break}if(c.child===d.child){for(d=c.child;d;){if(d===s)return h(c),e;if(d===o)return h(c),a;d=d.sibling}throw Error(i(188))}if(s.return!==o.return)s=c,o=d;else{for(var y=!1,_=c.child;_;){if(_===s){y=!0,s=c,o=d;break}if(_===o){y=!0,o=c,s=d;break}_=_.sibling}if(!y){for(_=d.child;_;){if(_===s){y=!0,s=d,o=c;break}if(_===o){y=!0,o=d,s=c;break}_=_.sibling}if(!y)throw Error(i(189))}}if(s.alternate!==o)throw Error(i(190))}if(s.tag!==3)throw Error(i(188));return s.stateNode.current===s?e:a}function p(e){var a=e.tag;if(a===5||a===26||a===27||a===6)return e;for(e=e.child;e!==null;){if(a=p(e),a!==null)return a;e=e.sibling}return null}var g=Object.assign,v=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),O=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),U=Symbol.for("react.provider"),B=Symbol.for("react.consumer"),q=Symbol.for("react.context"),G=Symbol.for("react.forward_ref"),Z=Symbol.for("react.suspense"),K=Symbol.for("react.suspense_list"),Q=Symbol.for("react.memo"),N=Symbol.for("react.lazy"),L=Symbol.for("react.activity"),rt=Symbol.for("react.memo_cache_sentinel"),tt=Symbol.iterator;function Y(e){return e===null||typeof e!="object"?null:(e=tt&&e[tt]||e["@@iterator"],typeof e=="function"?e:null)}var it=Symbol.for("react.client.reference");function St(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===it?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case j:return"Profiler";case A:return"StrictMode";case Z:return"Suspense";case K:return"SuspenseList";case L:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case O:return"Portal";case q:return(e.displayName||"Context")+".Provider";case B:return(e._context.displayName||"Context")+".Consumer";case G:var a=e.render;return e=e.displayName,e||(e=a.displayName||a.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Q:return a=e.displayName||null,a!==null?a:St(e.type)||"Memo";case N:a=e._payload,e=e._init;try{return St(e(a))}catch{}}return null}var ut=Array.isArray,C=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,st={pending:!1,data:null,method:null,action:null},bt=[],T=-1;function F(e){return{current:e}}function nt(e){0>T||(e.current=bt[T],bt[T]=null,T--)}function J(e,a){T++,bt[T]=e.current,e.current=a}var et=F(null),ft=F(null),lt=F(null),Dt=F(null);function dt(e,a){switch(J(lt,a),J(ft,e),J(et,null),a.nodeType){case 9:case 11:e=(e=a.documentElement)&&(e=e.namespaceURI)?Ab(e):0;break;default:if(e=a.tagName,a=a.namespaceURI)a=Ab(a),e=Cb(a,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}nt(et),J(et,e)}function kt(){nt(et),nt(ft),nt(lt)}function Zt(e){e.memoizedState!==null&&J(Dt,e);var a=et.current,s=Cb(a,e.type);a!==s&&(J(ft,e),J(et,s))}function fe(e){ft.current===e&&(nt(et),nt(ft)),Dt.current===e&&(nt(Dt),rl._currentValue=st)}var Se=Object.prototype.hasOwnProperty,ma=t.unstable_scheduleCallback,Un=t.unstable_cancelCallback,qa=t.unstable_shouldYield,_n=t.unstable_requestPaint,De=t.unstable_now,Ha=t.unstable_getCurrentPriorityLevel,uo=t.unstable_ImmediatePriority,fo=t.unstable_UserBlockingPriority,Bt=t.unstable_NormalPriority,ie=t.unstable_LowPriority,Ts=t.unstable_IdlePriority,vc=t.log,Pd=t.unstable_setDisableYieldValue,Or=null,cn=null;function Ia(e){if(typeof vc=="function"&&Pd(e),cn&&typeof cn.setStrictMode=="function")try{cn.setStrictMode(Or,e)}catch{}}var un=Math.clz32?Math.clz32:HR,BR=Math.log,qR=Math.LN2;function HR(e){return e>>>=0,e===0?32:31-(BR(e)/qR|0)|0}var _c=256,bc=4194304;function Rr(e){var a=e&42;if(a!==0)return a;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Sc(e,a,s){var o=e.pendingLanes;if(o===0)return 0;var c=0,d=e.suspendedLanes,y=e.pingedLanes;e=e.warmLanes;var _=o&134217727;return _!==0?(o=_&~d,o!==0?c=Rr(o):(y&=_,y!==0?c=Rr(y):s||(s=_&~e,s!==0&&(c=Rr(s))))):(_=o&~d,_!==0?c=Rr(_):y!==0?c=Rr(y):s||(s=o&~e,s!==0&&(c=Rr(s)))),c===0?0:a!==0&&a!==c&&(a&d)===0&&(d=c&-c,s=a&-a,d>=s||d===32&&(s&4194048)!==0)?a:c}function ho(e,a){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&a)===0}function IR(e,a){switch(e){case 1:case 2:case 4:case 8:case 64:return a+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return a+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function f0(){var e=_c;return _c<<=1,(_c&4194048)===0&&(_c=256),e}function d0(){var e=bc;return bc<<=1,(bc&62914560)===0&&(bc=4194304),e}function Vd(e){for(var a=[],s=0;31>s;s++)a.push(e);return a}function mo(e,a){e.pendingLanes|=a,a!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function PR(e,a,s,o,c,d){var y=e.pendingLanes;e.pendingLanes=s,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=s,e.entangledLanes&=s,e.errorRecoveryDisabledLanes&=s,e.shellSuspendCounter=0;var _=e.entanglements,S=e.expirationTimes,k=e.hiddenUpdates;for(s=y&~s;0<s;){var H=31-un(s),V=1<<H;_[H]=0,S[H]=-1;var z=k[H];if(z!==null)for(k[H]=null,H=0;H<z.length;H++){var $=z[H];$!==null&&($.lane&=-536870913)}s&=~V}o!==0&&h0(e,o,0),d!==0&&c===0&&e.tag!==0&&(e.suspendedLanes|=d&~(y&~a))}function h0(e,a,s){e.pendingLanes|=a,e.suspendedLanes&=~a;var o=31-un(a);e.entangledLanes|=a,e.entanglements[o]=e.entanglements[o]|1073741824|s&4194090}function m0(e,a){var s=e.entangledLanes|=a;for(e=e.entanglements;s;){var o=31-un(s),c=1<<o;c&a|e[o]&a&&(e[o]|=a),s&=~c}}function Gd(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Kd(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function p0(){var e=I.p;return e!==0?e:(e=window.event,e===void 0?32:Qb(e.type))}function VR(e,a){var s=I.p;try{return I.p=e,a()}finally{I.p=s}}var Pa=Math.random().toString(36).slice(2),He="__reactFiber$"+Pa,tn="__reactProps$"+Pa,Os="__reactContainer$"+Pa,Qd="__reactEvents$"+Pa,GR="__reactListeners$"+Pa,KR="__reactHandles$"+Pa,y0="__reactResources$"+Pa,po="__reactMarker$"+Pa;function Yd(e){delete e[He],delete e[tn],delete e[Qd],delete e[GR],delete e[KR]}function Rs(e){var a=e[He];if(a)return a;for(var s=e.parentNode;s;){if(a=s[Os]||s[He]){if(s=a.alternate,a.child!==null||s!==null&&s.child!==null)for(e=Nb(e);e!==null;){if(s=e[He])return s;e=Nb(e)}return a}e=s,s=e.parentNode}return null}function Ms(e){if(e=e[He]||e[Os]){var a=e.tag;if(a===5||a===6||a===13||a===26||a===27||a===3)return e}return null}function yo(e){var a=e.tag;if(a===5||a===26||a===27||a===6)return e.stateNode;throw Error(i(33))}function ws(e){var a=e[y0];return a||(a=e[y0]={hoistableStyles:new Map,hoistableScripts:new Map}),a}function ke(e){e[po]=!0}var g0=new Set,v0={};function Mr(e,a){As(e,a),As(e+"Capture",a)}function As(e,a){for(v0[e]=a,e=0;e<a.length;e++)g0.add(a[e])}var QR=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),_0={},b0={};function YR(e){return Se.call(b0,e)?!0:Se.call(_0,e)?!1:QR.test(e)?b0[e]=!0:(_0[e]=!0,!1)}function Ec(e,a,s){if(YR(a))if(s===null)e.removeAttribute(a);else{switch(typeof s){case"undefined":case"function":case"symbol":e.removeAttribute(a);return;case"boolean":var o=a.toLowerCase().slice(0,5);if(o!=="data-"&&o!=="aria-"){e.removeAttribute(a);return}}e.setAttribute(a,""+s)}}function Tc(e,a,s){if(s===null)e.removeAttribute(a);else{switch(typeof s){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttribute(a,""+s)}}function pa(e,a,s,o){if(o===null)e.removeAttribute(s);else{switch(typeof o){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(s);return}e.setAttributeNS(a,s,""+o)}}var Xd,S0;function Cs(e){if(Xd===void 0)try{throw Error()}catch(s){var a=s.stack.trim().match(/\n( *(at )?)/);Xd=a&&a[1]||"",S0=-1<s.stack.indexOf(`
    at`)?" (<anonymous>)":-1<s.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Xd+e+S0}var Zd=!1;function Jd(e,a){if(!e||Zd)return"";Zd=!0;var s=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var o={DetermineComponentFrameRoot:function(){try{if(a){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch($){var z=$}Reflect.construct(e,[],V)}else{try{V.call()}catch($){z=$}e.call(V.prototype)}}else{try{throw Error()}catch($){z=$}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch($){if($&&z&&typeof $.stack=="string")return[$.stack,z.stack]}return[null,null]}};o.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var c=Object.getOwnPropertyDescriptor(o.DetermineComponentFrameRoot,"name");c&&c.configurable&&Object.defineProperty(o.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var d=o.DetermineComponentFrameRoot(),y=d[0],_=d[1];if(y&&_){var S=y.split(`
`),k=_.split(`
`);for(c=o=0;o<S.length&&!S[o].includes("DetermineComponentFrameRoot");)o++;for(;c<k.length&&!k[c].includes("DetermineComponentFrameRoot");)c++;if(o===S.length||c===k.length)for(o=S.length-1,c=k.length-1;1<=o&&0<=c&&S[o]!==k[c];)c--;for(;1<=o&&0<=c;o--,c--)if(S[o]!==k[c]){if(o!==1||c!==1)do if(o--,c--,0>c||S[o]!==k[c]){var H=`
`+S[o].replace(" at new "," at ");return e.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",e.displayName)),H}while(1<=o&&0<=c);break}}}finally{Zd=!1,Error.prepareStackTrace=s}return(s=e?e.displayName||e.name:"")?Cs(s):""}function XR(e){switch(e.tag){case 26:case 27:case 5:return Cs(e.type);case 16:return Cs("Lazy");case 13:return Cs("Suspense");case 19:return Cs("SuspenseList");case 0:case 15:return Jd(e.type,!1);case 11:return Jd(e.type.render,!1);case 1:return Jd(e.type,!0);case 31:return Cs("Activity");default:return""}}function E0(e){try{var a="";do a+=XR(e),e=e.return;while(e);return a}catch(s){return`
Error generating stack: `+s.message+`
`+s.stack}}function bn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function T0(e){var a=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(a==="checkbox"||a==="radio")}function ZR(e){var a=T0(e)?"checked":"value",s=Object.getOwnPropertyDescriptor(e.constructor.prototype,a),o=""+e[a];if(!e.hasOwnProperty(a)&&typeof s<"u"&&typeof s.get=="function"&&typeof s.set=="function"){var c=s.get,d=s.set;return Object.defineProperty(e,a,{configurable:!0,get:function(){return c.call(this)},set:function(y){o=""+y,d.call(this,y)}}),Object.defineProperty(e,a,{enumerable:s.enumerable}),{getValue:function(){return o},setValue:function(y){o=""+y},stopTracking:function(){e._valueTracker=null,delete e[a]}}}}function Oc(e){e._valueTracker||(e._valueTracker=ZR(e))}function O0(e){if(!e)return!1;var a=e._valueTracker;if(!a)return!0;var s=a.getValue(),o="";return e&&(o=T0(e)?e.checked?"true":"false":e.value),e=o,e!==s?(a.setValue(e),!0):!1}function Rc(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var JR=/[\n"\\]/g;function Sn(e){return e.replace(JR,function(a){return"\\"+a.charCodeAt(0).toString(16)+" "})}function Wd(e,a,s,o,c,d,y,_){e.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.type=y:e.removeAttribute("type"),a!=null?y==="number"?(a===0&&e.value===""||e.value!=a)&&(e.value=""+bn(a)):e.value!==""+bn(a)&&(e.value=""+bn(a)):y!=="submit"&&y!=="reset"||e.removeAttribute("value"),a!=null?th(e,y,bn(a)):s!=null?th(e,y,bn(s)):o!=null&&e.removeAttribute("value"),c==null&&d!=null&&(e.defaultChecked=!!d),c!=null&&(e.checked=c&&typeof c!="function"&&typeof c!="symbol"),_!=null&&typeof _!="function"&&typeof _!="symbol"&&typeof _!="boolean"?e.name=""+bn(_):e.removeAttribute("name")}function R0(e,a,s,o,c,d,y,_){if(d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.type=d),a!=null||s!=null){if(!(d!=="submit"&&d!=="reset"||a!=null))return;s=s!=null?""+bn(s):"",a=a!=null?""+bn(a):s,_||a===e.value||(e.value=a),e.defaultValue=a}o=o??c,o=typeof o!="function"&&typeof o!="symbol"&&!!o,e.checked=_?e.checked:!!o,e.defaultChecked=!!o,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(e.name=y)}function th(e,a,s){a==="number"&&Rc(e.ownerDocument)===e||e.defaultValue===""+s||(e.defaultValue=""+s)}function xs(e,a,s,o){if(e=e.options,a){a={};for(var c=0;c<s.length;c++)a["$"+s[c]]=!0;for(s=0;s<e.length;s++)c=a.hasOwnProperty("$"+e[s].value),e[s].selected!==c&&(e[s].selected=c),c&&o&&(e[s].defaultSelected=!0)}else{for(s=""+bn(s),a=null,c=0;c<e.length;c++){if(e[c].value===s){e[c].selected=!0,o&&(e[c].defaultSelected=!0);return}a!==null||e[c].disabled||(a=e[c])}a!==null&&(a.selected=!0)}}function M0(e,a,s){if(a!=null&&(a=""+bn(a),a!==e.value&&(e.value=a),s==null)){e.defaultValue!==a&&(e.defaultValue=a);return}e.defaultValue=s!=null?""+bn(s):""}function w0(e,a,s,o){if(a==null){if(o!=null){if(s!=null)throw Error(i(92));if(ut(o)){if(1<o.length)throw Error(i(93));o=o[0]}s=o}s==null&&(s=""),a=s}s=bn(a),e.defaultValue=s,o=e.textContent,o===s&&o!==""&&o!==null&&(e.value=o)}function Ds(e,a){if(a){var s=e.firstChild;if(s&&s===e.lastChild&&s.nodeType===3){s.nodeValue=a;return}}e.textContent=a}var WR=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function A0(e,a,s){var o=a.indexOf("--")===0;s==null||typeof s=="boolean"||s===""?o?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="":o?e.setProperty(a,s):typeof s!="number"||s===0||WR.has(a)?a==="float"?e.cssFloat=s:e[a]=(""+s).trim():e[a]=s+"px"}function C0(e,a,s){if(a!=null&&typeof a!="object")throw Error(i(62));if(e=e.style,s!=null){for(var o in s)!s.hasOwnProperty(o)||a!=null&&a.hasOwnProperty(o)||(o.indexOf("--")===0?e.setProperty(o,""):o==="float"?e.cssFloat="":e[o]="");for(var c in a)o=a[c],a.hasOwnProperty(c)&&s[c]!==o&&A0(e,c,o)}else for(var d in a)a.hasOwnProperty(d)&&A0(e,d,a[d])}function eh(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var tM=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),eM=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Mc(e){return eM.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var nh=null;function ah(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ks=null,Ns=null;function x0(e){var a=Ms(e);if(a&&(e=a.stateNode)){var s=e[tn]||null;t:switch(e=a.stateNode,a.type){case"input":if(Wd(e,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name),a=s.name,s.type==="radio"&&a!=null){for(s=e;s.parentNode;)s=s.parentNode;for(s=s.querySelectorAll('input[name="'+Sn(""+a)+'"][type="radio"]'),a=0;a<s.length;a++){var o=s[a];if(o!==e&&o.form===e.form){var c=o[tn]||null;if(!c)throw Error(i(90));Wd(o,c.value,c.defaultValue,c.defaultValue,c.checked,c.defaultChecked,c.type,c.name)}}for(a=0;a<s.length;a++)o=s[a],o.form===e.form&&O0(o)}break t;case"textarea":M0(e,s.value,s.defaultValue);break t;case"select":a=s.value,a!=null&&xs(e,!!s.multiple,a,!1)}}}var rh=!1;function D0(e,a,s){if(rh)return e(a,s);rh=!0;try{var o=e(a);return o}finally{if(rh=!1,(ks!==null||Ns!==null)&&(fu(),ks&&(a=ks,e=Ns,Ns=ks=null,x0(a),e)))for(a=0;a<e.length;a++)x0(e[a])}}function go(e,a){var s=e.stateNode;if(s===null)return null;var o=s[tn]||null;if(o===null)return null;s=o[a];t:switch(a){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break t;default:e=!1}if(e)return null;if(s&&typeof s!="function")throw Error(i(231,a,typeof s));return s}var ya=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),sh=!1;if(ya)try{var vo={};Object.defineProperty(vo,"passive",{get:function(){sh=!0}}),window.addEventListener("test",vo,vo),window.removeEventListener("test",vo,vo)}catch{sh=!1}var Va=null,ih=null,wc=null;function k0(){if(wc)return wc;var e,a=ih,s=a.length,o,c="value"in Va?Va.value:Va.textContent,d=c.length;for(e=0;e<s&&a[e]===c[e];e++);var y=s-e;for(o=1;o<=y&&a[s-o]===c[d-o];o++);return wc=c.slice(e,1<o?1-o:void 0)}function Ac(e){var a=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&a===13&&(e=13)):e=a,e===10&&(e=13),32<=e||e===13?e:0}function Cc(){return!0}function N0(){return!1}function en(e){function a(s,o,c,d,y){this._reactName=s,this._targetInst=c,this.type=o,this.nativeEvent=d,this.target=y,this.currentTarget=null;for(var _ in e)e.hasOwnProperty(_)&&(s=e[_],this[_]=s?s(d):d[_]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Cc:N0,this.isPropagationStopped=N0,this}return g(a.prototype,{preventDefault:function(){this.defaultPrevented=!0;var s=this.nativeEvent;s&&(s.preventDefault?s.preventDefault():typeof s.returnValue!="unknown"&&(s.returnValue=!1),this.isDefaultPrevented=Cc)},stopPropagation:function(){var s=this.nativeEvent;s&&(s.stopPropagation?s.stopPropagation():typeof s.cancelBubble!="unknown"&&(s.cancelBubble=!0),this.isPropagationStopped=Cc)},persist:function(){},isPersistent:Cc}),a}var wr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},xc=en(wr),_o=g({},wr,{view:0,detail:0}),nM=en(_o),oh,lh,bo,Dc=g({},_o,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:uh,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==bo&&(bo&&e.type==="mousemove"?(oh=e.screenX-bo.screenX,lh=e.screenY-bo.screenY):lh=oh=0,bo=e),oh)},movementY:function(e){return"movementY"in e?e.movementY:lh}}),z0=en(Dc),aM=g({},Dc,{dataTransfer:0}),rM=en(aM),sM=g({},_o,{relatedTarget:0}),ch=en(sM),iM=g({},wr,{animationName:0,elapsedTime:0,pseudoElement:0}),oM=en(iM),lM=g({},wr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),cM=en(lM),uM=g({},wr,{data:0}),L0=en(uM),fM={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dM={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hM={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mM(e){var a=this.nativeEvent;return a.getModifierState?a.getModifierState(e):(e=hM[e])?!!a[e]:!1}function uh(){return mM}var pM=g({},_o,{key:function(e){if(e.key){var a=fM[e.key]||e.key;if(a!=="Unidentified")return a}return e.type==="keypress"?(e=Ac(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?dM[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:uh,charCode:function(e){return e.type==="keypress"?Ac(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ac(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),yM=en(pM),gM=g({},Dc,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$0=en(gM),vM=g({},_o,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:uh}),_M=en(vM),bM=g({},wr,{propertyName:0,elapsedTime:0,pseudoElement:0}),SM=en(bM),EM=g({},Dc,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),TM=en(EM),OM=g({},wr,{newState:0,oldState:0}),RM=en(OM),MM=[9,13,27,32],fh=ya&&"CompositionEvent"in window,So=null;ya&&"documentMode"in document&&(So=document.documentMode);var wM=ya&&"TextEvent"in window&&!So,U0=ya&&(!fh||So&&8<So&&11>=So),F0=" ",j0=!1;function B0(e,a){switch(e){case"keyup":return MM.indexOf(a.keyCode)!==-1;case"keydown":return a.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function q0(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var zs=!1;function AM(e,a){switch(e){case"compositionend":return q0(a);case"keypress":return a.which!==32?null:(j0=!0,F0);case"textInput":return e=a.data,e===F0&&j0?null:e;default:return null}}function CM(e,a){if(zs)return e==="compositionend"||!fh&&B0(e,a)?(e=k0(),wc=ih=Va=null,zs=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(a.ctrlKey||a.altKey||a.metaKey)||a.ctrlKey&&a.altKey){if(a.char&&1<a.char.length)return a.char;if(a.which)return String.fromCharCode(a.which)}return null;case"compositionend":return U0&&a.locale!=="ko"?null:a.data;default:return null}}var xM={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function H0(e){var a=e&&e.nodeName&&e.nodeName.toLowerCase();return a==="input"?!!xM[e.type]:a==="textarea"}function I0(e,a,s,o){ks?Ns?Ns.push(o):Ns=[o]:ks=o,a=gu(a,"onChange"),0<a.length&&(s=new xc("onChange","change",null,s,o),e.push({event:s,listeners:a}))}var Eo=null,To=null;function DM(e){Tb(e,0)}function kc(e){var a=yo(e);if(O0(a))return e}function P0(e,a){if(e==="change")return a}var V0=!1;if(ya){var dh;if(ya){var hh="oninput"in document;if(!hh){var G0=document.createElement("div");G0.setAttribute("oninput","return;"),hh=typeof G0.oninput=="function"}dh=hh}else dh=!1;V0=dh&&(!document.documentMode||9<document.documentMode)}function K0(){Eo&&(Eo.detachEvent("onpropertychange",Q0),To=Eo=null)}function Q0(e){if(e.propertyName==="value"&&kc(To)){var a=[];I0(a,To,e,ah(e)),D0(DM,a)}}function kM(e,a,s){e==="focusin"?(K0(),Eo=a,To=s,Eo.attachEvent("onpropertychange",Q0)):e==="focusout"&&K0()}function NM(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return kc(To)}function zM(e,a){if(e==="click")return kc(a)}function LM(e,a){if(e==="input"||e==="change")return kc(a)}function $M(e,a){return e===a&&(e!==0||1/e===1/a)||e!==e&&a!==a}var fn=typeof Object.is=="function"?Object.is:$M;function Oo(e,a){if(fn(e,a))return!0;if(typeof e!="object"||e===null||typeof a!="object"||a===null)return!1;var s=Object.keys(e),o=Object.keys(a);if(s.length!==o.length)return!1;for(o=0;o<s.length;o++){var c=s[o];if(!Se.call(a,c)||!fn(e[c],a[c]))return!1}return!0}function Y0(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function X0(e,a){var s=Y0(e);e=0;for(var o;s;){if(s.nodeType===3){if(o=e+s.textContent.length,e<=a&&o>=a)return{node:s,offset:a-e};e=o}t:{for(;s;){if(s.nextSibling){s=s.nextSibling;break t}s=s.parentNode}s=void 0}s=Y0(s)}}function Z0(e,a){return e&&a?e===a?!0:e&&e.nodeType===3?!1:a&&a.nodeType===3?Z0(e,a.parentNode):"contains"in e?e.contains(a):e.compareDocumentPosition?!!(e.compareDocumentPosition(a)&16):!1:!1}function J0(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var a=Rc(e.document);a instanceof e.HTMLIFrameElement;){try{var s=typeof a.contentWindow.location.href=="string"}catch{s=!1}if(s)e=a.contentWindow;else break;a=Rc(e.document)}return a}function mh(e){var a=e&&e.nodeName&&e.nodeName.toLowerCase();return a&&(a==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||a==="textarea"||e.contentEditable==="true")}var UM=ya&&"documentMode"in document&&11>=document.documentMode,Ls=null,ph=null,Ro=null,yh=!1;function W0(e,a,s){var o=s.window===s?s.document:s.nodeType===9?s:s.ownerDocument;yh||Ls==null||Ls!==Rc(o)||(o=Ls,"selectionStart"in o&&mh(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),Ro&&Oo(Ro,o)||(Ro=o,o=gu(ph,"onSelect"),0<o.length&&(a=new xc("onSelect","select",null,a,s),e.push({event:a,listeners:o}),a.target=Ls)))}function Ar(e,a){var s={};return s[e.toLowerCase()]=a.toLowerCase(),s["Webkit"+e]="webkit"+a,s["Moz"+e]="moz"+a,s}var $s={animationend:Ar("Animation","AnimationEnd"),animationiteration:Ar("Animation","AnimationIteration"),animationstart:Ar("Animation","AnimationStart"),transitionrun:Ar("Transition","TransitionRun"),transitionstart:Ar("Transition","TransitionStart"),transitioncancel:Ar("Transition","TransitionCancel"),transitionend:Ar("Transition","TransitionEnd")},gh={},tv={};ya&&(tv=document.createElement("div").style,"AnimationEvent"in window||(delete $s.animationend.animation,delete $s.animationiteration.animation,delete $s.animationstart.animation),"TransitionEvent"in window||delete $s.transitionend.transition);function Cr(e){if(gh[e])return gh[e];if(!$s[e])return e;var a=$s[e],s;for(s in a)if(a.hasOwnProperty(s)&&s in tv)return gh[e]=a[s];return e}var ev=Cr("animationend"),nv=Cr("animationiteration"),av=Cr("animationstart"),FM=Cr("transitionrun"),jM=Cr("transitionstart"),BM=Cr("transitioncancel"),rv=Cr("transitionend"),sv=new Map,vh="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");vh.push("scrollEnd");function Fn(e,a){sv.set(e,a),Mr(a,[e])}var iv=new WeakMap;function En(e,a){if(typeof e=="object"&&e!==null){var s=iv.get(e);return s!==void 0?s:(a={value:e,source:a,stack:E0(a)},iv.set(e,a),a)}return{value:e,source:a,stack:E0(a)}}var Tn=[],Us=0,_h=0;function Nc(){for(var e=Us,a=_h=Us=0;a<e;){var s=Tn[a];Tn[a++]=null;var o=Tn[a];Tn[a++]=null;var c=Tn[a];Tn[a++]=null;var d=Tn[a];if(Tn[a++]=null,o!==null&&c!==null){var y=o.pending;y===null?c.next=c:(c.next=y.next,y.next=c),o.pending=c}d!==0&&ov(s,c,d)}}function zc(e,a,s,o){Tn[Us++]=e,Tn[Us++]=a,Tn[Us++]=s,Tn[Us++]=o,_h|=o,e.lanes|=o,e=e.alternate,e!==null&&(e.lanes|=o)}function bh(e,a,s,o){return zc(e,a,s,o),Lc(e)}function Fs(e,a){return zc(e,null,null,a),Lc(e)}function ov(e,a,s){e.lanes|=s;var o=e.alternate;o!==null&&(o.lanes|=s);for(var c=!1,d=e.return;d!==null;)d.childLanes|=s,o=d.alternate,o!==null&&(o.childLanes|=s),d.tag===22&&(e=d.stateNode,e===null||e._visibility&1||(c=!0)),e=d,d=d.return;return e.tag===3?(d=e.stateNode,c&&a!==null&&(c=31-un(s),e=d.hiddenUpdates,o=e[c],o===null?e[c]=[a]:o.push(a),a.lane=s|536870912),d):null}function Lc(e){if(50<Xo)throw Xo=0,Mm=null,Error(i(185));for(var a=e.return;a!==null;)e=a,a=e.return;return e.tag===3?e.stateNode:null}var js={};function qM(e,a,s,o){this.tag=e,this.key=s,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=a,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dn(e,a,s,o){return new qM(e,a,s,o)}function Sh(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ga(e,a){var s=e.alternate;return s===null?(s=dn(e.tag,a,e.key,e.mode),s.elementType=e.elementType,s.type=e.type,s.stateNode=e.stateNode,s.alternate=e,e.alternate=s):(s.pendingProps=a,s.type=e.type,s.flags=0,s.subtreeFlags=0,s.deletions=null),s.flags=e.flags&65011712,s.childLanes=e.childLanes,s.lanes=e.lanes,s.child=e.child,s.memoizedProps=e.memoizedProps,s.memoizedState=e.memoizedState,s.updateQueue=e.updateQueue,a=e.dependencies,s.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},s.sibling=e.sibling,s.index=e.index,s.ref=e.ref,s.refCleanup=e.refCleanup,s}function lv(e,a){e.flags&=65011714;var s=e.alternate;return s===null?(e.childLanes=0,e.lanes=a,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=s.childLanes,e.lanes=s.lanes,e.child=s.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=s.memoizedProps,e.memoizedState=s.memoizedState,e.updateQueue=s.updateQueue,e.type=s.type,a=s.dependencies,e.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext}),e}function $c(e,a,s,o,c,d){var y=0;if(o=e,typeof e=="function")Sh(e)&&(y=1);else if(typeof e=="string")y=Iw(e,s,et.current)?26:e==="html"||e==="head"||e==="body"?27:5;else t:switch(e){case L:return e=dn(31,s,a,c),e.elementType=L,e.lanes=d,e;case R:return xr(s.children,c,d,a);case A:y=8,c|=24;break;case j:return e=dn(12,s,a,c|2),e.elementType=j,e.lanes=d,e;case Z:return e=dn(13,s,a,c),e.elementType=Z,e.lanes=d,e;case K:return e=dn(19,s,a,c),e.elementType=K,e.lanes=d,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case U:case q:y=10;break t;case B:y=9;break t;case G:y=11;break t;case Q:y=14;break t;case N:y=16,o=null;break t}y=29,s=Error(i(130,e===null?"null":typeof e,"")),o=null}return a=dn(y,s,a,c),a.elementType=e,a.type=o,a.lanes=d,a}function xr(e,a,s,o){return e=dn(7,e,o,a),e.lanes=s,e}function Eh(e,a,s){return e=dn(6,e,null,a),e.lanes=s,e}function Th(e,a,s){return a=dn(4,e.children!==null?e.children:[],e.key,a),a.lanes=s,a.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},a}var Bs=[],qs=0,Uc=null,Fc=0,On=[],Rn=0,Dr=null,va=1,_a="";function kr(e,a){Bs[qs++]=Fc,Bs[qs++]=Uc,Uc=e,Fc=a}function cv(e,a,s){On[Rn++]=va,On[Rn++]=_a,On[Rn++]=Dr,Dr=e;var o=va;e=_a;var c=32-un(o)-1;o&=~(1<<c),s+=1;var d=32-un(a)+c;if(30<d){var y=c-c%5;d=(o&(1<<y)-1).toString(32),o>>=y,c-=y,va=1<<32-un(a)+c|s<<c|o,_a=d+e}else va=1<<d|s<<c|o,_a=e}function Oh(e){e.return!==null&&(kr(e,1),cv(e,1,0))}function Rh(e){for(;e===Uc;)Uc=Bs[--qs],Bs[qs]=null,Fc=Bs[--qs],Bs[qs]=null;for(;e===Dr;)Dr=On[--Rn],On[Rn]=null,_a=On[--Rn],On[Rn]=null,va=On[--Rn],On[Rn]=null}var Qe=null,de=null,qt=!1,Nr=null,Zn=!1,Mh=Error(i(519));function zr(e){var a=Error(i(418,""));throw Ao(En(a,e)),Mh}function uv(e){var a=e.stateNode,s=e.type,o=e.memoizedProps;switch(a[He]=e,a[tn]=o,s){case"dialog":zt("cancel",a),zt("close",a);break;case"iframe":case"object":case"embed":zt("load",a);break;case"video":case"audio":for(s=0;s<Jo.length;s++)zt(Jo[s],a);break;case"source":zt("error",a);break;case"img":case"image":case"link":zt("error",a),zt("load",a);break;case"details":zt("toggle",a);break;case"input":zt("invalid",a),R0(a,o.value,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name,!0),Oc(a);break;case"select":zt("invalid",a);break;case"textarea":zt("invalid",a),w0(a,o.value,o.defaultValue,o.children),Oc(a)}s=o.children,typeof s!="string"&&typeof s!="number"&&typeof s!="bigint"||a.textContent===""+s||o.suppressHydrationWarning===!0||wb(a.textContent,s)?(o.popover!=null&&(zt("beforetoggle",a),zt("toggle",a)),o.onScroll!=null&&zt("scroll",a),o.onScrollEnd!=null&&zt("scrollend",a),o.onClick!=null&&(a.onclick=vu),a=!0):a=!1,a||zr(e)}function fv(e){for(Qe=e.return;Qe;)switch(Qe.tag){case 5:case 13:Zn=!1;return;case 27:case 3:Zn=!0;return;default:Qe=Qe.return}}function Mo(e){if(e!==Qe)return!1;if(!qt)return fv(e),qt=!0,!1;var a=e.tag,s;if((s=a!==3&&a!==27)&&((s=a===5)&&(s=e.type,s=!(s!=="form"&&s!=="button")||Hm(e.type,e.memoizedProps)),s=!s),s&&de&&zr(e),fv(e),a===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));t:{for(e=e.nextSibling,a=0;e;){if(e.nodeType===8)if(s=e.data,s==="/$"){if(a===0){de=Bn(e.nextSibling);break t}a--}else s!=="$"&&s!=="$!"&&s!=="$?"||a++;e=e.nextSibling}de=null}}else a===27?(a=de,or(e.type)?(e=Gm,Gm=null,de=e):de=a):de=Qe?Bn(e.stateNode.nextSibling):null;return!0}function wo(){de=Qe=null,qt=!1}function dv(){var e=Nr;return e!==null&&(rn===null?rn=e:rn.push.apply(rn,e),Nr=null),e}function Ao(e){Nr===null?Nr=[e]:Nr.push(e)}var wh=F(null),Lr=null,ba=null;function Ga(e,a,s){J(wh,a._currentValue),a._currentValue=s}function Sa(e){e._currentValue=wh.current,nt(wh)}function Ah(e,a,s){for(;e!==null;){var o=e.alternate;if((e.childLanes&a)!==a?(e.childLanes|=a,o!==null&&(o.childLanes|=a)):o!==null&&(o.childLanes&a)!==a&&(o.childLanes|=a),e===s)break;e=e.return}}function Ch(e,a,s,o){var c=e.child;for(c!==null&&(c.return=e);c!==null;){var d=c.dependencies;if(d!==null){var y=c.child;d=d.firstContext;t:for(;d!==null;){var _=d;d=c;for(var S=0;S<a.length;S++)if(_.context===a[S]){d.lanes|=s,_=d.alternate,_!==null&&(_.lanes|=s),Ah(d.return,s,e),o||(y=null);break t}d=_.next}}else if(c.tag===18){if(y=c.return,y===null)throw Error(i(341));y.lanes|=s,d=y.alternate,d!==null&&(d.lanes|=s),Ah(y,s,e),y=null}else y=c.child;if(y!==null)y.return=c;else for(y=c;y!==null;){if(y===e){y=null;break}if(c=y.sibling,c!==null){c.return=y.return,y=c;break}y=y.return}c=y}}function Co(e,a,s,o){e=null;for(var c=a,d=!1;c!==null;){if(!d){if((c.flags&524288)!==0)d=!0;else if((c.flags&262144)!==0)break}if(c.tag===10){var y=c.alternate;if(y===null)throw Error(i(387));if(y=y.memoizedProps,y!==null){var _=c.type;fn(c.pendingProps.value,y.value)||(e!==null?e.push(_):e=[_])}}else if(c===Dt.current){if(y=c.alternate,y===null)throw Error(i(387));y.memoizedState.memoizedState!==c.memoizedState.memoizedState&&(e!==null?e.push(rl):e=[rl])}c=c.return}e!==null&&Ch(a,e,s,o),a.flags|=262144}function jc(e){for(e=e.firstContext;e!==null;){if(!fn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function $r(e){Lr=e,ba=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ie(e){return hv(Lr,e)}function Bc(e,a){return Lr===null&&$r(e),hv(e,a)}function hv(e,a){var s=a._currentValue;if(a={context:a,memoizedValue:s,next:null},ba===null){if(e===null)throw Error(i(308));ba=a,e.dependencies={lanes:0,firstContext:a},e.flags|=524288}else ba=ba.next=a;return s}var HM=typeof AbortController<"u"?AbortController:function(){var e=[],a=this.signal={aborted:!1,addEventListener:function(s,o){e.push(o)}};this.abort=function(){a.aborted=!0,e.forEach(function(s){return s()})}},IM=t.unstable_scheduleCallback,PM=t.unstable_NormalPriority,Re={$$typeof:q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function xh(){return{controller:new HM,data:new Map,refCount:0}}function xo(e){e.refCount--,e.refCount===0&&IM(PM,function(){e.controller.abort()})}var Do=null,Dh=0,Hs=0,Is=null;function VM(e,a){if(Do===null){var s=Do=[];Dh=0,Hs=Nm(),Is={status:"pending",value:void 0,then:function(o){s.push(o)}}}return Dh++,a.then(mv,mv),a}function mv(){if(--Dh===0&&Do!==null){Is!==null&&(Is.status="fulfilled");var e=Do;Do=null,Hs=0,Is=null;for(var a=0;a<e.length;a++)(0,e[a])()}}function GM(e,a){var s=[],o={status:"pending",value:null,reason:null,then:function(c){s.push(c)}};return e.then(function(){o.status="fulfilled",o.value=a;for(var c=0;c<s.length;c++)(0,s[c])(a)},function(c){for(o.status="rejected",o.reason=c,c=0;c<s.length;c++)(0,s[c])(void 0)}),o}var pv=C.S;C.S=function(e,a){typeof a=="object"&&a!==null&&typeof a.then=="function"&&VM(e,a),pv!==null&&pv(e,a)};var Ur=F(null);function kh(){var e=Ur.current;return e!==null?e:ee.pooledCache}function qc(e,a){a===null?J(Ur,Ur.current):J(Ur,a.pool)}function yv(){var e=kh();return e===null?null:{parent:Re._currentValue,pool:e}}var ko=Error(i(460)),gv=Error(i(474)),Hc=Error(i(542)),Nh={then:function(){}};function vv(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ic(){}function _v(e,a,s){switch(s=e[s],s===void 0?e.push(a):s!==a&&(a.then(Ic,Ic),a=s),a.status){case"fulfilled":return a.value;case"rejected":throw e=a.reason,Sv(e),e;default:if(typeof a.status=="string")a.then(Ic,Ic);else{if(e=ee,e!==null&&100<e.shellSuspendCounter)throw Error(i(482));e=a,e.status="pending",e.then(function(o){if(a.status==="pending"){var c=a;c.status="fulfilled",c.value=o}},function(o){if(a.status==="pending"){var c=a;c.status="rejected",c.reason=o}})}switch(a.status){case"fulfilled":return a.value;case"rejected":throw e=a.reason,Sv(e),e}throw No=a,ko}}var No=null;function bv(){if(No===null)throw Error(i(459));var e=No;return No=null,e}function Sv(e){if(e===ko||e===Hc)throw Error(i(483))}var Ka=!1;function zh(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Lh(e,a){e=e.updateQueue,a.updateQueue===e&&(a.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Qa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Ya(e,a,s){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(It&2)!==0){var c=o.pending;return c===null?a.next=a:(a.next=c.next,c.next=a),o.pending=a,a=Lc(e),ov(e,null,s),a}return zc(e,o,a,s),Lc(e)}function zo(e,a,s){if(a=a.updateQueue,a!==null&&(a=a.shared,(s&4194048)!==0)){var o=a.lanes;o&=e.pendingLanes,s|=o,a.lanes=s,m0(e,s)}}function $h(e,a){var s=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,s===o)){var c=null,d=null;if(s=s.firstBaseUpdate,s!==null){do{var y={lane:s.lane,tag:s.tag,payload:s.payload,callback:null,next:null};d===null?c=d=y:d=d.next=y,s=s.next}while(s!==null);d===null?c=d=a:d=d.next=a}else c=d=a;s={baseState:o.baseState,firstBaseUpdate:c,lastBaseUpdate:d,shared:o.shared,callbacks:o.callbacks},e.updateQueue=s;return}e=s.lastBaseUpdate,e===null?s.firstBaseUpdate=a:e.next=a,s.lastBaseUpdate=a}var Uh=!1;function Lo(){if(Uh){var e=Is;if(e!==null)throw e}}function $o(e,a,s,o){Uh=!1;var c=e.updateQueue;Ka=!1;var d=c.firstBaseUpdate,y=c.lastBaseUpdate,_=c.shared.pending;if(_!==null){c.shared.pending=null;var S=_,k=S.next;S.next=null,y===null?d=k:y.next=k,y=S;var H=e.alternate;H!==null&&(H=H.updateQueue,_=H.lastBaseUpdate,_!==y&&(_===null?H.firstBaseUpdate=k:_.next=k,H.lastBaseUpdate=S))}if(d!==null){var V=c.baseState;y=0,H=k=S=null,_=d;do{var z=_.lane&-536870913,$=z!==_.lane;if($?(Ut&z)===z:(o&z)===z){z!==0&&z===Hs&&(Uh=!0),H!==null&&(H=H.next={lane:0,tag:_.tag,payload:_.payload,callback:null,next:null});t:{var _t=e,gt=_;z=a;var Yt=s;switch(gt.tag){case 1:if(_t=gt.payload,typeof _t=="function"){V=_t.call(Yt,V,z);break t}V=_t;break t;case 3:_t.flags=_t.flags&-65537|128;case 0:if(_t=gt.payload,z=typeof _t=="function"?_t.call(Yt,V,z):_t,z==null)break t;V=g({},V,z);break t;case 2:Ka=!0}}z=_.callback,z!==null&&(e.flags|=64,$&&(e.flags|=8192),$=c.callbacks,$===null?c.callbacks=[z]:$.push(z))}else $={lane:z,tag:_.tag,payload:_.payload,callback:_.callback,next:null},H===null?(k=H=$,S=V):H=H.next=$,y|=z;if(_=_.next,_===null){if(_=c.shared.pending,_===null)break;$=_,_=$.next,$.next=null,c.lastBaseUpdate=$,c.shared.pending=null}}while(!0);H===null&&(S=V),c.baseState=S,c.firstBaseUpdate=k,c.lastBaseUpdate=H,d===null&&(c.shared.lanes=0),ar|=y,e.lanes=y,e.memoizedState=V}}function Ev(e,a){if(typeof e!="function")throw Error(i(191,e));e.call(a)}function Tv(e,a){var s=e.callbacks;if(s!==null)for(e.callbacks=null,e=0;e<s.length;e++)Ev(s[e],a)}var Ps=F(null),Pc=F(0);function Ov(e,a){e=Aa,J(Pc,e),J(Ps,a),Aa=e|a.baseLanes}function Fh(){J(Pc,Aa),J(Ps,Ps.current)}function jh(){Aa=Pc.current,nt(Ps),nt(Pc)}var Xa=0,Ct=null,Kt=null,Ee=null,Vc=!1,Vs=!1,Fr=!1,Gc=0,Uo=0,Gs=null,KM=0;function ye(){throw Error(i(321))}function Bh(e,a){if(a===null)return!1;for(var s=0;s<a.length&&s<e.length;s++)if(!fn(e[s],a[s]))return!1;return!0}function qh(e,a,s,o,c,d){return Xa=d,Ct=a,a.memoizedState=null,a.updateQueue=null,a.lanes=0,C.H=e===null||e.memoizedState===null?o_:l_,Fr=!1,d=s(o,c),Fr=!1,Vs&&(d=Mv(a,s,o,c)),Rv(e),d}function Rv(e){C.H=Jc;var a=Kt!==null&&Kt.next!==null;if(Xa=0,Ee=Kt=Ct=null,Vc=!1,Uo=0,Gs=null,a)throw Error(i(300));e===null||Ne||(e=e.dependencies,e!==null&&jc(e)&&(Ne=!0))}function Mv(e,a,s,o){Ct=e;var c=0;do{if(Vs&&(Gs=null),Uo=0,Vs=!1,25<=c)throw Error(i(301));if(c+=1,Ee=Kt=null,e.updateQueue!=null){var d=e.updateQueue;d.lastEffect=null,d.events=null,d.stores=null,d.memoCache!=null&&(d.memoCache.index=0)}C.H=tw,d=a(s,o)}while(Vs);return d}function QM(){var e=C.H,a=e.useState()[0];return a=typeof a.then=="function"?Fo(a):a,e=e.useState()[0],(Kt!==null?Kt.memoizedState:null)!==e&&(Ct.flags|=1024),a}function Hh(){var e=Gc!==0;return Gc=0,e}function Ih(e,a,s){a.updateQueue=e.updateQueue,a.flags&=-2053,e.lanes&=~s}function Ph(e){if(Vc){for(e=e.memoizedState;e!==null;){var a=e.queue;a!==null&&(a.pending=null),e=e.next}Vc=!1}Xa=0,Ee=Kt=Ct=null,Vs=!1,Uo=Gc=0,Gs=null}function nn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?Ct.memoizedState=Ee=e:Ee=Ee.next=e,Ee}function Te(){if(Kt===null){var e=Ct.alternate;e=e!==null?e.memoizedState:null}else e=Kt.next;var a=Ee===null?Ct.memoizedState:Ee.next;if(a!==null)Ee=a,Kt=e;else{if(e===null)throw Ct.alternate===null?Error(i(467)):Error(i(310));Kt=e,e={memoizedState:Kt.memoizedState,baseState:Kt.baseState,baseQueue:Kt.baseQueue,queue:Kt.queue,next:null},Ee===null?Ct.memoizedState=Ee=e:Ee=Ee.next=e}return Ee}function Vh(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Fo(e){var a=Uo;return Uo+=1,Gs===null&&(Gs=[]),e=_v(Gs,e,a),a=Ct,(Ee===null?a.memoizedState:Ee.next)===null&&(a=a.alternate,C.H=a===null||a.memoizedState===null?o_:l_),e}function Kc(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Fo(e);if(e.$$typeof===q)return Ie(e)}throw Error(i(438,String(e)))}function Gh(e){var a=null,s=Ct.updateQueue;if(s!==null&&(a=s.memoCache),a==null){var o=Ct.alternate;o!==null&&(o=o.updateQueue,o!==null&&(o=o.memoCache,o!=null&&(a={data:o.data.map(function(c){return c.slice()}),index:0})))}if(a==null&&(a={data:[],index:0}),s===null&&(s=Vh(),Ct.updateQueue=s),s.memoCache=a,s=a.data[a.index],s===void 0)for(s=a.data[a.index]=Array(e),o=0;o<e;o++)s[o]=rt;return a.index++,s}function Ea(e,a){return typeof a=="function"?a(e):a}function Qc(e){var a=Te();return Kh(a,Kt,e)}function Kh(e,a,s){var o=e.queue;if(o===null)throw Error(i(311));o.lastRenderedReducer=s;var c=e.baseQueue,d=o.pending;if(d!==null){if(c!==null){var y=c.next;c.next=d.next,d.next=y}a.baseQueue=c=d,o.pending=null}if(d=e.baseState,c===null)e.memoizedState=d;else{a=c.next;var _=y=null,S=null,k=a,H=!1;do{var V=k.lane&-536870913;if(V!==k.lane?(Ut&V)===V:(Xa&V)===V){var z=k.revertLane;if(z===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null}),V===Hs&&(H=!0);else if((Xa&z)===z){k=k.next,z===Hs&&(H=!0);continue}else V={lane:0,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},S===null?(_=S=V,y=d):S=S.next=V,Ct.lanes|=z,ar|=z;V=k.action,Fr&&s(d,V),d=k.hasEagerState?k.eagerState:s(d,V)}else z={lane:V,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},S===null?(_=S=z,y=d):S=S.next=z,Ct.lanes|=V,ar|=V;k=k.next}while(k!==null&&k!==a);if(S===null?y=d:S.next=_,!fn(d,e.memoizedState)&&(Ne=!0,H&&(s=Is,s!==null)))throw s;e.memoizedState=d,e.baseState=y,e.baseQueue=S,o.lastRenderedState=d}return c===null&&(o.lanes=0),[e.memoizedState,o.dispatch]}function Qh(e){var a=Te(),s=a.queue;if(s===null)throw Error(i(311));s.lastRenderedReducer=e;var o=s.dispatch,c=s.pending,d=a.memoizedState;if(c!==null){s.pending=null;var y=c=c.next;do d=e(d,y.action),y=y.next;while(y!==c);fn(d,a.memoizedState)||(Ne=!0),a.memoizedState=d,a.baseQueue===null&&(a.baseState=d),s.lastRenderedState=d}return[d,o]}function wv(e,a,s){var o=Ct,c=Te(),d=qt;if(d){if(s===void 0)throw Error(i(407));s=s()}else s=a();var y=!fn((Kt||c).memoizedState,s);y&&(c.memoizedState=s,Ne=!0),c=c.queue;var _=xv.bind(null,o,c,e);if(jo(2048,8,_,[e]),c.getSnapshot!==a||y||Ee!==null&&Ee.memoizedState.tag&1){if(o.flags|=2048,Ks(9,Yc(),Cv.bind(null,o,c,s,a),null),ee===null)throw Error(i(349));d||(Xa&124)!==0||Av(o,a,s)}return s}function Av(e,a,s){e.flags|=16384,e={getSnapshot:a,value:s},a=Ct.updateQueue,a===null?(a=Vh(),Ct.updateQueue=a,a.stores=[e]):(s=a.stores,s===null?a.stores=[e]:s.push(e))}function Cv(e,a,s,o){a.value=s,a.getSnapshot=o,Dv(a)&&kv(e)}function xv(e,a,s){return s(function(){Dv(a)&&kv(e)})}function Dv(e){var a=e.getSnapshot;e=e.value;try{var s=a();return!fn(e,s)}catch{return!0}}function kv(e){var a=Fs(e,2);a!==null&&gn(a,e,2)}function Yh(e){var a=nn();if(typeof e=="function"){var s=e;if(e=s(),Fr){Ia(!0);try{s()}finally{Ia(!1)}}}return a.memoizedState=a.baseState=e,a.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ea,lastRenderedState:e},a}function Nv(e,a,s,o){return e.baseState=s,Kh(e,Kt,typeof o=="function"?o:Ea)}function YM(e,a,s,o,c){if(Zc(e))throw Error(i(485));if(e=a.action,e!==null){var d={payload:c,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){d.listeners.push(y)}};C.T!==null?s(!0):d.isTransition=!1,o(d),s=a.pending,s===null?(d.next=a.pending=d,zv(a,d)):(d.next=s.next,a.pending=s.next=d)}}function zv(e,a){var s=a.action,o=a.payload,c=e.state;if(a.isTransition){var d=C.T,y={};C.T=y;try{var _=s(c,o),S=C.S;S!==null&&S(y,_),Lv(e,a,_)}catch(k){Xh(e,a,k)}finally{C.T=d}}else try{d=s(c,o),Lv(e,a,d)}catch(k){Xh(e,a,k)}}function Lv(e,a,s){s!==null&&typeof s=="object"&&typeof s.then=="function"?s.then(function(o){$v(e,a,o)},function(o){return Xh(e,a,o)}):$v(e,a,s)}function $v(e,a,s){a.status="fulfilled",a.value=s,Uv(a),e.state=s,a=e.pending,a!==null&&(s=a.next,s===a?e.pending=null:(s=s.next,a.next=s,zv(e,s)))}function Xh(e,a,s){var o=e.pending;if(e.pending=null,o!==null){o=o.next;do a.status="rejected",a.reason=s,Uv(a),a=a.next;while(a!==o)}e.action=null}function Uv(e){e=e.listeners;for(var a=0;a<e.length;a++)(0,e[a])()}function Fv(e,a){return a}function jv(e,a){if(qt){var s=ee.formState;if(s!==null){t:{var o=Ct;if(qt){if(de){e:{for(var c=de,d=Zn;c.nodeType!==8;){if(!d){c=null;break e}if(c=Bn(c.nextSibling),c===null){c=null;break e}}d=c.data,c=d==="F!"||d==="F"?c:null}if(c){de=Bn(c.nextSibling),o=c.data==="F!";break t}}zr(o)}o=!1}o&&(a=s[0])}}return s=nn(),s.memoizedState=s.baseState=a,o={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Fv,lastRenderedState:a},s.queue=o,s=r_.bind(null,Ct,o),o.dispatch=s,o=Yh(!1),d=em.bind(null,Ct,!1,o.queue),o=nn(),c={state:a,dispatch:null,action:e,pending:null},o.queue=c,s=YM.bind(null,Ct,c,d,s),c.dispatch=s,o.memoizedState=e,[a,s,!1]}function Bv(e){var a=Te();return qv(a,Kt,e)}function qv(e,a,s){if(a=Kh(e,a,Fv)[0],e=Qc(Ea)[0],typeof a=="object"&&a!==null&&typeof a.then=="function")try{var o=Fo(a)}catch(y){throw y===ko?Hc:y}else o=a;a=Te();var c=a.queue,d=c.dispatch;return s!==a.memoizedState&&(Ct.flags|=2048,Ks(9,Yc(),XM.bind(null,c,s),null)),[o,d,e]}function XM(e,a){e.action=a}function Hv(e){var a=Te(),s=Kt;if(s!==null)return qv(a,s,e);Te(),a=a.memoizedState,s=Te();var o=s.queue.dispatch;return s.memoizedState=e,[a,o,!1]}function Ks(e,a,s,o){return e={tag:e,create:s,deps:o,inst:a,next:null},a=Ct.updateQueue,a===null&&(a=Vh(),Ct.updateQueue=a),s=a.lastEffect,s===null?a.lastEffect=e.next=e:(o=s.next,s.next=e,e.next=o,a.lastEffect=e),e}function Yc(){return{destroy:void 0,resource:void 0}}function Iv(){return Te().memoizedState}function Xc(e,a,s,o){var c=nn();o=o===void 0?null:o,Ct.flags|=e,c.memoizedState=Ks(1|a,Yc(),s,o)}function jo(e,a,s,o){var c=Te();o=o===void 0?null:o;var d=c.memoizedState.inst;Kt!==null&&o!==null&&Bh(o,Kt.memoizedState.deps)?c.memoizedState=Ks(a,d,s,o):(Ct.flags|=e,c.memoizedState=Ks(1|a,d,s,o))}function Pv(e,a){Xc(8390656,8,e,a)}function Vv(e,a){jo(2048,8,e,a)}function Gv(e,a){return jo(4,2,e,a)}function Kv(e,a){return jo(4,4,e,a)}function Qv(e,a){if(typeof a=="function"){e=e();var s=a(e);return function(){typeof s=="function"?s():a(null)}}if(a!=null)return e=e(),a.current=e,function(){a.current=null}}function Yv(e,a,s){s=s!=null?s.concat([e]):null,jo(4,4,Qv.bind(null,a,e),s)}function Zh(){}function Xv(e,a){var s=Te();a=a===void 0?null:a;var o=s.memoizedState;return a!==null&&Bh(a,o[1])?o[0]:(s.memoizedState=[e,a],e)}function Zv(e,a){var s=Te();a=a===void 0?null:a;var o=s.memoizedState;if(a!==null&&Bh(a,o[1]))return o[0];if(o=e(),Fr){Ia(!0);try{e()}finally{Ia(!1)}}return s.memoizedState=[o,a],o}function Jh(e,a,s){return s===void 0||(Xa&1073741824)!==0?e.memoizedState=a:(e.memoizedState=s,e=tb(),Ct.lanes|=e,ar|=e,s)}function Jv(e,a,s,o){return fn(s,a)?s:Ps.current!==null?(e=Jh(e,s,o),fn(e,a)||(Ne=!0),e):(Xa&42)===0?(Ne=!0,e.memoizedState=s):(e=tb(),Ct.lanes|=e,ar|=e,a)}function Wv(e,a,s,o,c){var d=I.p;I.p=d!==0&&8>d?d:8;var y=C.T,_={};C.T=_,em(e,!1,a,s);try{var S=c(),k=C.S;if(k!==null&&k(_,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var H=GM(S,o);Bo(e,a,H,yn(e))}else Bo(e,a,o,yn(e))}catch(V){Bo(e,a,{then:function(){},status:"rejected",reason:V},yn())}finally{I.p=d,C.T=y}}function ZM(){}function Wh(e,a,s,o){if(e.tag!==5)throw Error(i(476));var c=t_(e).queue;Wv(e,c,a,st,s===null?ZM:function(){return e_(e),s(o)})}function t_(e){var a=e.memoizedState;if(a!==null)return a;a={memoizedState:st,baseState:st,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ea,lastRenderedState:st},next:null};var s={};return a.next={memoizedState:s,baseState:s,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ea,lastRenderedState:s},next:null},e.memoizedState=a,e=e.alternate,e!==null&&(e.memoizedState=a),a}function e_(e){var a=t_(e).next.queue;Bo(e,a,{},yn())}function tm(){return Ie(rl)}function n_(){return Te().memoizedState}function a_(){return Te().memoizedState}function JM(e){for(var a=e.return;a!==null;){switch(a.tag){case 24:case 3:var s=yn();e=Qa(s);var o=Ya(a,e,s);o!==null&&(gn(o,a,s),zo(o,a,s)),a={cache:xh()},e.payload=a;return}a=a.return}}function WM(e,a,s){var o=yn();s={lane:o,revertLane:0,action:s,hasEagerState:!1,eagerState:null,next:null},Zc(e)?s_(a,s):(s=bh(e,a,s,o),s!==null&&(gn(s,e,o),i_(s,a,o)))}function r_(e,a,s){var o=yn();Bo(e,a,s,o)}function Bo(e,a,s,o){var c={lane:o,revertLane:0,action:s,hasEagerState:!1,eagerState:null,next:null};if(Zc(e))s_(a,c);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=a.lastRenderedReducer,d!==null))try{var y=a.lastRenderedState,_=d(y,s);if(c.hasEagerState=!0,c.eagerState=_,fn(_,y))return zc(e,a,c,0),ee===null&&Nc(),!1}catch{}finally{}if(s=bh(e,a,c,o),s!==null)return gn(s,e,o),i_(s,a,o),!0}return!1}function em(e,a,s,o){if(o={lane:2,revertLane:Nm(),action:o,hasEagerState:!1,eagerState:null,next:null},Zc(e)){if(a)throw Error(i(479))}else a=bh(e,s,o,2),a!==null&&gn(a,e,2)}function Zc(e){var a=e.alternate;return e===Ct||a!==null&&a===Ct}function s_(e,a){Vs=Vc=!0;var s=e.pending;s===null?a.next=a:(a.next=s.next,s.next=a),e.pending=a}function i_(e,a,s){if((s&4194048)!==0){var o=a.lanes;o&=e.pendingLanes,s|=o,a.lanes=s,m0(e,s)}}var Jc={readContext:Ie,use:Kc,useCallback:ye,useContext:ye,useEffect:ye,useImperativeHandle:ye,useLayoutEffect:ye,useInsertionEffect:ye,useMemo:ye,useReducer:ye,useRef:ye,useState:ye,useDebugValue:ye,useDeferredValue:ye,useTransition:ye,useSyncExternalStore:ye,useId:ye,useHostTransitionStatus:ye,useFormState:ye,useActionState:ye,useOptimistic:ye,useMemoCache:ye,useCacheRefresh:ye},o_={readContext:Ie,use:Kc,useCallback:function(e,a){return nn().memoizedState=[e,a===void 0?null:a],e},useContext:Ie,useEffect:Pv,useImperativeHandle:function(e,a,s){s=s!=null?s.concat([e]):null,Xc(4194308,4,Qv.bind(null,a,e),s)},useLayoutEffect:function(e,a){return Xc(4194308,4,e,a)},useInsertionEffect:function(e,a){Xc(4,2,e,a)},useMemo:function(e,a){var s=nn();a=a===void 0?null:a;var o=e();if(Fr){Ia(!0);try{e()}finally{Ia(!1)}}return s.memoizedState=[o,a],o},useReducer:function(e,a,s){var o=nn();if(s!==void 0){var c=s(a);if(Fr){Ia(!0);try{s(a)}finally{Ia(!1)}}}else c=a;return o.memoizedState=o.baseState=c,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:c},o.queue=e,e=e.dispatch=WM.bind(null,Ct,e),[o.memoizedState,e]},useRef:function(e){var a=nn();return e={current:e},a.memoizedState=e},useState:function(e){e=Yh(e);var a=e.queue,s=r_.bind(null,Ct,a);return a.dispatch=s,[e.memoizedState,s]},useDebugValue:Zh,useDeferredValue:function(e,a){var s=nn();return Jh(s,e,a)},useTransition:function(){var e=Yh(!1);return e=Wv.bind(null,Ct,e.queue,!0,!1),nn().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,a,s){var o=Ct,c=nn();if(qt){if(s===void 0)throw Error(i(407));s=s()}else{if(s=a(),ee===null)throw Error(i(349));(Ut&124)!==0||Av(o,a,s)}c.memoizedState=s;var d={value:s,getSnapshot:a};return c.queue=d,Pv(xv.bind(null,o,d,e),[e]),o.flags|=2048,Ks(9,Yc(),Cv.bind(null,o,d,s,a),null),s},useId:function(){var e=nn(),a=ee.identifierPrefix;if(qt){var s=_a,o=va;s=(o&~(1<<32-un(o)-1)).toString(32)+s,a="«"+a+"R"+s,s=Gc++,0<s&&(a+="H"+s.toString(32)),a+="»"}else s=KM++,a="«"+a+"r"+s.toString(32)+"»";return e.memoizedState=a},useHostTransitionStatus:tm,useFormState:jv,useActionState:jv,useOptimistic:function(e){var a=nn();a.memoizedState=a.baseState=e;var s={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return a.queue=s,a=em.bind(null,Ct,!0,s),s.dispatch=a,[e,a]},useMemoCache:Gh,useCacheRefresh:function(){return nn().memoizedState=JM.bind(null,Ct)}},l_={readContext:Ie,use:Kc,useCallback:Xv,useContext:Ie,useEffect:Vv,useImperativeHandle:Yv,useInsertionEffect:Gv,useLayoutEffect:Kv,useMemo:Zv,useReducer:Qc,useRef:Iv,useState:function(){return Qc(Ea)},useDebugValue:Zh,useDeferredValue:function(e,a){var s=Te();return Jv(s,Kt.memoizedState,e,a)},useTransition:function(){var e=Qc(Ea)[0],a=Te().memoizedState;return[typeof e=="boolean"?e:Fo(e),a]},useSyncExternalStore:wv,useId:n_,useHostTransitionStatus:tm,useFormState:Bv,useActionState:Bv,useOptimistic:function(e,a){var s=Te();return Nv(s,Kt,e,a)},useMemoCache:Gh,useCacheRefresh:a_},tw={readContext:Ie,use:Kc,useCallback:Xv,useContext:Ie,useEffect:Vv,useImperativeHandle:Yv,useInsertionEffect:Gv,useLayoutEffect:Kv,useMemo:Zv,useReducer:Qh,useRef:Iv,useState:function(){return Qh(Ea)},useDebugValue:Zh,useDeferredValue:function(e,a){var s=Te();return Kt===null?Jh(s,e,a):Jv(s,Kt.memoizedState,e,a)},useTransition:function(){var e=Qh(Ea)[0],a=Te().memoizedState;return[typeof e=="boolean"?e:Fo(e),a]},useSyncExternalStore:wv,useId:n_,useHostTransitionStatus:tm,useFormState:Hv,useActionState:Hv,useOptimistic:function(e,a){var s=Te();return Kt!==null?Nv(s,Kt,e,a):(s.baseState=e,[e,s.queue.dispatch])},useMemoCache:Gh,useCacheRefresh:a_},Qs=null,qo=0;function Wc(e){var a=qo;return qo+=1,Qs===null&&(Qs=[]),_v(Qs,e,a)}function Ho(e,a){a=a.props.ref,e.ref=a!==void 0?a:null}function tu(e,a){throw a.$$typeof===v?Error(i(525)):(e=Object.prototype.toString.call(a),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(a).join(", ")+"}":e)))}function c_(e){var a=e._init;return a(e._payload)}function u_(e){function a(w,M){if(e){var D=w.deletions;D===null?(w.deletions=[M],w.flags|=16):D.push(M)}}function s(w,M){if(!e)return null;for(;M!==null;)a(w,M),M=M.sibling;return null}function o(w){for(var M=new Map;w!==null;)w.key!==null?M.set(w.key,w):M.set(w.index,w),w=w.sibling;return M}function c(w,M){return w=ga(w,M),w.index=0,w.sibling=null,w}function d(w,M,D){return w.index=D,e?(D=w.alternate,D!==null?(D=D.index,D<M?(w.flags|=67108866,M):D):(w.flags|=67108866,M)):(w.flags|=1048576,M)}function y(w){return e&&w.alternate===null&&(w.flags|=67108866),w}function _(w,M,D,P){return M===null||M.tag!==6?(M=Eh(D,w.mode,P),M.return=w,M):(M=c(M,D),M.return=w,M)}function S(w,M,D,P){var ct=D.type;return ct===R?H(w,M,D.props.children,P,D.key):M!==null&&(M.elementType===ct||typeof ct=="object"&&ct!==null&&ct.$$typeof===N&&c_(ct)===M.type)?(M=c(M,D.props),Ho(M,D),M.return=w,M):(M=$c(D.type,D.key,D.props,null,w.mode,P),Ho(M,D),M.return=w,M)}function k(w,M,D,P){return M===null||M.tag!==4||M.stateNode.containerInfo!==D.containerInfo||M.stateNode.implementation!==D.implementation?(M=Th(D,w.mode,P),M.return=w,M):(M=c(M,D.children||[]),M.return=w,M)}function H(w,M,D,P,ct){return M===null||M.tag!==7?(M=xr(D,w.mode,P,ct),M.return=w,M):(M=c(M,D),M.return=w,M)}function V(w,M,D){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return M=Eh(""+M,w.mode,D),M.return=w,M;if(typeof M=="object"&&M!==null){switch(M.$$typeof){case b:return D=$c(M.type,M.key,M.props,null,w.mode,D),Ho(D,M),D.return=w,D;case O:return M=Th(M,w.mode,D),M.return=w,M;case N:var P=M._init;return M=P(M._payload),V(w,M,D)}if(ut(M)||Y(M))return M=xr(M,w.mode,D,null),M.return=w,M;if(typeof M.then=="function")return V(w,Wc(M),D);if(M.$$typeof===q)return V(w,Bc(w,M),D);tu(w,M)}return null}function z(w,M,D,P){var ct=M!==null?M.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return ct!==null?null:_(w,M,""+D,P);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case b:return D.key===ct?S(w,M,D,P):null;case O:return D.key===ct?k(w,M,D,P):null;case N:return ct=D._init,D=ct(D._payload),z(w,M,D,P)}if(ut(D)||Y(D))return ct!==null?null:H(w,M,D,P,null);if(typeof D.then=="function")return z(w,M,Wc(D),P);if(D.$$typeof===q)return z(w,M,Bc(w,D),P);tu(w,D)}return null}function $(w,M,D,P,ct){if(typeof P=="string"&&P!==""||typeof P=="number"||typeof P=="bigint")return w=w.get(D)||null,_(M,w,""+P,ct);if(typeof P=="object"&&P!==null){switch(P.$$typeof){case b:return w=w.get(P.key===null?D:P.key)||null,S(M,w,P,ct);case O:return w=w.get(P.key===null?D:P.key)||null,k(M,w,P,ct);case N:var xt=P._init;return P=xt(P._payload),$(w,M,D,P,ct)}if(ut(P)||Y(P))return w=w.get(D)||null,H(M,w,P,ct,null);if(typeof P.then=="function")return $(w,M,D,Wc(P),ct);if(P.$$typeof===q)return $(w,M,D,Bc(M,P),ct);tu(M,P)}return null}function _t(w,M,D,P){for(var ct=null,xt=null,ht=M,vt=M=0,Le=null;ht!==null&&vt<D.length;vt++){ht.index>vt?(Le=ht,ht=null):Le=ht.sibling;var jt=z(w,ht,D[vt],P);if(jt===null){ht===null&&(ht=Le);break}e&&ht&&jt.alternate===null&&a(w,ht),M=d(jt,M,vt),xt===null?ct=jt:xt.sibling=jt,xt=jt,ht=Le}if(vt===D.length)return s(w,ht),qt&&kr(w,vt),ct;if(ht===null){for(;vt<D.length;vt++)ht=V(w,D[vt],P),ht!==null&&(M=d(ht,M,vt),xt===null?ct=ht:xt.sibling=ht,xt=ht);return qt&&kr(w,vt),ct}for(ht=o(ht);vt<D.length;vt++)Le=$(ht,w,vt,D[vt],P),Le!==null&&(e&&Le.alternate!==null&&ht.delete(Le.key===null?vt:Le.key),M=d(Le,M,vt),xt===null?ct=Le:xt.sibling=Le,xt=Le);return e&&ht.forEach(function(dr){return a(w,dr)}),qt&&kr(w,vt),ct}function gt(w,M,D,P){if(D==null)throw Error(i(151));for(var ct=null,xt=null,ht=M,vt=M=0,Le=null,jt=D.next();ht!==null&&!jt.done;vt++,jt=D.next()){ht.index>vt?(Le=ht,ht=null):Le=ht.sibling;var dr=z(w,ht,jt.value,P);if(dr===null){ht===null&&(ht=Le);break}e&&ht&&dr.alternate===null&&a(w,ht),M=d(dr,M,vt),xt===null?ct=dr:xt.sibling=dr,xt=dr,ht=Le}if(jt.done)return s(w,ht),qt&&kr(w,vt),ct;if(ht===null){for(;!jt.done;vt++,jt=D.next())jt=V(w,jt.value,P),jt!==null&&(M=d(jt,M,vt),xt===null?ct=jt:xt.sibling=jt,xt=jt);return qt&&kr(w,vt),ct}for(ht=o(ht);!jt.done;vt++,jt=D.next())jt=$(ht,w,vt,jt.value,P),jt!==null&&(e&&jt.alternate!==null&&ht.delete(jt.key===null?vt:jt.key),M=d(jt,M,vt),xt===null?ct=jt:xt.sibling=jt,xt=jt);return e&&ht.forEach(function(eA){return a(w,eA)}),qt&&kr(w,vt),ct}function Yt(w,M,D,P){if(typeof D=="object"&&D!==null&&D.type===R&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case b:t:{for(var ct=D.key;M!==null;){if(M.key===ct){if(ct=D.type,ct===R){if(M.tag===7){s(w,M.sibling),P=c(M,D.props.children),P.return=w,w=P;break t}}else if(M.elementType===ct||typeof ct=="object"&&ct!==null&&ct.$$typeof===N&&c_(ct)===M.type){s(w,M.sibling),P=c(M,D.props),Ho(P,D),P.return=w,w=P;break t}s(w,M);break}else a(w,M);M=M.sibling}D.type===R?(P=xr(D.props.children,w.mode,P,D.key),P.return=w,w=P):(P=$c(D.type,D.key,D.props,null,w.mode,P),Ho(P,D),P.return=w,w=P)}return y(w);case O:t:{for(ct=D.key;M!==null;){if(M.key===ct)if(M.tag===4&&M.stateNode.containerInfo===D.containerInfo&&M.stateNode.implementation===D.implementation){s(w,M.sibling),P=c(M,D.children||[]),P.return=w,w=P;break t}else{s(w,M);break}else a(w,M);M=M.sibling}P=Th(D,w.mode,P),P.return=w,w=P}return y(w);case N:return ct=D._init,D=ct(D._payload),Yt(w,M,D,P)}if(ut(D))return _t(w,M,D,P);if(Y(D)){if(ct=Y(D),typeof ct!="function")throw Error(i(150));return D=ct.call(D),gt(w,M,D,P)}if(typeof D.then=="function")return Yt(w,M,Wc(D),P);if(D.$$typeof===q)return Yt(w,M,Bc(w,D),P);tu(w,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,M!==null&&M.tag===6?(s(w,M.sibling),P=c(M,D),P.return=w,w=P):(s(w,M),P=Eh(D,w.mode,P),P.return=w,w=P),y(w)):s(w,M)}return function(w,M,D,P){try{qo=0;var ct=Yt(w,M,D,P);return Qs=null,ct}catch(ht){if(ht===ko||ht===Hc)throw ht;var xt=dn(29,ht,null,w.mode);return xt.lanes=P,xt.return=w,xt}finally{}}}var Ys=u_(!0),f_=u_(!1),Mn=F(null),Jn=null;function Za(e){var a=e.alternate;J(Me,Me.current&1),J(Mn,e),Jn===null&&(a===null||Ps.current!==null||a.memoizedState!==null)&&(Jn=e)}function d_(e){if(e.tag===22){if(J(Me,Me.current),J(Mn,e),Jn===null){var a=e.alternate;a!==null&&a.memoizedState!==null&&(Jn=e)}}else Ja()}function Ja(){J(Me,Me.current),J(Mn,Mn.current)}function Ta(e){nt(Mn),Jn===e&&(Jn=null),nt(Me)}var Me=F(0);function eu(e){for(var a=e;a!==null;){if(a.tag===13){var s=a.memoizedState;if(s!==null&&(s=s.dehydrated,s===null||s.data==="$?"||Vm(s)))return a}else if(a.tag===19&&a.memoizedProps.revealOrder!==void 0){if((a.flags&128)!==0)return a}else if(a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break;for(;a.sibling===null;){if(a.return===null||a.return===e)return null;a=a.return}a.sibling.return=a.return,a=a.sibling}return null}function nm(e,a,s,o){a=e.memoizedState,s=s(o,a),s=s==null?a:g({},a,s),e.memoizedState=s,e.lanes===0&&(e.updateQueue.baseState=s)}var am={enqueueSetState:function(e,a,s){e=e._reactInternals;var o=yn(),c=Qa(o);c.payload=a,s!=null&&(c.callback=s),a=Ya(e,c,o),a!==null&&(gn(a,e,o),zo(a,e,o))},enqueueReplaceState:function(e,a,s){e=e._reactInternals;var o=yn(),c=Qa(o);c.tag=1,c.payload=a,s!=null&&(c.callback=s),a=Ya(e,c,o),a!==null&&(gn(a,e,o),zo(a,e,o))},enqueueForceUpdate:function(e,a){e=e._reactInternals;var s=yn(),o=Qa(s);o.tag=2,a!=null&&(o.callback=a),a=Ya(e,o,s),a!==null&&(gn(a,e,s),zo(a,e,s))}};function h_(e,a,s,o,c,d,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,d,y):a.prototype&&a.prototype.isPureReactComponent?!Oo(s,o)||!Oo(c,d):!0}function m_(e,a,s,o){e=a.state,typeof a.componentWillReceiveProps=="function"&&a.componentWillReceiveProps(s,o),typeof a.UNSAFE_componentWillReceiveProps=="function"&&a.UNSAFE_componentWillReceiveProps(s,o),a.state!==e&&am.enqueueReplaceState(a,a.state,null)}function jr(e,a){var s=a;if("ref"in a){s={};for(var o in a)o!=="ref"&&(s[o]=a[o])}if(e=e.defaultProps){s===a&&(s=g({},s));for(var c in e)s[c]===void 0&&(s[c]=e[c])}return s}var nu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var a=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(a))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function p_(e){nu(e)}function y_(e){console.error(e)}function g_(e){nu(e)}function au(e,a){try{var s=e.onUncaughtError;s(a.value,{componentStack:a.stack})}catch(o){setTimeout(function(){throw o})}}function v_(e,a,s){try{var o=e.onCaughtError;o(s.value,{componentStack:s.stack,errorBoundary:a.tag===1?a.stateNode:null})}catch(c){setTimeout(function(){throw c})}}function rm(e,a,s){return s=Qa(s),s.tag=3,s.payload={element:null},s.callback=function(){au(e,a)},s}function __(e){return e=Qa(e),e.tag=3,e}function b_(e,a,s,o){var c=s.type.getDerivedStateFromError;if(typeof c=="function"){var d=o.value;e.payload=function(){return c(d)},e.callback=function(){v_(a,s,o)}}var y=s.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(e.callback=function(){v_(a,s,o),typeof c!="function"&&(rr===null?rr=new Set([this]):rr.add(this));var _=o.stack;this.componentDidCatch(o.value,{componentStack:_!==null?_:""})})}function ew(e,a,s,o,c){if(s.flags|=32768,o!==null&&typeof o=="object"&&typeof o.then=="function"){if(a=s.alternate,a!==null&&Co(a,s,c,!0),s=Mn.current,s!==null){switch(s.tag){case 13:return Jn===null?Am():s.alternate===null&&he===0&&(he=3),s.flags&=-257,s.flags|=65536,s.lanes=c,o===Nh?s.flags|=16384:(a=s.updateQueue,a===null?s.updateQueue=new Set([o]):a.add(o),xm(e,o,c)),!1;case 22:return s.flags|=65536,o===Nh?s.flags|=16384:(a=s.updateQueue,a===null?(a={transitions:null,markerInstances:null,retryQueue:new Set([o])},s.updateQueue=a):(s=a.retryQueue,s===null?a.retryQueue=new Set([o]):s.add(o)),xm(e,o,c)),!1}throw Error(i(435,s.tag))}return xm(e,o,c),Am(),!1}if(qt)return a=Mn.current,a!==null?((a.flags&65536)===0&&(a.flags|=256),a.flags|=65536,a.lanes=c,o!==Mh&&(e=Error(i(422),{cause:o}),Ao(En(e,s)))):(o!==Mh&&(a=Error(i(423),{cause:o}),Ao(En(a,s))),e=e.current.alternate,e.flags|=65536,c&=-c,e.lanes|=c,o=En(o,s),c=rm(e.stateNode,o,c),$h(e,c),he!==4&&(he=2)),!1;var d=Error(i(520),{cause:o});if(d=En(d,s),Yo===null?Yo=[d]:Yo.push(d),he!==4&&(he=2),a===null)return!0;o=En(o,s),s=a;do{switch(s.tag){case 3:return s.flags|=65536,e=c&-c,s.lanes|=e,e=rm(s.stateNode,o,e),$h(s,e),!1;case 1:if(a=s.type,d=s.stateNode,(s.flags&128)===0&&(typeof a.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(rr===null||!rr.has(d))))return s.flags|=65536,c&=-c,s.lanes|=c,c=__(c),b_(c,e,s,o),$h(s,c),!1}s=s.return}while(s!==null);return!1}var S_=Error(i(461)),Ne=!1;function Ue(e,a,s,o){a.child=e===null?f_(a,null,s,o):Ys(a,e.child,s,o)}function E_(e,a,s,o,c){s=s.render;var d=a.ref;if("ref"in o){var y={};for(var _ in o)_!=="ref"&&(y[_]=o[_])}else y=o;return $r(a),o=qh(e,a,s,y,d,c),_=Hh(),e!==null&&!Ne?(Ih(e,a,c),Oa(e,a,c)):(qt&&_&&Oh(a),a.flags|=1,Ue(e,a,o,c),a.child)}function T_(e,a,s,o,c){if(e===null){var d=s.type;return typeof d=="function"&&!Sh(d)&&d.defaultProps===void 0&&s.compare===null?(a.tag=15,a.type=d,O_(e,a,d,o,c)):(e=$c(s.type,null,o,a,a.mode,c),e.ref=a.ref,e.return=a,a.child=e)}if(d=e.child,!dm(e,c)){var y=d.memoizedProps;if(s=s.compare,s=s!==null?s:Oo,s(y,o)&&e.ref===a.ref)return Oa(e,a,c)}return a.flags|=1,e=ga(d,o),e.ref=a.ref,e.return=a,a.child=e}function O_(e,a,s,o,c){if(e!==null){var d=e.memoizedProps;if(Oo(d,o)&&e.ref===a.ref)if(Ne=!1,a.pendingProps=o=d,dm(e,c))(e.flags&131072)!==0&&(Ne=!0);else return a.lanes=e.lanes,Oa(e,a,c)}return sm(e,a,s,o,c)}function R_(e,a,s){var o=a.pendingProps,c=o.children,d=e!==null?e.memoizedState:null;if(o.mode==="hidden"){if((a.flags&128)!==0){if(o=d!==null?d.baseLanes|s:s,e!==null){for(c=a.child=e.child,d=0;c!==null;)d=d|c.lanes|c.childLanes,c=c.sibling;a.childLanes=d&~o}else a.childLanes=0,a.child=null;return M_(e,a,o,s)}if((s&536870912)!==0)a.memoizedState={baseLanes:0,cachePool:null},e!==null&&qc(a,d!==null?d.cachePool:null),d!==null?Ov(a,d):Fh(),d_(a);else return a.lanes=a.childLanes=536870912,M_(e,a,d!==null?d.baseLanes|s:s,s)}else d!==null?(qc(a,d.cachePool),Ov(a,d),Ja(),a.memoizedState=null):(e!==null&&qc(a,null),Fh(),Ja());return Ue(e,a,c,s),a.child}function M_(e,a,s,o){var c=kh();return c=c===null?null:{parent:Re._currentValue,pool:c},a.memoizedState={baseLanes:s,cachePool:c},e!==null&&qc(a,null),Fh(),d_(a),e!==null&&Co(e,a,o,!0),null}function ru(e,a){var s=a.ref;if(s===null)e!==null&&e.ref!==null&&(a.flags|=4194816);else{if(typeof s!="function"&&typeof s!="object")throw Error(i(284));(e===null||e.ref!==s)&&(a.flags|=4194816)}}function sm(e,a,s,o,c){return $r(a),s=qh(e,a,s,o,void 0,c),o=Hh(),e!==null&&!Ne?(Ih(e,a,c),Oa(e,a,c)):(qt&&o&&Oh(a),a.flags|=1,Ue(e,a,s,c),a.child)}function w_(e,a,s,o,c,d){return $r(a),a.updateQueue=null,s=Mv(a,o,s,c),Rv(e),o=Hh(),e!==null&&!Ne?(Ih(e,a,d),Oa(e,a,d)):(qt&&o&&Oh(a),a.flags|=1,Ue(e,a,s,d),a.child)}function A_(e,a,s,o,c){if($r(a),a.stateNode===null){var d=js,y=s.contextType;typeof y=="object"&&y!==null&&(d=Ie(y)),d=new s(o,d),a.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,d.updater=am,a.stateNode=d,d._reactInternals=a,d=a.stateNode,d.props=o,d.state=a.memoizedState,d.refs={},zh(a),y=s.contextType,d.context=typeof y=="object"&&y!==null?Ie(y):js,d.state=a.memoizedState,y=s.getDerivedStateFromProps,typeof y=="function"&&(nm(a,s,y,o),d.state=a.memoizedState),typeof s.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(y=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),y!==d.state&&am.enqueueReplaceState(d,d.state,null),$o(a,o,d,c),Lo(),d.state=a.memoizedState),typeof d.componentDidMount=="function"&&(a.flags|=4194308),o=!0}else if(e===null){d=a.stateNode;var _=a.memoizedProps,S=jr(s,_);d.props=S;var k=d.context,H=s.contextType;y=js,typeof H=="object"&&H!==null&&(y=Ie(H));var V=s.getDerivedStateFromProps;H=typeof V=="function"||typeof d.getSnapshotBeforeUpdate=="function",_=a.pendingProps!==_,H||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(_||k!==y)&&m_(a,d,o,y),Ka=!1;var z=a.memoizedState;d.state=z,$o(a,o,d,c),Lo(),k=a.memoizedState,_||z!==k||Ka?(typeof V=="function"&&(nm(a,s,V,o),k=a.memoizedState),(S=Ka||h_(a,s,S,o,z,k,y))?(H||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount()),typeof d.componentDidMount=="function"&&(a.flags|=4194308)):(typeof d.componentDidMount=="function"&&(a.flags|=4194308),a.memoizedProps=o,a.memoizedState=k),d.props=o,d.state=k,d.context=y,o=S):(typeof d.componentDidMount=="function"&&(a.flags|=4194308),o=!1)}else{d=a.stateNode,Lh(e,a),y=a.memoizedProps,H=jr(s,y),d.props=H,V=a.pendingProps,z=d.context,k=s.contextType,S=js,typeof k=="object"&&k!==null&&(S=Ie(k)),_=s.getDerivedStateFromProps,(k=typeof _=="function"||typeof d.getSnapshotBeforeUpdate=="function")||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(y!==V||z!==S)&&m_(a,d,o,S),Ka=!1,z=a.memoizedState,d.state=z,$o(a,o,d,c),Lo();var $=a.memoizedState;y!==V||z!==$||Ka||e!==null&&e.dependencies!==null&&jc(e.dependencies)?(typeof _=="function"&&(nm(a,s,_,o),$=a.memoizedState),(H=Ka||h_(a,s,H,o,z,$,S)||e!==null&&e.dependencies!==null&&jc(e.dependencies))?(k||typeof d.UNSAFE_componentWillUpdate!="function"&&typeof d.componentWillUpdate!="function"||(typeof d.componentWillUpdate=="function"&&d.componentWillUpdate(o,$,S),typeof d.UNSAFE_componentWillUpdate=="function"&&d.UNSAFE_componentWillUpdate(o,$,S)),typeof d.componentDidUpdate=="function"&&(a.flags|=4),typeof d.getSnapshotBeforeUpdate=="function"&&(a.flags|=1024)):(typeof d.componentDidUpdate!="function"||y===e.memoizedProps&&z===e.memoizedState||(a.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&z===e.memoizedState||(a.flags|=1024),a.memoizedProps=o,a.memoizedState=$),d.props=o,d.state=$,d.context=S,o=H):(typeof d.componentDidUpdate!="function"||y===e.memoizedProps&&z===e.memoizedState||(a.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&z===e.memoizedState||(a.flags|=1024),o=!1)}return d=o,ru(e,a),o=(a.flags&128)!==0,d||o?(d=a.stateNode,s=o&&typeof s.getDerivedStateFromError!="function"?null:d.render(),a.flags|=1,e!==null&&o?(a.child=Ys(a,e.child,null,c),a.child=Ys(a,null,s,c)):Ue(e,a,s,c),a.memoizedState=d.state,e=a.child):e=Oa(e,a,c),e}function C_(e,a,s,o){return wo(),a.flags|=256,Ue(e,a,s,o),a.child}var im={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function om(e){return{baseLanes:e,cachePool:yv()}}function lm(e,a,s){return e=e!==null?e.childLanes&~s:0,a&&(e|=wn),e}function x_(e,a,s){var o=a.pendingProps,c=!1,d=(a.flags&128)!==0,y;if((y=d)||(y=e!==null&&e.memoizedState===null?!1:(Me.current&2)!==0),y&&(c=!0,a.flags&=-129),y=(a.flags&32)!==0,a.flags&=-33,e===null){if(qt){if(c?Za(a):Ja(),qt){var _=de,S;if(S=_){t:{for(S=_,_=Zn;S.nodeType!==8;){if(!_){_=null;break t}if(S=Bn(S.nextSibling),S===null){_=null;break t}}_=S}_!==null?(a.memoizedState={dehydrated:_,treeContext:Dr!==null?{id:va,overflow:_a}:null,retryLane:536870912,hydrationErrors:null},S=dn(18,null,null,0),S.stateNode=_,S.return=a,a.child=S,Qe=a,de=null,S=!0):S=!1}S||zr(a)}if(_=a.memoizedState,_!==null&&(_=_.dehydrated,_!==null))return Vm(_)?a.lanes=32:a.lanes=536870912,null;Ta(a)}return _=o.children,o=o.fallback,c?(Ja(),c=a.mode,_=su({mode:"hidden",children:_},c),o=xr(o,c,s,null),_.return=a,o.return=a,_.sibling=o,a.child=_,c=a.child,c.memoizedState=om(s),c.childLanes=lm(e,y,s),a.memoizedState=im,o):(Za(a),cm(a,_))}if(S=e.memoizedState,S!==null&&(_=S.dehydrated,_!==null)){if(d)a.flags&256?(Za(a),a.flags&=-257,a=um(e,a,s)):a.memoizedState!==null?(Ja(),a.child=e.child,a.flags|=128,a=null):(Ja(),c=o.fallback,_=a.mode,o=su({mode:"visible",children:o.children},_),c=xr(c,_,s,null),c.flags|=2,o.return=a,c.return=a,o.sibling=c,a.child=o,Ys(a,e.child,null,s),o=a.child,o.memoizedState=om(s),o.childLanes=lm(e,y,s),a.memoizedState=im,a=c);else if(Za(a),Vm(_)){if(y=_.nextSibling&&_.nextSibling.dataset,y)var k=y.dgst;y=k,o=Error(i(419)),o.stack="",o.digest=y,Ao({value:o,source:null,stack:null}),a=um(e,a,s)}else if(Ne||Co(e,a,s,!1),y=(s&e.childLanes)!==0,Ne||y){if(y=ee,y!==null&&(o=s&-s,o=(o&42)!==0?1:Gd(o),o=(o&(y.suspendedLanes|s))!==0?0:o,o!==0&&o!==S.retryLane))throw S.retryLane=o,Fs(e,o),gn(y,e,o),S_;_.data==="$?"||Am(),a=um(e,a,s)}else _.data==="$?"?(a.flags|=192,a.child=e.child,a=null):(e=S.treeContext,de=Bn(_.nextSibling),Qe=a,qt=!0,Nr=null,Zn=!1,e!==null&&(On[Rn++]=va,On[Rn++]=_a,On[Rn++]=Dr,va=e.id,_a=e.overflow,Dr=a),a=cm(a,o.children),a.flags|=4096);return a}return c?(Ja(),c=o.fallback,_=a.mode,S=e.child,k=S.sibling,o=ga(S,{mode:"hidden",children:o.children}),o.subtreeFlags=S.subtreeFlags&65011712,k!==null?c=ga(k,c):(c=xr(c,_,s,null),c.flags|=2),c.return=a,o.return=a,o.sibling=c,a.child=o,o=c,c=a.child,_=e.child.memoizedState,_===null?_=om(s):(S=_.cachePool,S!==null?(k=Re._currentValue,S=S.parent!==k?{parent:k,pool:k}:S):S=yv(),_={baseLanes:_.baseLanes|s,cachePool:S}),c.memoizedState=_,c.childLanes=lm(e,y,s),a.memoizedState=im,o):(Za(a),s=e.child,e=s.sibling,s=ga(s,{mode:"visible",children:o.children}),s.return=a,s.sibling=null,e!==null&&(y=a.deletions,y===null?(a.deletions=[e],a.flags|=16):y.push(e)),a.child=s,a.memoizedState=null,s)}function cm(e,a){return a=su({mode:"visible",children:a},e.mode),a.return=e,e.child=a}function su(e,a){return e=dn(22,e,null,a),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function um(e,a,s){return Ys(a,e.child,null,s),e=cm(a,a.pendingProps.children),e.flags|=2,a.memoizedState=null,e}function D_(e,a,s){e.lanes|=a;var o=e.alternate;o!==null&&(o.lanes|=a),Ah(e.return,a,s)}function fm(e,a,s,o,c){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:a,rendering:null,renderingStartTime:0,last:o,tail:s,tailMode:c}:(d.isBackwards=a,d.rendering=null,d.renderingStartTime=0,d.last=o,d.tail=s,d.tailMode=c)}function k_(e,a,s){var o=a.pendingProps,c=o.revealOrder,d=o.tail;if(Ue(e,a,o.children,s),o=Me.current,(o&2)!==0)o=o&1|2,a.flags|=128;else{if(e!==null&&(e.flags&128)!==0)t:for(e=a.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&D_(e,s,a);else if(e.tag===19)D_(e,s,a);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===a)break t;for(;e.sibling===null;){if(e.return===null||e.return===a)break t;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}switch(J(Me,o),c){case"forwards":for(s=a.child,c=null;s!==null;)e=s.alternate,e!==null&&eu(e)===null&&(c=s),s=s.sibling;s=c,s===null?(c=a.child,a.child=null):(c=s.sibling,s.sibling=null),fm(a,!1,c,s,d);break;case"backwards":for(s=null,c=a.child,a.child=null;c!==null;){if(e=c.alternate,e!==null&&eu(e)===null){a.child=c;break}e=c.sibling,c.sibling=s,s=c,c=e}fm(a,!0,s,null,d);break;case"together":fm(a,!1,null,null,void 0);break;default:a.memoizedState=null}return a.child}function Oa(e,a,s){if(e!==null&&(a.dependencies=e.dependencies),ar|=a.lanes,(s&a.childLanes)===0)if(e!==null){if(Co(e,a,s,!1),(s&a.childLanes)===0)return null}else return null;if(e!==null&&a.child!==e.child)throw Error(i(153));if(a.child!==null){for(e=a.child,s=ga(e,e.pendingProps),a.child=s,s.return=a;e.sibling!==null;)e=e.sibling,s=s.sibling=ga(e,e.pendingProps),s.return=a;s.sibling=null}return a.child}function dm(e,a){return(e.lanes&a)!==0?!0:(e=e.dependencies,!!(e!==null&&jc(e)))}function nw(e,a,s){switch(a.tag){case 3:dt(a,a.stateNode.containerInfo),Ga(a,Re,e.memoizedState.cache),wo();break;case 27:case 5:Zt(a);break;case 4:dt(a,a.stateNode.containerInfo);break;case 10:Ga(a,a.type,a.memoizedProps.value);break;case 13:var o=a.memoizedState;if(o!==null)return o.dehydrated!==null?(Za(a),a.flags|=128,null):(s&a.child.childLanes)!==0?x_(e,a,s):(Za(a),e=Oa(e,a,s),e!==null?e.sibling:null);Za(a);break;case 19:var c=(e.flags&128)!==0;if(o=(s&a.childLanes)!==0,o||(Co(e,a,s,!1),o=(s&a.childLanes)!==0),c){if(o)return k_(e,a,s);a.flags|=128}if(c=a.memoizedState,c!==null&&(c.rendering=null,c.tail=null,c.lastEffect=null),J(Me,Me.current),o)break;return null;case 22:case 23:return a.lanes=0,R_(e,a,s);case 24:Ga(a,Re,e.memoizedState.cache)}return Oa(e,a,s)}function N_(e,a,s){if(e!==null)if(e.memoizedProps!==a.pendingProps)Ne=!0;else{if(!dm(e,s)&&(a.flags&128)===0)return Ne=!1,nw(e,a,s);Ne=(e.flags&131072)!==0}else Ne=!1,qt&&(a.flags&1048576)!==0&&cv(a,Fc,a.index);switch(a.lanes=0,a.tag){case 16:t:{e=a.pendingProps;var o=a.elementType,c=o._init;if(o=c(o._payload),a.type=o,typeof o=="function")Sh(o)?(e=jr(o,e),a.tag=1,a=A_(null,a,o,e,s)):(a.tag=0,a=sm(null,a,o,e,s));else{if(o!=null){if(c=o.$$typeof,c===G){a.tag=11,a=E_(null,a,o,e,s);break t}else if(c===Q){a.tag=14,a=T_(null,a,o,e,s);break t}}throw a=St(o)||o,Error(i(306,a,""))}}return a;case 0:return sm(e,a,a.type,a.pendingProps,s);case 1:return o=a.type,c=jr(o,a.pendingProps),A_(e,a,o,c,s);case 3:t:{if(dt(a,a.stateNode.containerInfo),e===null)throw Error(i(387));o=a.pendingProps;var d=a.memoizedState;c=d.element,Lh(e,a),$o(a,o,null,s);var y=a.memoizedState;if(o=y.cache,Ga(a,Re,o),o!==d.cache&&Ch(a,[Re],s,!0),Lo(),o=y.element,d.isDehydrated)if(d={element:o,isDehydrated:!1,cache:y.cache},a.updateQueue.baseState=d,a.memoizedState=d,a.flags&256){a=C_(e,a,o,s);break t}else if(o!==c){c=En(Error(i(424)),a),Ao(c),a=C_(e,a,o,s);break t}else{switch(e=a.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(de=Bn(e.firstChild),Qe=a,qt=!0,Nr=null,Zn=!0,s=f_(a,null,o,s),a.child=s;s;)s.flags=s.flags&-3|4096,s=s.sibling}else{if(wo(),o===c){a=Oa(e,a,s);break t}Ue(e,a,o,s)}a=a.child}return a;case 26:return ru(e,a),e===null?(s=Ub(a.type,null,a.pendingProps,null))?a.memoizedState=s:qt||(s=a.type,e=a.pendingProps,o=_u(lt.current).createElement(s),o[He]=a,o[tn]=e,je(o,s,e),ke(o),a.stateNode=o):a.memoizedState=Ub(a.type,e.memoizedProps,a.pendingProps,e.memoizedState),null;case 27:return Zt(a),e===null&&qt&&(o=a.stateNode=zb(a.type,a.pendingProps,lt.current),Qe=a,Zn=!0,c=de,or(a.type)?(Gm=c,de=Bn(o.firstChild)):de=c),Ue(e,a,a.pendingProps.children,s),ru(e,a),e===null&&(a.flags|=4194304),a.child;case 5:return e===null&&qt&&((c=o=de)&&(o=xw(o,a.type,a.pendingProps,Zn),o!==null?(a.stateNode=o,Qe=a,de=Bn(o.firstChild),Zn=!1,c=!0):c=!1),c||zr(a)),Zt(a),c=a.type,d=a.pendingProps,y=e!==null?e.memoizedProps:null,o=d.children,Hm(c,d)?o=null:y!==null&&Hm(c,y)&&(a.flags|=32),a.memoizedState!==null&&(c=qh(e,a,QM,null,null,s),rl._currentValue=c),ru(e,a),Ue(e,a,o,s),a.child;case 6:return e===null&&qt&&((e=s=de)&&(s=Dw(s,a.pendingProps,Zn),s!==null?(a.stateNode=s,Qe=a,de=null,e=!0):e=!1),e||zr(a)),null;case 13:return x_(e,a,s);case 4:return dt(a,a.stateNode.containerInfo),o=a.pendingProps,e===null?a.child=Ys(a,null,o,s):Ue(e,a,o,s),a.child;case 11:return E_(e,a,a.type,a.pendingProps,s);case 7:return Ue(e,a,a.pendingProps,s),a.child;case 8:return Ue(e,a,a.pendingProps.children,s),a.child;case 12:return Ue(e,a,a.pendingProps.children,s),a.child;case 10:return o=a.pendingProps,Ga(a,a.type,o.value),Ue(e,a,o.children,s),a.child;case 9:return c=a.type._context,o=a.pendingProps.children,$r(a),c=Ie(c),o=o(c),a.flags|=1,Ue(e,a,o,s),a.child;case 14:return T_(e,a,a.type,a.pendingProps,s);case 15:return O_(e,a,a.type,a.pendingProps,s);case 19:return k_(e,a,s);case 31:return o=a.pendingProps,s=a.mode,o={mode:o.mode,children:o.children},e===null?(s=su(o,s),s.ref=a.ref,a.child=s,s.return=a,a=s):(s=ga(e.child,o),s.ref=a.ref,a.child=s,s.return=a,a=s),a;case 22:return R_(e,a,s);case 24:return $r(a),o=Ie(Re),e===null?(c=kh(),c===null&&(c=ee,d=xh(),c.pooledCache=d,d.refCount++,d!==null&&(c.pooledCacheLanes|=s),c=d),a.memoizedState={parent:o,cache:c},zh(a),Ga(a,Re,c)):((e.lanes&s)!==0&&(Lh(e,a),$o(a,null,null,s),Lo()),c=e.memoizedState,d=a.memoizedState,c.parent!==o?(c={parent:o,cache:o},a.memoizedState=c,a.lanes===0&&(a.memoizedState=a.updateQueue.baseState=c),Ga(a,Re,o)):(o=d.cache,Ga(a,Re,o),o!==c.cache&&Ch(a,[Re],s,!0))),Ue(e,a,a.pendingProps.children,s),a.child;case 29:throw a.pendingProps}throw Error(i(156,a.tag))}function Ra(e){e.flags|=4}function z_(e,a){if(a.type!=="stylesheet"||(a.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Hb(a)){if(a=Mn.current,a!==null&&((Ut&4194048)===Ut?Jn!==null:(Ut&62914560)!==Ut&&(Ut&536870912)===0||a!==Jn))throw No=Nh,gv;e.flags|=8192}}function iu(e,a){a!==null&&(e.flags|=4),e.flags&16384&&(a=e.tag!==22?d0():536870912,e.lanes|=a,Ws|=a)}function Io(e,a){if(!qt)switch(e.tailMode){case"hidden":a=e.tail;for(var s=null;a!==null;)a.alternate!==null&&(s=a),a=a.sibling;s===null?e.tail=null:s.sibling=null;break;case"collapsed":s=e.tail;for(var o=null;s!==null;)s.alternate!==null&&(o=s),s=s.sibling;o===null?a||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function oe(e){var a=e.alternate!==null&&e.alternate.child===e.child,s=0,o=0;if(a)for(var c=e.child;c!==null;)s|=c.lanes|c.childLanes,o|=c.subtreeFlags&65011712,o|=c.flags&65011712,c.return=e,c=c.sibling;else for(c=e.child;c!==null;)s|=c.lanes|c.childLanes,o|=c.subtreeFlags,o|=c.flags,c.return=e,c=c.sibling;return e.subtreeFlags|=o,e.childLanes=s,a}function aw(e,a,s){var o=a.pendingProps;switch(Rh(a),a.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return oe(a),null;case 1:return oe(a),null;case 3:return s=a.stateNode,o=null,e!==null&&(o=e.memoizedState.cache),a.memoizedState.cache!==o&&(a.flags|=2048),Sa(Re),kt(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(Mo(a)?Ra(a):e===null||e.memoizedState.isDehydrated&&(a.flags&256)===0||(a.flags|=1024,dv())),oe(a),null;case 26:return s=a.memoizedState,e===null?(Ra(a),s!==null?(oe(a),z_(a,s)):(oe(a),a.flags&=-16777217)):s?s!==e.memoizedState?(Ra(a),oe(a),z_(a,s)):(oe(a),a.flags&=-16777217):(e.memoizedProps!==o&&Ra(a),oe(a),a.flags&=-16777217),null;case 27:fe(a),s=lt.current;var c=a.type;if(e!==null&&a.stateNode!=null)e.memoizedProps!==o&&Ra(a);else{if(!o){if(a.stateNode===null)throw Error(i(166));return oe(a),null}e=et.current,Mo(a)?uv(a):(e=zb(c,o,s),a.stateNode=e,Ra(a))}return oe(a),null;case 5:if(fe(a),s=a.type,e!==null&&a.stateNode!=null)e.memoizedProps!==o&&Ra(a);else{if(!o){if(a.stateNode===null)throw Error(i(166));return oe(a),null}if(e=et.current,Mo(a))uv(a);else{switch(c=_u(lt.current),e){case 1:e=c.createElementNS("http://www.w3.org/2000/svg",s);break;case 2:e=c.createElementNS("http://www.w3.org/1998/Math/MathML",s);break;default:switch(s){case"svg":e=c.createElementNS("http://www.w3.org/2000/svg",s);break;case"math":e=c.createElementNS("http://www.w3.org/1998/Math/MathML",s);break;case"script":e=c.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof o.is=="string"?c.createElement("select",{is:o.is}):c.createElement("select"),o.multiple?e.multiple=!0:o.size&&(e.size=o.size);break;default:e=typeof o.is=="string"?c.createElement(s,{is:o.is}):c.createElement(s)}}e[He]=a,e[tn]=o;t:for(c=a.child;c!==null;){if(c.tag===5||c.tag===6)e.appendChild(c.stateNode);else if(c.tag!==4&&c.tag!==27&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===a)break t;for(;c.sibling===null;){if(c.return===null||c.return===a)break t;c=c.return}c.sibling.return=c.return,c=c.sibling}a.stateNode=e;t:switch(je(e,s,o),s){case"button":case"input":case"select":case"textarea":e=!!o.autoFocus;break t;case"img":e=!0;break t;default:e=!1}e&&Ra(a)}}return oe(a),a.flags&=-16777217,null;case 6:if(e&&a.stateNode!=null)e.memoizedProps!==o&&Ra(a);else{if(typeof o!="string"&&a.stateNode===null)throw Error(i(166));if(e=lt.current,Mo(a)){if(e=a.stateNode,s=a.memoizedProps,o=null,c=Qe,c!==null)switch(c.tag){case 27:case 5:o=c.memoizedProps}e[He]=a,e=!!(e.nodeValue===s||o!==null&&o.suppressHydrationWarning===!0||wb(e.nodeValue,s)),e||zr(a)}else e=_u(e).createTextNode(o),e[He]=a,a.stateNode=e}return oe(a),null;case 13:if(o=a.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(c=Mo(a),o!==null&&o.dehydrated!==null){if(e===null){if(!c)throw Error(i(318));if(c=a.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(i(317));c[He]=a}else wo(),(a.flags&128)===0&&(a.memoizedState=null),a.flags|=4;oe(a),c=!1}else c=dv(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=c),c=!0;if(!c)return a.flags&256?(Ta(a),a):(Ta(a),null)}if(Ta(a),(a.flags&128)!==0)return a.lanes=s,a;if(s=o!==null,e=e!==null&&e.memoizedState!==null,s){o=a.child,c=null,o.alternate!==null&&o.alternate.memoizedState!==null&&o.alternate.memoizedState.cachePool!==null&&(c=o.alternate.memoizedState.cachePool.pool);var d=null;o.memoizedState!==null&&o.memoizedState.cachePool!==null&&(d=o.memoizedState.cachePool.pool),d!==c&&(o.flags|=2048)}return s!==e&&s&&(a.child.flags|=8192),iu(a,a.updateQueue),oe(a),null;case 4:return kt(),e===null&&Um(a.stateNode.containerInfo),oe(a),null;case 10:return Sa(a.type),oe(a),null;case 19:if(nt(Me),c=a.memoizedState,c===null)return oe(a),null;if(o=(a.flags&128)!==0,d=c.rendering,d===null)if(o)Io(c,!1);else{if(he!==0||e!==null&&(e.flags&128)!==0)for(e=a.child;e!==null;){if(d=eu(e),d!==null){for(a.flags|=128,Io(c,!1),e=d.updateQueue,a.updateQueue=e,iu(a,e),a.subtreeFlags=0,e=s,s=a.child;s!==null;)lv(s,e),s=s.sibling;return J(Me,Me.current&1|2),a.child}e=e.sibling}c.tail!==null&&De()>cu&&(a.flags|=128,o=!0,Io(c,!1),a.lanes=4194304)}else{if(!o)if(e=eu(d),e!==null){if(a.flags|=128,o=!0,e=e.updateQueue,a.updateQueue=e,iu(a,e),Io(c,!0),c.tail===null&&c.tailMode==="hidden"&&!d.alternate&&!qt)return oe(a),null}else 2*De()-c.renderingStartTime>cu&&s!==536870912&&(a.flags|=128,o=!0,Io(c,!1),a.lanes=4194304);c.isBackwards?(d.sibling=a.child,a.child=d):(e=c.last,e!==null?e.sibling=d:a.child=d,c.last=d)}return c.tail!==null?(a=c.tail,c.rendering=a,c.tail=a.sibling,c.renderingStartTime=De(),a.sibling=null,e=Me.current,J(Me,o?e&1|2:e&1),a):(oe(a),null);case 22:case 23:return Ta(a),jh(),o=a.memoizedState!==null,e!==null?e.memoizedState!==null!==o&&(a.flags|=8192):o&&(a.flags|=8192),o?(s&536870912)!==0&&(a.flags&128)===0&&(oe(a),a.subtreeFlags&6&&(a.flags|=8192)):oe(a),s=a.updateQueue,s!==null&&iu(a,s.retryQueue),s=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(s=e.memoizedState.cachePool.pool),o=null,a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(o=a.memoizedState.cachePool.pool),o!==s&&(a.flags|=2048),e!==null&&nt(Ur),null;case 24:return s=null,e!==null&&(s=e.memoizedState.cache),a.memoizedState.cache!==s&&(a.flags|=2048),Sa(Re),oe(a),null;case 25:return null;case 30:return null}throw Error(i(156,a.tag))}function rw(e,a){switch(Rh(a),a.tag){case 1:return e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 3:return Sa(Re),kt(),e=a.flags,(e&65536)!==0&&(e&128)===0?(a.flags=e&-65537|128,a):null;case 26:case 27:case 5:return fe(a),null;case 13:if(Ta(a),e=a.memoizedState,e!==null&&e.dehydrated!==null){if(a.alternate===null)throw Error(i(340));wo()}return e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 19:return nt(Me),null;case 4:return kt(),null;case 10:return Sa(a.type),null;case 22:case 23:return Ta(a),jh(),e!==null&&nt(Ur),e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 24:return Sa(Re),null;case 25:return null;default:return null}}function L_(e,a){switch(Rh(a),a.tag){case 3:Sa(Re),kt();break;case 26:case 27:case 5:fe(a);break;case 4:kt();break;case 13:Ta(a);break;case 19:nt(Me);break;case 10:Sa(a.type);break;case 22:case 23:Ta(a),jh(),e!==null&&nt(Ur);break;case 24:Sa(Re)}}function Po(e,a){try{var s=a.updateQueue,o=s!==null?s.lastEffect:null;if(o!==null){var c=o.next;s=c;do{if((s.tag&e)===e){o=void 0;var d=s.create,y=s.inst;o=d(),y.destroy=o}s=s.next}while(s!==c)}}catch(_){Jt(a,a.return,_)}}function Wa(e,a,s){try{var o=a.updateQueue,c=o!==null?o.lastEffect:null;if(c!==null){var d=c.next;o=d;do{if((o.tag&e)===e){var y=o.inst,_=y.destroy;if(_!==void 0){y.destroy=void 0,c=a;var S=s,k=_;try{k()}catch(H){Jt(c,S,H)}}}o=o.next}while(o!==d)}}catch(H){Jt(a,a.return,H)}}function $_(e){var a=e.updateQueue;if(a!==null){var s=e.stateNode;try{Tv(a,s)}catch(o){Jt(e,e.return,o)}}}function U_(e,a,s){s.props=jr(e.type,e.memoizedProps),s.state=e.memoizedState;try{s.componentWillUnmount()}catch(o){Jt(e,a,o)}}function Vo(e,a){try{var s=e.ref;if(s!==null){switch(e.tag){case 26:case 27:case 5:var o=e.stateNode;break;case 30:o=e.stateNode;break;default:o=e.stateNode}typeof s=="function"?e.refCleanup=s(o):s.current=o}}catch(c){Jt(e,a,c)}}function Wn(e,a){var s=e.ref,o=e.refCleanup;if(s!==null)if(typeof o=="function")try{o()}catch(c){Jt(e,a,c)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof s=="function")try{s(null)}catch(c){Jt(e,a,c)}else s.current=null}function F_(e){var a=e.type,s=e.memoizedProps,o=e.stateNode;try{t:switch(a){case"button":case"input":case"select":case"textarea":s.autoFocus&&o.focus();break t;case"img":s.src?o.src=s.src:s.srcSet&&(o.srcset=s.srcSet)}}catch(c){Jt(e,e.return,c)}}function hm(e,a,s){try{var o=e.stateNode;Rw(o,e.type,s,a),o[tn]=a}catch(c){Jt(e,e.return,c)}}function j_(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&or(e.type)||e.tag===4}function mm(e){t:for(;;){for(;e.sibling===null;){if(e.return===null||j_(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&or(e.type)||e.flags&2||e.child===null||e.tag===4)continue t;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function pm(e,a,s){var o=e.tag;if(o===5||o===6)e=e.stateNode,a?(s.nodeType===9?s.body:s.nodeName==="HTML"?s.ownerDocument.body:s).insertBefore(e,a):(a=s.nodeType===9?s.body:s.nodeName==="HTML"?s.ownerDocument.body:s,a.appendChild(e),s=s._reactRootContainer,s!=null||a.onclick!==null||(a.onclick=vu));else if(o!==4&&(o===27&&or(e.type)&&(s=e.stateNode,a=null),e=e.child,e!==null))for(pm(e,a,s),e=e.sibling;e!==null;)pm(e,a,s),e=e.sibling}function ou(e,a,s){var o=e.tag;if(o===5||o===6)e=e.stateNode,a?s.insertBefore(e,a):s.appendChild(e);else if(o!==4&&(o===27&&or(e.type)&&(s=e.stateNode),e=e.child,e!==null))for(ou(e,a,s),e=e.sibling;e!==null;)ou(e,a,s),e=e.sibling}function B_(e){var a=e.stateNode,s=e.memoizedProps;try{for(var o=e.type,c=a.attributes;c.length;)a.removeAttributeNode(c[0]);je(a,o,s),a[He]=e,a[tn]=s}catch(d){Jt(e,e.return,d)}}var Ma=!1,ge=!1,ym=!1,q_=typeof WeakSet=="function"?WeakSet:Set,ze=null;function sw(e,a){if(e=e.containerInfo,Bm=Ru,e=J0(e),mh(e)){if("selectionStart"in e)var s={start:e.selectionStart,end:e.selectionEnd};else t:{s=(s=e.ownerDocument)&&s.defaultView||window;var o=s.getSelection&&s.getSelection();if(o&&o.rangeCount!==0){s=o.anchorNode;var c=o.anchorOffset,d=o.focusNode;o=o.focusOffset;try{s.nodeType,d.nodeType}catch{s=null;break t}var y=0,_=-1,S=-1,k=0,H=0,V=e,z=null;e:for(;;){for(var $;V!==s||c!==0&&V.nodeType!==3||(_=y+c),V!==d||o!==0&&V.nodeType!==3||(S=y+o),V.nodeType===3&&(y+=V.nodeValue.length),($=V.firstChild)!==null;)z=V,V=$;for(;;){if(V===e)break e;if(z===s&&++k===c&&(_=y),z===d&&++H===o&&(S=y),($=V.nextSibling)!==null)break;V=z,z=V.parentNode}V=$}s=_===-1||S===-1?null:{start:_,end:S}}else s=null}s=s||{start:0,end:0}}else s=null;for(qm={focusedElem:e,selectionRange:s},Ru=!1,ze=a;ze!==null;)if(a=ze,e=a.child,(a.subtreeFlags&1024)!==0&&e!==null)e.return=a,ze=e;else for(;ze!==null;){switch(a=ze,d=a.alternate,e=a.flags,a.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&d!==null){e=void 0,s=a,c=d.memoizedProps,d=d.memoizedState,o=s.stateNode;try{var _t=jr(s.type,c,s.elementType===s.type);e=o.getSnapshotBeforeUpdate(_t,d),o.__reactInternalSnapshotBeforeUpdate=e}catch(gt){Jt(s,s.return,gt)}}break;case 3:if((e&1024)!==0){if(e=a.stateNode.containerInfo,s=e.nodeType,s===9)Pm(e);else if(s===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Pm(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(i(163))}if(e=a.sibling,e!==null){e.return=a.return,ze=e;break}ze=a.return}}function H_(e,a,s){var o=s.flags;switch(s.tag){case 0:case 11:case 15:tr(e,s),o&4&&Po(5,s);break;case 1:if(tr(e,s),o&4)if(e=s.stateNode,a===null)try{e.componentDidMount()}catch(y){Jt(s,s.return,y)}else{var c=jr(s.type,a.memoizedProps);a=a.memoizedState;try{e.componentDidUpdate(c,a,e.__reactInternalSnapshotBeforeUpdate)}catch(y){Jt(s,s.return,y)}}o&64&&$_(s),o&512&&Vo(s,s.return);break;case 3:if(tr(e,s),o&64&&(e=s.updateQueue,e!==null)){if(a=null,s.child!==null)switch(s.child.tag){case 27:case 5:a=s.child.stateNode;break;case 1:a=s.child.stateNode}try{Tv(e,a)}catch(y){Jt(s,s.return,y)}}break;case 27:a===null&&o&4&&B_(s);case 26:case 5:tr(e,s),a===null&&o&4&&F_(s),o&512&&Vo(s,s.return);break;case 12:tr(e,s);break;case 13:tr(e,s),o&4&&V_(e,s),o&64&&(e=s.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(s=mw.bind(null,s),kw(e,s))));break;case 22:if(o=s.memoizedState!==null||Ma,!o){a=a!==null&&a.memoizedState!==null||ge,c=Ma;var d=ge;Ma=o,(ge=a)&&!d?er(e,s,(s.subtreeFlags&8772)!==0):tr(e,s),Ma=c,ge=d}break;case 30:break;default:tr(e,s)}}function I_(e){var a=e.alternate;a!==null&&(e.alternate=null,I_(a)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(a=e.stateNode,a!==null&&Yd(a)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var se=null,an=!1;function wa(e,a,s){for(s=s.child;s!==null;)P_(e,a,s),s=s.sibling}function P_(e,a,s){if(cn&&typeof cn.onCommitFiberUnmount=="function")try{cn.onCommitFiberUnmount(Or,s)}catch{}switch(s.tag){case 26:ge||Wn(s,a),wa(e,a,s),s.memoizedState?s.memoizedState.count--:s.stateNode&&(s=s.stateNode,s.parentNode.removeChild(s));break;case 27:ge||Wn(s,a);var o=se,c=an;or(s.type)&&(se=s.stateNode,an=!1),wa(e,a,s),tl(s.stateNode),se=o,an=c;break;case 5:ge||Wn(s,a);case 6:if(o=se,c=an,se=null,wa(e,a,s),se=o,an=c,se!==null)if(an)try{(se.nodeType===9?se.body:se.nodeName==="HTML"?se.ownerDocument.body:se).removeChild(s.stateNode)}catch(d){Jt(s,a,d)}else try{se.removeChild(s.stateNode)}catch(d){Jt(s,a,d)}break;case 18:se!==null&&(an?(e=se,kb(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,s.stateNode),ll(e)):kb(se,s.stateNode));break;case 4:o=se,c=an,se=s.stateNode.containerInfo,an=!0,wa(e,a,s),se=o,an=c;break;case 0:case 11:case 14:case 15:ge||Wa(2,s,a),ge||Wa(4,s,a),wa(e,a,s);break;case 1:ge||(Wn(s,a),o=s.stateNode,typeof o.componentWillUnmount=="function"&&U_(s,a,o)),wa(e,a,s);break;case 21:wa(e,a,s);break;case 22:ge=(o=ge)||s.memoizedState!==null,wa(e,a,s),ge=o;break;default:wa(e,a,s)}}function V_(e,a){if(a.memoizedState===null&&(e=a.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{ll(e)}catch(s){Jt(a,a.return,s)}}function iw(e){switch(e.tag){case 13:case 19:var a=e.stateNode;return a===null&&(a=e.stateNode=new q_),a;case 22:return e=e.stateNode,a=e._retryCache,a===null&&(a=e._retryCache=new q_),a;default:throw Error(i(435,e.tag))}}function gm(e,a){var s=iw(e);a.forEach(function(o){var c=pw.bind(null,e,o);s.has(o)||(s.add(o),o.then(c,c))})}function hn(e,a){var s=a.deletions;if(s!==null)for(var o=0;o<s.length;o++){var c=s[o],d=e,y=a,_=y;t:for(;_!==null;){switch(_.tag){case 27:if(or(_.type)){se=_.stateNode,an=!1;break t}break;case 5:se=_.stateNode,an=!1;break t;case 3:case 4:se=_.stateNode.containerInfo,an=!0;break t}_=_.return}if(se===null)throw Error(i(160));P_(d,y,c),se=null,an=!1,d=c.alternate,d!==null&&(d.return=null),c.return=null}if(a.subtreeFlags&13878)for(a=a.child;a!==null;)G_(a,e),a=a.sibling}var jn=null;function G_(e,a){var s=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:hn(a,e),mn(e),o&4&&(Wa(3,e,e.return),Po(3,e),Wa(5,e,e.return));break;case 1:hn(a,e),mn(e),o&512&&(ge||s===null||Wn(s,s.return)),o&64&&Ma&&(e=e.updateQueue,e!==null&&(o=e.callbacks,o!==null&&(s=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=s===null?o:s.concat(o))));break;case 26:var c=jn;if(hn(a,e),mn(e),o&512&&(ge||s===null||Wn(s,s.return)),o&4){var d=s!==null?s.memoizedState:null;if(o=e.memoizedState,s===null)if(o===null)if(e.stateNode===null){t:{o=e.type,s=e.memoizedProps,c=c.ownerDocument||c;e:switch(o){case"title":d=c.getElementsByTagName("title")[0],(!d||d[po]||d[He]||d.namespaceURI==="http://www.w3.org/2000/svg"||d.hasAttribute("itemprop"))&&(d=c.createElement(o),c.head.insertBefore(d,c.querySelector("head > title"))),je(d,o,s),d[He]=e,ke(d),o=d;break t;case"link":var y=Bb("link","href",c).get(o+(s.href||""));if(y){for(var _=0;_<y.length;_++)if(d=y[_],d.getAttribute("href")===(s.href==null||s.href===""?null:s.href)&&d.getAttribute("rel")===(s.rel==null?null:s.rel)&&d.getAttribute("title")===(s.title==null?null:s.title)&&d.getAttribute("crossorigin")===(s.crossOrigin==null?null:s.crossOrigin)){y.splice(_,1);break e}}d=c.createElement(o),je(d,o,s),c.head.appendChild(d);break;case"meta":if(y=Bb("meta","content",c).get(o+(s.content||""))){for(_=0;_<y.length;_++)if(d=y[_],d.getAttribute("content")===(s.content==null?null:""+s.content)&&d.getAttribute("name")===(s.name==null?null:s.name)&&d.getAttribute("property")===(s.property==null?null:s.property)&&d.getAttribute("http-equiv")===(s.httpEquiv==null?null:s.httpEquiv)&&d.getAttribute("charset")===(s.charSet==null?null:s.charSet)){y.splice(_,1);break e}}d=c.createElement(o),je(d,o,s),c.head.appendChild(d);break;default:throw Error(i(468,o))}d[He]=e,ke(d),o=d}e.stateNode=o}else qb(c,e.type,e.stateNode);else e.stateNode=jb(c,o,e.memoizedProps);else d!==o?(d===null?s.stateNode!==null&&(s=s.stateNode,s.parentNode.removeChild(s)):d.count--,o===null?qb(c,e.type,e.stateNode):jb(c,o,e.memoizedProps)):o===null&&e.stateNode!==null&&hm(e,e.memoizedProps,s.memoizedProps)}break;case 27:hn(a,e),mn(e),o&512&&(ge||s===null||Wn(s,s.return)),s!==null&&o&4&&hm(e,e.memoizedProps,s.memoizedProps);break;case 5:if(hn(a,e),mn(e),o&512&&(ge||s===null||Wn(s,s.return)),e.flags&32){c=e.stateNode;try{Ds(c,"")}catch($){Jt(e,e.return,$)}}o&4&&e.stateNode!=null&&(c=e.memoizedProps,hm(e,c,s!==null?s.memoizedProps:c)),o&1024&&(ym=!0);break;case 6:if(hn(a,e),mn(e),o&4){if(e.stateNode===null)throw Error(i(162));o=e.memoizedProps,s=e.stateNode;try{s.nodeValue=o}catch($){Jt(e,e.return,$)}}break;case 3:if(Eu=null,c=jn,jn=bu(a.containerInfo),hn(a,e),jn=c,mn(e),o&4&&s!==null&&s.memoizedState.isDehydrated)try{ll(a.containerInfo)}catch($){Jt(e,e.return,$)}ym&&(ym=!1,K_(e));break;case 4:o=jn,jn=bu(e.stateNode.containerInfo),hn(a,e),mn(e),jn=o;break;case 12:hn(a,e),mn(e);break;case 13:hn(a,e),mn(e),e.child.flags&8192&&e.memoizedState!==null!=(s!==null&&s.memoizedState!==null)&&(Tm=De()),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,gm(e,o)));break;case 22:c=e.memoizedState!==null;var S=s!==null&&s.memoizedState!==null,k=Ma,H=ge;if(Ma=k||c,ge=H||S,hn(a,e),ge=H,Ma=k,mn(e),o&8192)t:for(a=e.stateNode,a._visibility=c?a._visibility&-2:a._visibility|1,c&&(s===null||S||Ma||ge||Br(e)),s=null,a=e;;){if(a.tag===5||a.tag===26){if(s===null){S=s=a;try{if(d=S.stateNode,c)y=d.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{_=S.stateNode;var V=S.memoizedProps.style,z=V!=null&&V.hasOwnProperty("display")?V.display:null;_.style.display=z==null||typeof z=="boolean"?"":(""+z).trim()}}catch($){Jt(S,S.return,$)}}}else if(a.tag===6){if(s===null){S=a;try{S.stateNode.nodeValue=c?"":S.memoizedProps}catch($){Jt(S,S.return,$)}}}else if((a.tag!==22&&a.tag!==23||a.memoizedState===null||a===e)&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;s===a&&(s=null),a=a.return}s===a&&(s=null),a.sibling.return=a.return,a=a.sibling}o&4&&(o=e.updateQueue,o!==null&&(s=o.retryQueue,s!==null&&(o.retryQueue=null,gm(e,s))));break;case 19:hn(a,e),mn(e),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,gm(e,o)));break;case 30:break;case 21:break;default:hn(a,e),mn(e)}}function mn(e){var a=e.flags;if(a&2){try{for(var s,o=e.return;o!==null;){if(j_(o)){s=o;break}o=o.return}if(s==null)throw Error(i(160));switch(s.tag){case 27:var c=s.stateNode,d=mm(e);ou(e,d,c);break;case 5:var y=s.stateNode;s.flags&32&&(Ds(y,""),s.flags&=-33);var _=mm(e);ou(e,_,y);break;case 3:case 4:var S=s.stateNode.containerInfo,k=mm(e);pm(e,k,S);break;default:throw Error(i(161))}}catch(H){Jt(e,e.return,H)}e.flags&=-3}a&4096&&(e.flags&=-4097)}function K_(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var a=e;K_(a),a.tag===5&&a.flags&1024&&a.stateNode.reset(),e=e.sibling}}function tr(e,a){if(a.subtreeFlags&8772)for(a=a.child;a!==null;)H_(e,a.alternate,a),a=a.sibling}function Br(e){for(e=e.child;e!==null;){var a=e;switch(a.tag){case 0:case 11:case 14:case 15:Wa(4,a,a.return),Br(a);break;case 1:Wn(a,a.return);var s=a.stateNode;typeof s.componentWillUnmount=="function"&&U_(a,a.return,s),Br(a);break;case 27:tl(a.stateNode);case 26:case 5:Wn(a,a.return),Br(a);break;case 22:a.memoizedState===null&&Br(a);break;case 30:Br(a);break;default:Br(a)}e=e.sibling}}function er(e,a,s){for(s=s&&(a.subtreeFlags&8772)!==0,a=a.child;a!==null;){var o=a.alternate,c=e,d=a,y=d.flags;switch(d.tag){case 0:case 11:case 15:er(c,d,s),Po(4,d);break;case 1:if(er(c,d,s),o=d,c=o.stateNode,typeof c.componentDidMount=="function")try{c.componentDidMount()}catch(k){Jt(o,o.return,k)}if(o=d,c=o.updateQueue,c!==null){var _=o.stateNode;try{var S=c.shared.hiddenCallbacks;if(S!==null)for(c.shared.hiddenCallbacks=null,c=0;c<S.length;c++)Ev(S[c],_)}catch(k){Jt(o,o.return,k)}}s&&y&64&&$_(d),Vo(d,d.return);break;case 27:B_(d);case 26:case 5:er(c,d,s),s&&o===null&&y&4&&F_(d),Vo(d,d.return);break;case 12:er(c,d,s);break;case 13:er(c,d,s),s&&y&4&&V_(c,d);break;case 22:d.memoizedState===null&&er(c,d,s),Vo(d,d.return);break;case 30:break;default:er(c,d,s)}a=a.sibling}}function vm(e,a){var s=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(s=e.memoizedState.cachePool.pool),e=null,a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(e=a.memoizedState.cachePool.pool),e!==s&&(e!=null&&e.refCount++,s!=null&&xo(s))}function _m(e,a){e=null,a.alternate!==null&&(e=a.alternate.memoizedState.cache),a=a.memoizedState.cache,a!==e&&(a.refCount++,e!=null&&xo(e))}function ta(e,a,s,o){if(a.subtreeFlags&10256)for(a=a.child;a!==null;)Q_(e,a,s,o),a=a.sibling}function Q_(e,a,s,o){var c=a.flags;switch(a.tag){case 0:case 11:case 15:ta(e,a,s,o),c&2048&&Po(9,a);break;case 1:ta(e,a,s,o);break;case 3:ta(e,a,s,o),c&2048&&(e=null,a.alternate!==null&&(e=a.alternate.memoizedState.cache),a=a.memoizedState.cache,a!==e&&(a.refCount++,e!=null&&xo(e)));break;case 12:if(c&2048){ta(e,a,s,o),e=a.stateNode;try{var d=a.memoizedProps,y=d.id,_=d.onPostCommit;typeof _=="function"&&_(y,a.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){Jt(a,a.return,S)}}else ta(e,a,s,o);break;case 13:ta(e,a,s,o);break;case 23:break;case 22:d=a.stateNode,y=a.alternate,a.memoizedState!==null?d._visibility&2?ta(e,a,s,o):Go(e,a):d._visibility&2?ta(e,a,s,o):(d._visibility|=2,Xs(e,a,s,o,(a.subtreeFlags&10256)!==0)),c&2048&&vm(y,a);break;case 24:ta(e,a,s,o),c&2048&&_m(a.alternate,a);break;default:ta(e,a,s,o)}}function Xs(e,a,s,o,c){for(c=c&&(a.subtreeFlags&10256)!==0,a=a.child;a!==null;){var d=e,y=a,_=s,S=o,k=y.flags;switch(y.tag){case 0:case 11:case 15:Xs(d,y,_,S,c),Po(8,y);break;case 23:break;case 22:var H=y.stateNode;y.memoizedState!==null?H._visibility&2?Xs(d,y,_,S,c):Go(d,y):(H._visibility|=2,Xs(d,y,_,S,c)),c&&k&2048&&vm(y.alternate,y);break;case 24:Xs(d,y,_,S,c),c&&k&2048&&_m(y.alternate,y);break;default:Xs(d,y,_,S,c)}a=a.sibling}}function Go(e,a){if(a.subtreeFlags&10256)for(a=a.child;a!==null;){var s=e,o=a,c=o.flags;switch(o.tag){case 22:Go(s,o),c&2048&&vm(o.alternate,o);break;case 24:Go(s,o),c&2048&&_m(o.alternate,o);break;default:Go(s,o)}a=a.sibling}}var Ko=8192;function Zs(e){if(e.subtreeFlags&Ko)for(e=e.child;e!==null;)Y_(e),e=e.sibling}function Y_(e){switch(e.tag){case 26:Zs(e),e.flags&Ko&&e.memoizedState!==null&&Vw(jn,e.memoizedState,e.memoizedProps);break;case 5:Zs(e);break;case 3:case 4:var a=jn;jn=bu(e.stateNode.containerInfo),Zs(e),jn=a;break;case 22:e.memoizedState===null&&(a=e.alternate,a!==null&&a.memoizedState!==null?(a=Ko,Ko=16777216,Zs(e),Ko=a):Zs(e));break;default:Zs(e)}}function X_(e){var a=e.alternate;if(a!==null&&(e=a.child,e!==null)){a.child=null;do a=e.sibling,e.sibling=null,e=a;while(e!==null)}}function Qo(e){var a=e.deletions;if((e.flags&16)!==0){if(a!==null)for(var s=0;s<a.length;s++){var o=a[s];ze=o,J_(o,e)}X_(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Z_(e),e=e.sibling}function Z_(e){switch(e.tag){case 0:case 11:case 15:Qo(e),e.flags&2048&&Wa(9,e,e.return);break;case 3:Qo(e);break;case 12:Qo(e);break;case 22:var a=e.stateNode;e.memoizedState!==null&&a._visibility&2&&(e.return===null||e.return.tag!==13)?(a._visibility&=-3,lu(e)):Qo(e);break;default:Qo(e)}}function lu(e){var a=e.deletions;if((e.flags&16)!==0){if(a!==null)for(var s=0;s<a.length;s++){var o=a[s];ze=o,J_(o,e)}X_(e)}for(e=e.child;e!==null;){switch(a=e,a.tag){case 0:case 11:case 15:Wa(8,a,a.return),lu(a);break;case 22:s=a.stateNode,s._visibility&2&&(s._visibility&=-3,lu(a));break;default:lu(a)}e=e.sibling}}function J_(e,a){for(;ze!==null;){var s=ze;switch(s.tag){case 0:case 11:case 15:Wa(8,s,a);break;case 23:case 22:if(s.memoizedState!==null&&s.memoizedState.cachePool!==null){var o=s.memoizedState.cachePool.pool;o!=null&&o.refCount++}break;case 24:xo(s.memoizedState.cache)}if(o=s.child,o!==null)o.return=s,ze=o;else t:for(s=e;ze!==null;){o=ze;var c=o.sibling,d=o.return;if(I_(o),o===s){ze=null;break t}if(c!==null){c.return=d,ze=c;break t}ze=d}}}var ow={getCacheForType:function(e){var a=Ie(Re),s=a.data.get(e);return s===void 0&&(s=e(),a.data.set(e,s)),s}},lw=typeof WeakMap=="function"?WeakMap:Map,It=0,ee=null,Nt=null,Ut=0,Pt=0,pn=null,nr=!1,Js=!1,bm=!1,Aa=0,he=0,ar=0,qr=0,Sm=0,wn=0,Ws=0,Yo=null,rn=null,Em=!1,Tm=0,cu=1/0,uu=null,rr=null,Fe=0,sr=null,ti=null,ei=0,Om=0,Rm=null,W_=null,Xo=0,Mm=null;function yn(){if((It&2)!==0&&Ut!==0)return Ut&-Ut;if(C.T!==null){var e=Hs;return e!==0?e:Nm()}return p0()}function tb(){wn===0&&(wn=(Ut&536870912)===0||qt?f0():536870912);var e=Mn.current;return e!==null&&(e.flags|=32),wn}function gn(e,a,s){(e===ee&&(Pt===2||Pt===9)||e.cancelPendingCommit!==null)&&(ni(e,0),ir(e,Ut,wn,!1)),mo(e,s),((It&2)===0||e!==ee)&&(e===ee&&((It&2)===0&&(qr|=s),he===4&&ir(e,Ut,wn,!1)),ea(e))}function eb(e,a,s){if((It&6)!==0)throw Error(i(327));var o=!s&&(a&124)===0&&(a&e.expiredLanes)===0||ho(e,a),c=o?fw(e,a):Cm(e,a,!0),d=o;do{if(c===0){Js&&!o&&ir(e,a,0,!1);break}else{if(s=e.current.alternate,d&&!cw(s)){c=Cm(e,a,!1),d=!1;continue}if(c===2){if(d=a,e.errorRecoveryDisabledLanes&d)var y=0;else y=e.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){a=y;t:{var _=e;c=Yo;var S=_.current.memoizedState.isDehydrated;if(S&&(ni(_,y).flags|=256),y=Cm(_,y,!1),y!==2){if(bm&&!S){_.errorRecoveryDisabledLanes|=d,qr|=d,c=4;break t}d=rn,rn=c,d!==null&&(rn===null?rn=d:rn.push.apply(rn,d))}c=y}if(d=!1,c!==2)continue}}if(c===1){ni(e,0),ir(e,a,0,!0);break}t:{switch(o=e,d=c,d){case 0:case 1:throw Error(i(345));case 4:if((a&4194048)!==a)break;case 6:ir(o,a,wn,!nr);break t;case 2:rn=null;break;case 3:case 5:break;default:throw Error(i(329))}if((a&62914560)===a&&(c=Tm+300-De(),10<c)){if(ir(o,a,wn,!nr),Sc(o,0,!0)!==0)break t;o.timeoutHandle=xb(nb.bind(null,o,s,rn,uu,Em,a,wn,qr,Ws,nr,d,2,-0,0),c);break t}nb(o,s,rn,uu,Em,a,wn,qr,Ws,nr,d,0,-0,0)}}break}while(!0);ea(e)}function nb(e,a,s,o,c,d,y,_,S,k,H,V,z,$){if(e.timeoutHandle=-1,V=a.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(al={stylesheets:null,count:0,unsuspend:Pw},Y_(a),V=Gw(),V!==null)){e.cancelPendingCommit=V(cb.bind(null,e,a,d,s,o,c,y,_,S,H,1,z,$)),ir(e,d,y,!k);return}cb(e,a,d,s,o,c,y,_,S)}function cw(e){for(var a=e;;){var s=a.tag;if((s===0||s===11||s===15)&&a.flags&16384&&(s=a.updateQueue,s!==null&&(s=s.stores,s!==null)))for(var o=0;o<s.length;o++){var c=s[o],d=c.getSnapshot;c=c.value;try{if(!fn(d(),c))return!1}catch{return!1}}if(s=a.child,a.subtreeFlags&16384&&s!==null)s.return=a,a=s;else{if(a===e)break;for(;a.sibling===null;){if(a.return===null||a.return===e)return!0;a=a.return}a.sibling.return=a.return,a=a.sibling}}return!0}function ir(e,a,s,o){a&=~Sm,a&=~qr,e.suspendedLanes|=a,e.pingedLanes&=~a,o&&(e.warmLanes|=a),o=e.expirationTimes;for(var c=a;0<c;){var d=31-un(c),y=1<<d;o[d]=-1,c&=~y}s!==0&&h0(e,s,a)}function fu(){return(It&6)===0?(Zo(0),!1):!0}function wm(){if(Nt!==null){if(Pt===0)var e=Nt.return;else e=Nt,ba=Lr=null,Ph(e),Qs=null,qo=0,e=Nt;for(;e!==null;)L_(e.alternate,e),e=e.return;Nt=null}}function ni(e,a){var s=e.timeoutHandle;s!==-1&&(e.timeoutHandle=-1,ww(s)),s=e.cancelPendingCommit,s!==null&&(e.cancelPendingCommit=null,s()),wm(),ee=e,Nt=s=ga(e.current,null),Ut=a,Pt=0,pn=null,nr=!1,Js=ho(e,a),bm=!1,Ws=wn=Sm=qr=ar=he=0,rn=Yo=null,Em=!1,(a&8)!==0&&(a|=a&32);var o=e.entangledLanes;if(o!==0)for(e=e.entanglements,o&=a;0<o;){var c=31-un(o),d=1<<c;a|=e[c],o&=~d}return Aa=a,Nc(),s}function ab(e,a){Ct=null,C.H=Jc,a===ko||a===Hc?(a=bv(),Pt=3):a===gv?(a=bv(),Pt=4):Pt=a===S_?8:a!==null&&typeof a=="object"&&typeof a.then=="function"?6:1,pn=a,Nt===null&&(he=1,au(e,En(a,e.current)))}function rb(){var e=C.H;return C.H=Jc,e===null?Jc:e}function sb(){var e=C.A;return C.A=ow,e}function Am(){he=4,nr||(Ut&4194048)!==Ut&&Mn.current!==null||(Js=!0),(ar&134217727)===0&&(qr&134217727)===0||ee===null||ir(ee,Ut,wn,!1)}function Cm(e,a,s){var o=It;It|=2;var c=rb(),d=sb();(ee!==e||Ut!==a)&&(uu=null,ni(e,a)),a=!1;var y=he;t:do try{if(Pt!==0&&Nt!==null){var _=Nt,S=pn;switch(Pt){case 8:wm(),y=6;break t;case 3:case 2:case 9:case 6:Mn.current===null&&(a=!0);var k=Pt;if(Pt=0,pn=null,ai(e,_,S,k),s&&Js){y=0;break t}break;default:k=Pt,Pt=0,pn=null,ai(e,_,S,k)}}uw(),y=he;break}catch(H){ab(e,H)}while(!0);return a&&e.shellSuspendCounter++,ba=Lr=null,It=o,C.H=c,C.A=d,Nt===null&&(ee=null,Ut=0,Nc()),y}function uw(){for(;Nt!==null;)ib(Nt)}function fw(e,a){var s=It;It|=2;var o=rb(),c=sb();ee!==e||Ut!==a?(uu=null,cu=De()+500,ni(e,a)):Js=ho(e,a);t:do try{if(Pt!==0&&Nt!==null){a=Nt;var d=pn;e:switch(Pt){case 1:Pt=0,pn=null,ai(e,a,d,1);break;case 2:case 9:if(vv(d)){Pt=0,pn=null,ob(a);break}a=function(){Pt!==2&&Pt!==9||ee!==e||(Pt=7),ea(e)},d.then(a,a);break t;case 3:Pt=7;break t;case 4:Pt=5;break t;case 7:vv(d)?(Pt=0,pn=null,ob(a)):(Pt=0,pn=null,ai(e,a,d,7));break;case 5:var y=null;switch(Nt.tag){case 26:y=Nt.memoizedState;case 5:case 27:var _=Nt;if(!y||Hb(y)){Pt=0,pn=null;var S=_.sibling;if(S!==null)Nt=S;else{var k=_.return;k!==null?(Nt=k,du(k)):Nt=null}break e}}Pt=0,pn=null,ai(e,a,d,5);break;case 6:Pt=0,pn=null,ai(e,a,d,6);break;case 8:wm(),he=6;break t;default:throw Error(i(462))}}dw();break}catch(H){ab(e,H)}while(!0);return ba=Lr=null,C.H=o,C.A=c,It=s,Nt!==null?0:(ee=null,Ut=0,Nc(),he)}function dw(){for(;Nt!==null&&!qa();)ib(Nt)}function ib(e){var a=N_(e.alternate,e,Aa);e.memoizedProps=e.pendingProps,a===null?du(e):Nt=a}function ob(e){var a=e,s=a.alternate;switch(a.tag){case 15:case 0:a=w_(s,a,a.pendingProps,a.type,void 0,Ut);break;case 11:a=w_(s,a,a.pendingProps,a.type.render,a.ref,Ut);break;case 5:Ph(a);default:L_(s,a),a=Nt=lv(a,Aa),a=N_(s,a,Aa)}e.memoizedProps=e.pendingProps,a===null?du(e):Nt=a}function ai(e,a,s,o){ba=Lr=null,Ph(a),Qs=null,qo=0;var c=a.return;try{if(ew(e,c,a,s,Ut)){he=1,au(e,En(s,e.current)),Nt=null;return}}catch(d){if(c!==null)throw Nt=c,d;he=1,au(e,En(s,e.current)),Nt=null;return}a.flags&32768?(qt||o===1?e=!0:Js||(Ut&536870912)!==0?e=!1:(nr=e=!0,(o===2||o===9||o===3||o===6)&&(o=Mn.current,o!==null&&o.tag===13&&(o.flags|=16384))),lb(a,e)):du(a)}function du(e){var a=e;do{if((a.flags&32768)!==0){lb(a,nr);return}e=a.return;var s=aw(a.alternate,a,Aa);if(s!==null){Nt=s;return}if(a=a.sibling,a!==null){Nt=a;return}Nt=a=e}while(a!==null);he===0&&(he=5)}function lb(e,a){do{var s=rw(e.alternate,e);if(s!==null){s.flags&=32767,Nt=s;return}if(s=e.return,s!==null&&(s.flags|=32768,s.subtreeFlags=0,s.deletions=null),!a&&(e=e.sibling,e!==null)){Nt=e;return}Nt=e=s}while(e!==null);he=6,Nt=null}function cb(e,a,s,o,c,d,y,_,S){e.cancelPendingCommit=null;do hu();while(Fe!==0);if((It&6)!==0)throw Error(i(327));if(a!==null){if(a===e.current)throw Error(i(177));if(d=a.lanes|a.childLanes,d|=_h,PR(e,s,d,y,_,S),e===ee&&(Nt=ee=null,Ut=0),ti=a,sr=e,ei=s,Om=d,Rm=c,W_=o,(a.subtreeFlags&10256)!==0||(a.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,yw(Bt,function(){return mb(),null})):(e.callbackNode=null,e.callbackPriority=0),o=(a.flags&13878)!==0,(a.subtreeFlags&13878)!==0||o){o=C.T,C.T=null,c=I.p,I.p=2,y=It,It|=4;try{sw(e,a,s)}finally{It=y,I.p=c,C.T=o}}Fe=1,ub(),fb(),db()}}function ub(){if(Fe===1){Fe=0;var e=sr,a=ti,s=(a.flags&13878)!==0;if((a.subtreeFlags&13878)!==0||s){s=C.T,C.T=null;var o=I.p;I.p=2;var c=It;It|=4;try{G_(a,e);var d=qm,y=J0(e.containerInfo),_=d.focusedElem,S=d.selectionRange;if(y!==_&&_&&_.ownerDocument&&Z0(_.ownerDocument.documentElement,_)){if(S!==null&&mh(_)){var k=S.start,H=S.end;if(H===void 0&&(H=k),"selectionStart"in _)_.selectionStart=k,_.selectionEnd=Math.min(H,_.value.length);else{var V=_.ownerDocument||document,z=V&&V.defaultView||window;if(z.getSelection){var $=z.getSelection(),_t=_.textContent.length,gt=Math.min(S.start,_t),Yt=S.end===void 0?gt:Math.min(S.end,_t);!$.extend&&gt>Yt&&(y=Yt,Yt=gt,gt=y);var w=X0(_,gt),M=X0(_,Yt);if(w&&M&&($.rangeCount!==1||$.anchorNode!==w.node||$.anchorOffset!==w.offset||$.focusNode!==M.node||$.focusOffset!==M.offset)){var D=V.createRange();D.setStart(w.node,w.offset),$.removeAllRanges(),gt>Yt?($.addRange(D),$.extend(M.node,M.offset)):(D.setEnd(M.node,M.offset),$.addRange(D))}}}}for(V=[],$=_;$=$.parentNode;)$.nodeType===1&&V.push({element:$,left:$.scrollLeft,top:$.scrollTop});for(typeof _.focus=="function"&&_.focus(),_=0;_<V.length;_++){var P=V[_];P.element.scrollLeft=P.left,P.element.scrollTop=P.top}}Ru=!!Bm,qm=Bm=null}finally{It=c,I.p=o,C.T=s}}e.current=a,Fe=2}}function fb(){if(Fe===2){Fe=0;var e=sr,a=ti,s=(a.flags&8772)!==0;if((a.subtreeFlags&8772)!==0||s){s=C.T,C.T=null;var o=I.p;I.p=2;var c=It;It|=4;try{H_(e,a.alternate,a)}finally{It=c,I.p=o,C.T=s}}Fe=3}}function db(){if(Fe===4||Fe===3){Fe=0,_n();var e=sr,a=ti,s=ei,o=W_;(a.subtreeFlags&10256)!==0||(a.flags&10256)!==0?Fe=5:(Fe=0,ti=sr=null,hb(e,e.pendingLanes));var c=e.pendingLanes;if(c===0&&(rr=null),Kd(s),a=a.stateNode,cn&&typeof cn.onCommitFiberRoot=="function")try{cn.onCommitFiberRoot(Or,a,void 0,(a.current.flags&128)===128)}catch{}if(o!==null){a=C.T,c=I.p,I.p=2,C.T=null;try{for(var d=e.onRecoverableError,y=0;y<o.length;y++){var _=o[y];d(_.value,{componentStack:_.stack})}}finally{C.T=a,I.p=c}}(ei&3)!==0&&hu(),ea(e),c=e.pendingLanes,(s&4194090)!==0&&(c&42)!==0?e===Mm?Xo++:(Xo=0,Mm=e):Xo=0,Zo(0)}}function hb(e,a){(e.pooledCacheLanes&=a)===0&&(a=e.pooledCache,a!=null&&(e.pooledCache=null,xo(a)))}function hu(e){return ub(),fb(),db(),mb()}function mb(){if(Fe!==5)return!1;var e=sr,a=Om;Om=0;var s=Kd(ei),o=C.T,c=I.p;try{I.p=32>s?32:s,C.T=null,s=Rm,Rm=null;var d=sr,y=ei;if(Fe=0,ti=sr=null,ei=0,(It&6)!==0)throw Error(i(331));var _=It;if(It|=4,Z_(d.current),Q_(d,d.current,y,s),It=_,Zo(0,!1),cn&&typeof cn.onPostCommitFiberRoot=="function")try{cn.onPostCommitFiberRoot(Or,d)}catch{}return!0}finally{I.p=c,C.T=o,hb(e,a)}}function pb(e,a,s){a=En(s,a),a=rm(e.stateNode,a,2),e=Ya(e,a,2),e!==null&&(mo(e,2),ea(e))}function Jt(e,a,s){if(e.tag===3)pb(e,e,s);else for(;a!==null;){if(a.tag===3){pb(a,e,s);break}else if(a.tag===1){var o=a.stateNode;if(typeof a.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(rr===null||!rr.has(o))){e=En(s,e),s=__(2),o=Ya(a,s,2),o!==null&&(b_(s,o,a,e),mo(o,2),ea(o));break}}a=a.return}}function xm(e,a,s){var o=e.pingCache;if(o===null){o=e.pingCache=new lw;var c=new Set;o.set(a,c)}else c=o.get(a),c===void 0&&(c=new Set,o.set(a,c));c.has(s)||(bm=!0,c.add(s),e=hw.bind(null,e,a,s),a.then(e,e))}function hw(e,a,s){var o=e.pingCache;o!==null&&o.delete(a),e.pingedLanes|=e.suspendedLanes&s,e.warmLanes&=~s,ee===e&&(Ut&s)===s&&(he===4||he===3&&(Ut&62914560)===Ut&&300>De()-Tm?(It&2)===0&&ni(e,0):Sm|=s,Ws===Ut&&(Ws=0)),ea(e)}function yb(e,a){a===0&&(a=d0()),e=Fs(e,a),e!==null&&(mo(e,a),ea(e))}function mw(e){var a=e.memoizedState,s=0;a!==null&&(s=a.retryLane),yb(e,s)}function pw(e,a){var s=0;switch(e.tag){case 13:var o=e.stateNode,c=e.memoizedState;c!==null&&(s=c.retryLane);break;case 19:o=e.stateNode;break;case 22:o=e.stateNode._retryCache;break;default:throw Error(i(314))}o!==null&&o.delete(a),yb(e,s)}function yw(e,a){return ma(e,a)}var mu=null,ri=null,Dm=!1,pu=!1,km=!1,Hr=0;function ea(e){e!==ri&&e.next===null&&(ri===null?mu=ri=e:ri=ri.next=e),pu=!0,Dm||(Dm=!0,vw())}function Zo(e,a){if(!km&&pu){km=!0;do for(var s=!1,o=mu;o!==null;){if(e!==0){var c=o.pendingLanes;if(c===0)var d=0;else{var y=o.suspendedLanes,_=o.pingedLanes;d=(1<<31-un(42|e)+1)-1,d&=c&~(y&~_),d=d&201326741?d&201326741|1:d?d|2:0}d!==0&&(s=!0,bb(o,d))}else d=Ut,d=Sc(o,o===ee?d:0,o.cancelPendingCommit!==null||o.timeoutHandle!==-1),(d&3)===0||ho(o,d)||(s=!0,bb(o,d));o=o.next}while(s);km=!1}}function gw(){gb()}function gb(){pu=Dm=!1;var e=0;Hr!==0&&(Mw()&&(e=Hr),Hr=0);for(var a=De(),s=null,o=mu;o!==null;){var c=o.next,d=vb(o,a);d===0?(o.next=null,s===null?mu=c:s.next=c,c===null&&(ri=s)):(s=o,(e!==0||(d&3)!==0)&&(pu=!0)),o=c}Zo(e)}function vb(e,a){for(var s=e.suspendedLanes,o=e.pingedLanes,c=e.expirationTimes,d=e.pendingLanes&-62914561;0<d;){var y=31-un(d),_=1<<y,S=c[y];S===-1?((_&s)===0||(_&o)!==0)&&(c[y]=IR(_,a)):S<=a&&(e.expiredLanes|=_),d&=~_}if(a=ee,s=Ut,s=Sc(e,e===a?s:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o=e.callbackNode,s===0||e===a&&(Pt===2||Pt===9)||e.cancelPendingCommit!==null)return o!==null&&o!==null&&Un(o),e.callbackNode=null,e.callbackPriority=0;if((s&3)===0||ho(e,s)){if(a=s&-s,a===e.callbackPriority)return a;switch(o!==null&&Un(o),Kd(s)){case 2:case 8:s=fo;break;case 32:s=Bt;break;case 268435456:s=Ts;break;default:s=Bt}return o=_b.bind(null,e),s=ma(s,o),e.callbackPriority=a,e.callbackNode=s,a}return o!==null&&o!==null&&Un(o),e.callbackPriority=2,e.callbackNode=null,2}function _b(e,a){if(Fe!==0&&Fe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var s=e.callbackNode;if(hu()&&e.callbackNode!==s)return null;var o=Ut;return o=Sc(e,e===ee?o:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o===0?null:(eb(e,o,a),vb(e,De()),e.callbackNode!=null&&e.callbackNode===s?_b.bind(null,e):null)}function bb(e,a){if(hu())return null;eb(e,a,!0)}function vw(){Aw(function(){(It&6)!==0?ma(uo,gw):gb()})}function Nm(){return Hr===0&&(Hr=f0()),Hr}function Sb(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Mc(""+e)}function Eb(e,a){var s=a.ownerDocument.createElement("input");return s.name=a.name,s.value=a.value,e.id&&s.setAttribute("form",e.id),a.parentNode.insertBefore(s,a),e=new FormData(e),s.parentNode.removeChild(s),e}function _w(e,a,s,o,c){if(a==="submit"&&s&&s.stateNode===c){var d=Sb((c[tn]||null).action),y=o.submitter;y&&(a=(a=y[tn]||null)?Sb(a.formAction):y.getAttribute("formAction"),a!==null&&(d=a,y=null));var _=new xc("action","action",null,o,c);e.push({event:_,listeners:[{instance:null,listener:function(){if(o.defaultPrevented){if(Hr!==0){var S=y?Eb(c,y):new FormData(c);Wh(s,{pending:!0,data:S,method:c.method,action:d},null,S)}}else typeof d=="function"&&(_.preventDefault(),S=y?Eb(c,y):new FormData(c),Wh(s,{pending:!0,data:S,method:c.method,action:d},d,S))},currentTarget:c}]})}}for(var zm=0;zm<vh.length;zm++){var Lm=vh[zm],bw=Lm.toLowerCase(),Sw=Lm[0].toUpperCase()+Lm.slice(1);Fn(bw,"on"+Sw)}Fn(ev,"onAnimationEnd"),Fn(nv,"onAnimationIteration"),Fn(av,"onAnimationStart"),Fn("dblclick","onDoubleClick"),Fn("focusin","onFocus"),Fn("focusout","onBlur"),Fn(FM,"onTransitionRun"),Fn(jM,"onTransitionStart"),Fn(BM,"onTransitionCancel"),Fn(rv,"onTransitionEnd"),As("onMouseEnter",["mouseout","mouseover"]),As("onMouseLeave",["mouseout","mouseover"]),As("onPointerEnter",["pointerout","pointerover"]),As("onPointerLeave",["pointerout","pointerover"]),Mr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Mr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Mr("onBeforeInput",["compositionend","keypress","textInput","paste"]),Mr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Mr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Mr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Jo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ew=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Jo));function Tb(e,a){a=(a&4)!==0;for(var s=0;s<e.length;s++){var o=e[s],c=o.event;o=o.listeners;t:{var d=void 0;if(a)for(var y=o.length-1;0<=y;y--){var _=o[y],S=_.instance,k=_.currentTarget;if(_=_.listener,S!==d&&c.isPropagationStopped())break t;d=_,c.currentTarget=k;try{d(c)}catch(H){nu(H)}c.currentTarget=null,d=S}else for(y=0;y<o.length;y++){if(_=o[y],S=_.instance,k=_.currentTarget,_=_.listener,S!==d&&c.isPropagationStopped())break t;d=_,c.currentTarget=k;try{d(c)}catch(H){nu(H)}c.currentTarget=null,d=S}}}}function zt(e,a){var s=a[Qd];s===void 0&&(s=a[Qd]=new Set);var o=e+"__bubble";s.has(o)||(Ob(a,e,2,!1),s.add(o))}function $m(e,a,s){var o=0;a&&(o|=4),Ob(s,e,o,a)}var yu="_reactListening"+Math.random().toString(36).slice(2);function Um(e){if(!e[yu]){e[yu]=!0,g0.forEach(function(s){s!=="selectionchange"&&(Ew.has(s)||$m(s,!1,e),$m(s,!0,e))});var a=e.nodeType===9?e:e.ownerDocument;a===null||a[yu]||(a[yu]=!0,$m("selectionchange",!1,a))}}function Ob(e,a,s,o){switch(Qb(a)){case 2:var c=Yw;break;case 8:c=Xw;break;default:c=Zm}s=c.bind(null,a,s,e),c=void 0,!sh||a!=="touchstart"&&a!=="touchmove"&&a!=="wheel"||(c=!0),o?c!==void 0?e.addEventListener(a,s,{capture:!0,passive:c}):e.addEventListener(a,s,!0):c!==void 0?e.addEventListener(a,s,{passive:c}):e.addEventListener(a,s,!1)}function Fm(e,a,s,o,c){var d=o;if((a&1)===0&&(a&2)===0&&o!==null)t:for(;;){if(o===null)return;var y=o.tag;if(y===3||y===4){var _=o.stateNode.containerInfo;if(_===c)break;if(y===4)for(y=o.return;y!==null;){var S=y.tag;if((S===3||S===4)&&y.stateNode.containerInfo===c)return;y=y.return}for(;_!==null;){if(y=Rs(_),y===null)return;if(S=y.tag,S===5||S===6||S===26||S===27){o=d=y;continue t}_=_.parentNode}}o=o.return}D0(function(){var k=d,H=ah(s),V=[];t:{var z=sv.get(e);if(z!==void 0){var $=xc,_t=e;switch(e){case"keypress":if(Ac(s)===0)break t;case"keydown":case"keyup":$=yM;break;case"focusin":_t="focus",$=ch;break;case"focusout":_t="blur",$=ch;break;case"beforeblur":case"afterblur":$=ch;break;case"click":if(s.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":$=z0;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":$=rM;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":$=_M;break;case ev:case nv:case av:$=oM;break;case rv:$=SM;break;case"scroll":case"scrollend":$=nM;break;case"wheel":$=TM;break;case"copy":case"cut":case"paste":$=cM;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":$=$0;break;case"toggle":case"beforetoggle":$=RM}var gt=(a&4)!==0,Yt=!gt&&(e==="scroll"||e==="scrollend"),w=gt?z!==null?z+"Capture":null:z;gt=[];for(var M=k,D;M!==null;){var P=M;if(D=P.stateNode,P=P.tag,P!==5&&P!==26&&P!==27||D===null||w===null||(P=go(M,w),P!=null&&gt.push(Wo(M,P,D))),Yt)break;M=M.return}0<gt.length&&(z=new $(z,_t,null,s,H),V.push({event:z,listeners:gt}))}}if((a&7)===0){t:{if(z=e==="mouseover"||e==="pointerover",$=e==="mouseout"||e==="pointerout",z&&s!==nh&&(_t=s.relatedTarget||s.fromElement)&&(Rs(_t)||_t[Os]))break t;if(($||z)&&(z=H.window===H?H:(z=H.ownerDocument)?z.defaultView||z.parentWindow:window,$?(_t=s.relatedTarget||s.toElement,$=k,_t=_t?Rs(_t):null,_t!==null&&(Yt=u(_t),gt=_t.tag,_t!==Yt||gt!==5&&gt!==27&&gt!==6)&&(_t=null)):($=null,_t=k),$!==_t)){if(gt=z0,P="onMouseLeave",w="onMouseEnter",M="mouse",(e==="pointerout"||e==="pointerover")&&(gt=$0,P="onPointerLeave",w="onPointerEnter",M="pointer"),Yt=$==null?z:yo($),D=_t==null?z:yo(_t),z=new gt(P,M+"leave",$,s,H),z.target=Yt,z.relatedTarget=D,P=null,Rs(H)===k&&(gt=new gt(w,M+"enter",_t,s,H),gt.target=D,gt.relatedTarget=Yt,P=gt),Yt=P,$&&_t)e:{for(gt=$,w=_t,M=0,D=gt;D;D=si(D))M++;for(D=0,P=w;P;P=si(P))D++;for(;0<M-D;)gt=si(gt),M--;for(;0<D-M;)w=si(w),D--;for(;M--;){if(gt===w||w!==null&&gt===w.alternate)break e;gt=si(gt),w=si(w)}gt=null}else gt=null;$!==null&&Rb(V,z,$,gt,!1),_t!==null&&Yt!==null&&Rb(V,Yt,_t,gt,!0)}}t:{if(z=k?yo(k):window,$=z.nodeName&&z.nodeName.toLowerCase(),$==="select"||$==="input"&&z.type==="file")var ct=P0;else if(H0(z))if(V0)ct=LM;else{ct=NM;var xt=kM}else $=z.nodeName,!$||$.toLowerCase()!=="input"||z.type!=="checkbox"&&z.type!=="radio"?k&&eh(k.elementType)&&(ct=P0):ct=zM;if(ct&&(ct=ct(e,k))){I0(V,ct,s,H);break t}xt&&xt(e,z,k),e==="focusout"&&k&&z.type==="number"&&k.memoizedProps.value!=null&&th(z,"number",z.value)}switch(xt=k?yo(k):window,e){case"focusin":(H0(xt)||xt.contentEditable==="true")&&(Ls=xt,ph=k,Ro=null);break;case"focusout":Ro=ph=Ls=null;break;case"mousedown":yh=!0;break;case"contextmenu":case"mouseup":case"dragend":yh=!1,W0(V,s,H);break;case"selectionchange":if(UM)break;case"keydown":case"keyup":W0(V,s,H)}var ht;if(fh)t:{switch(e){case"compositionstart":var vt="onCompositionStart";break t;case"compositionend":vt="onCompositionEnd";break t;case"compositionupdate":vt="onCompositionUpdate";break t}vt=void 0}else zs?B0(e,s)&&(vt="onCompositionEnd"):e==="keydown"&&s.keyCode===229&&(vt="onCompositionStart");vt&&(U0&&s.locale!=="ko"&&(zs||vt!=="onCompositionStart"?vt==="onCompositionEnd"&&zs&&(ht=k0()):(Va=H,ih="value"in Va?Va.value:Va.textContent,zs=!0)),xt=gu(k,vt),0<xt.length&&(vt=new L0(vt,e,null,s,H),V.push({event:vt,listeners:xt}),ht?vt.data=ht:(ht=q0(s),ht!==null&&(vt.data=ht)))),(ht=wM?AM(e,s):CM(e,s))&&(vt=gu(k,"onBeforeInput"),0<vt.length&&(xt=new L0("onBeforeInput","beforeinput",null,s,H),V.push({event:xt,listeners:vt}),xt.data=ht)),_w(V,e,k,s,H)}Tb(V,a)})}function Wo(e,a,s){return{instance:e,listener:a,currentTarget:s}}function gu(e,a){for(var s=a+"Capture",o=[];e!==null;){var c=e,d=c.stateNode;if(c=c.tag,c!==5&&c!==26&&c!==27||d===null||(c=go(e,s),c!=null&&o.unshift(Wo(e,c,d)),c=go(e,a),c!=null&&o.push(Wo(e,c,d))),e.tag===3)return o;e=e.return}return[]}function si(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Rb(e,a,s,o,c){for(var d=a._reactName,y=[];s!==null&&s!==o;){var _=s,S=_.alternate,k=_.stateNode;if(_=_.tag,S!==null&&S===o)break;_!==5&&_!==26&&_!==27||k===null||(S=k,c?(k=go(s,d),k!=null&&y.unshift(Wo(s,k,S))):c||(k=go(s,d),k!=null&&y.push(Wo(s,k,S)))),s=s.return}y.length!==0&&e.push({event:a,listeners:y})}var Tw=/\r\n?/g,Ow=/\u0000|\uFFFD/g;function Mb(e){return(typeof e=="string"?e:""+e).replace(Tw,`
`).replace(Ow,"")}function wb(e,a){return a=Mb(a),Mb(e)===a}function vu(){}function Qt(e,a,s,o,c,d){switch(s){case"children":typeof o=="string"?a==="body"||a==="textarea"&&o===""||Ds(e,o):(typeof o=="number"||typeof o=="bigint")&&a!=="body"&&Ds(e,""+o);break;case"className":Tc(e,"class",o);break;case"tabIndex":Tc(e,"tabindex",o);break;case"dir":case"role":case"viewBox":case"width":case"height":Tc(e,s,o);break;case"style":C0(e,o,d);break;case"data":if(a!=="object"){Tc(e,"data",o);break}case"src":case"href":if(o===""&&(a!=="a"||s!=="href")){e.removeAttribute(s);break}if(o==null||typeof o=="function"||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(s);break}o=Mc(""+o),e.setAttribute(s,o);break;case"action":case"formAction":if(typeof o=="function"){e.setAttribute(s,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof d=="function"&&(s==="formAction"?(a!=="input"&&Qt(e,a,"name",c.name,c,null),Qt(e,a,"formEncType",c.formEncType,c,null),Qt(e,a,"formMethod",c.formMethod,c,null),Qt(e,a,"formTarget",c.formTarget,c,null)):(Qt(e,a,"encType",c.encType,c,null),Qt(e,a,"method",c.method,c,null),Qt(e,a,"target",c.target,c,null)));if(o==null||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(s);break}o=Mc(""+o),e.setAttribute(s,o);break;case"onClick":o!=null&&(e.onclick=vu);break;case"onScroll":o!=null&&zt("scroll",e);break;case"onScrollEnd":o!=null&&zt("scrollend",e);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(i(61));if(s=o.__html,s!=null){if(c.children!=null)throw Error(i(60));e.innerHTML=s}}break;case"multiple":e.multiple=o&&typeof o!="function"&&typeof o!="symbol";break;case"muted":e.muted=o&&typeof o!="function"&&typeof o!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(o==null||typeof o=="function"||typeof o=="boolean"||typeof o=="symbol"){e.removeAttribute("xlink:href");break}s=Mc(""+o),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",s);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(s,""+o):e.removeAttribute(s);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":o&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(s,""):e.removeAttribute(s);break;case"capture":case"download":o===!0?e.setAttribute(s,""):o!==!1&&o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(s,o):e.removeAttribute(s);break;case"cols":case"rows":case"size":case"span":o!=null&&typeof o!="function"&&typeof o!="symbol"&&!isNaN(o)&&1<=o?e.setAttribute(s,o):e.removeAttribute(s);break;case"rowSpan":case"start":o==null||typeof o=="function"||typeof o=="symbol"||isNaN(o)?e.removeAttribute(s):e.setAttribute(s,o);break;case"popover":zt("beforetoggle",e),zt("toggle",e),Ec(e,"popover",o);break;case"xlinkActuate":pa(e,"http://www.w3.org/1999/xlink","xlink:actuate",o);break;case"xlinkArcrole":pa(e,"http://www.w3.org/1999/xlink","xlink:arcrole",o);break;case"xlinkRole":pa(e,"http://www.w3.org/1999/xlink","xlink:role",o);break;case"xlinkShow":pa(e,"http://www.w3.org/1999/xlink","xlink:show",o);break;case"xlinkTitle":pa(e,"http://www.w3.org/1999/xlink","xlink:title",o);break;case"xlinkType":pa(e,"http://www.w3.org/1999/xlink","xlink:type",o);break;case"xmlBase":pa(e,"http://www.w3.org/XML/1998/namespace","xml:base",o);break;case"xmlLang":pa(e,"http://www.w3.org/XML/1998/namespace","xml:lang",o);break;case"xmlSpace":pa(e,"http://www.w3.org/XML/1998/namespace","xml:space",o);break;case"is":Ec(e,"is",o);break;case"innerText":case"textContent":break;default:(!(2<s.length)||s[0]!=="o"&&s[0]!=="O"||s[1]!=="n"&&s[1]!=="N")&&(s=tM.get(s)||s,Ec(e,s,o))}}function jm(e,a,s,o,c,d){switch(s){case"style":C0(e,o,d);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(i(61));if(s=o.__html,s!=null){if(c.children!=null)throw Error(i(60));e.innerHTML=s}}break;case"children":typeof o=="string"?Ds(e,o):(typeof o=="number"||typeof o=="bigint")&&Ds(e,""+o);break;case"onScroll":o!=null&&zt("scroll",e);break;case"onScrollEnd":o!=null&&zt("scrollend",e);break;case"onClick":o!=null&&(e.onclick=vu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!v0.hasOwnProperty(s))t:{if(s[0]==="o"&&s[1]==="n"&&(c=s.endsWith("Capture"),a=s.slice(2,c?s.length-7:void 0),d=e[tn]||null,d=d!=null?d[s]:null,typeof d=="function"&&e.removeEventListener(a,d,c),typeof o=="function")){typeof d!="function"&&d!==null&&(s in e?e[s]=null:e.hasAttribute(s)&&e.removeAttribute(s)),e.addEventListener(a,o,c);break t}s in e?e[s]=o:o===!0?e.setAttribute(s,""):Ec(e,s,o)}}}function je(e,a,s){switch(a){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":zt("error",e),zt("load",e);var o=!1,c=!1,d;for(d in s)if(s.hasOwnProperty(d)){var y=s[d];if(y!=null)switch(d){case"src":o=!0;break;case"srcSet":c=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,a));default:Qt(e,a,d,y,s,null)}}c&&Qt(e,a,"srcSet",s.srcSet,s,null),o&&Qt(e,a,"src",s.src,s,null);return;case"input":zt("invalid",e);var _=d=y=c=null,S=null,k=null;for(o in s)if(s.hasOwnProperty(o)){var H=s[o];if(H!=null)switch(o){case"name":c=H;break;case"type":y=H;break;case"checked":S=H;break;case"defaultChecked":k=H;break;case"value":d=H;break;case"defaultValue":_=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(i(137,a));break;default:Qt(e,a,o,H,s,null)}}R0(e,d,_,S,k,y,c,!1),Oc(e);return;case"select":zt("invalid",e),o=y=d=null;for(c in s)if(s.hasOwnProperty(c)&&(_=s[c],_!=null))switch(c){case"value":d=_;break;case"defaultValue":y=_;break;case"multiple":o=_;default:Qt(e,a,c,_,s,null)}a=d,s=y,e.multiple=!!o,a!=null?xs(e,!!o,a,!1):s!=null&&xs(e,!!o,s,!0);return;case"textarea":zt("invalid",e),d=c=o=null;for(y in s)if(s.hasOwnProperty(y)&&(_=s[y],_!=null))switch(y){case"value":o=_;break;case"defaultValue":c=_;break;case"children":d=_;break;case"dangerouslySetInnerHTML":if(_!=null)throw Error(i(91));break;default:Qt(e,a,y,_,s,null)}w0(e,o,c,d),Oc(e);return;case"option":for(S in s)if(s.hasOwnProperty(S)&&(o=s[S],o!=null))switch(S){case"selected":e.selected=o&&typeof o!="function"&&typeof o!="symbol";break;default:Qt(e,a,S,o,s,null)}return;case"dialog":zt("beforetoggle",e),zt("toggle",e),zt("cancel",e),zt("close",e);break;case"iframe":case"object":zt("load",e);break;case"video":case"audio":for(o=0;o<Jo.length;o++)zt(Jo[o],e);break;case"image":zt("error",e),zt("load",e);break;case"details":zt("toggle",e);break;case"embed":case"source":case"link":zt("error",e),zt("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(k in s)if(s.hasOwnProperty(k)&&(o=s[k],o!=null))switch(k){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,a));default:Qt(e,a,k,o,s,null)}return;default:if(eh(a)){for(H in s)s.hasOwnProperty(H)&&(o=s[H],o!==void 0&&jm(e,a,H,o,s,void 0));return}}for(_ in s)s.hasOwnProperty(_)&&(o=s[_],o!=null&&Qt(e,a,_,o,s,null))}function Rw(e,a,s,o){switch(a){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var c=null,d=null,y=null,_=null,S=null,k=null,H=null;for($ in s){var V=s[$];if(s.hasOwnProperty($)&&V!=null)switch($){case"checked":break;case"value":break;case"defaultValue":S=V;default:o.hasOwnProperty($)||Qt(e,a,$,null,o,V)}}for(var z in o){var $=o[z];if(V=s[z],o.hasOwnProperty(z)&&($!=null||V!=null))switch(z){case"type":d=$;break;case"name":c=$;break;case"checked":k=$;break;case"defaultChecked":H=$;break;case"value":y=$;break;case"defaultValue":_=$;break;case"children":case"dangerouslySetInnerHTML":if($!=null)throw Error(i(137,a));break;default:$!==V&&Qt(e,a,z,$,o,V)}}Wd(e,y,_,S,k,H,d,c);return;case"select":$=y=_=z=null;for(d in s)if(S=s[d],s.hasOwnProperty(d)&&S!=null)switch(d){case"value":break;case"multiple":$=S;default:o.hasOwnProperty(d)||Qt(e,a,d,null,o,S)}for(c in o)if(d=o[c],S=s[c],o.hasOwnProperty(c)&&(d!=null||S!=null))switch(c){case"value":z=d;break;case"defaultValue":_=d;break;case"multiple":y=d;default:d!==S&&Qt(e,a,c,d,o,S)}a=_,s=y,o=$,z!=null?xs(e,!!s,z,!1):!!o!=!!s&&(a!=null?xs(e,!!s,a,!0):xs(e,!!s,s?[]:"",!1));return;case"textarea":$=z=null;for(_ in s)if(c=s[_],s.hasOwnProperty(_)&&c!=null&&!o.hasOwnProperty(_))switch(_){case"value":break;case"children":break;default:Qt(e,a,_,null,o,c)}for(y in o)if(c=o[y],d=s[y],o.hasOwnProperty(y)&&(c!=null||d!=null))switch(y){case"value":z=c;break;case"defaultValue":$=c;break;case"children":break;case"dangerouslySetInnerHTML":if(c!=null)throw Error(i(91));break;default:c!==d&&Qt(e,a,y,c,o,d)}M0(e,z,$);return;case"option":for(var _t in s)if(z=s[_t],s.hasOwnProperty(_t)&&z!=null&&!o.hasOwnProperty(_t))switch(_t){case"selected":e.selected=!1;break;default:Qt(e,a,_t,null,o,z)}for(S in o)if(z=o[S],$=s[S],o.hasOwnProperty(S)&&z!==$&&(z!=null||$!=null))switch(S){case"selected":e.selected=z&&typeof z!="function"&&typeof z!="symbol";break;default:Qt(e,a,S,z,o,$)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var gt in s)z=s[gt],s.hasOwnProperty(gt)&&z!=null&&!o.hasOwnProperty(gt)&&Qt(e,a,gt,null,o,z);for(k in o)if(z=o[k],$=s[k],o.hasOwnProperty(k)&&z!==$&&(z!=null||$!=null))switch(k){case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(i(137,a));break;default:Qt(e,a,k,z,o,$)}return;default:if(eh(a)){for(var Yt in s)z=s[Yt],s.hasOwnProperty(Yt)&&z!==void 0&&!o.hasOwnProperty(Yt)&&jm(e,a,Yt,void 0,o,z);for(H in o)z=o[H],$=s[H],!o.hasOwnProperty(H)||z===$||z===void 0&&$===void 0||jm(e,a,H,z,o,$);return}}for(var w in s)z=s[w],s.hasOwnProperty(w)&&z!=null&&!o.hasOwnProperty(w)&&Qt(e,a,w,null,o,z);for(V in o)z=o[V],$=s[V],!o.hasOwnProperty(V)||z===$||z==null&&$==null||Qt(e,a,V,z,o,$)}var Bm=null,qm=null;function _u(e){return e.nodeType===9?e:e.ownerDocument}function Ab(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Cb(e,a){if(e===0)switch(a){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&a==="foreignObject"?0:e}function Hm(e,a){return e==="textarea"||e==="noscript"||typeof a.children=="string"||typeof a.children=="number"||typeof a.children=="bigint"||typeof a.dangerouslySetInnerHTML=="object"&&a.dangerouslySetInnerHTML!==null&&a.dangerouslySetInnerHTML.__html!=null}var Im=null;function Mw(){var e=window.event;return e&&e.type==="popstate"?e===Im?!1:(Im=e,!0):(Im=null,!1)}var xb=typeof setTimeout=="function"?setTimeout:void 0,ww=typeof clearTimeout=="function"?clearTimeout:void 0,Db=typeof Promise=="function"?Promise:void 0,Aw=typeof queueMicrotask=="function"?queueMicrotask:typeof Db<"u"?function(e){return Db.resolve(null).then(e).catch(Cw)}:xb;function Cw(e){setTimeout(function(){throw e})}function or(e){return e==="head"}function kb(e,a){var s=a,o=0,c=0;do{var d=s.nextSibling;if(e.removeChild(s),d&&d.nodeType===8)if(s=d.data,s==="/$"){if(0<o&&8>o){s=o;var y=e.ownerDocument;if(s&1&&tl(y.documentElement),s&2&&tl(y.body),s&4)for(s=y.head,tl(s),y=s.firstChild;y;){var _=y.nextSibling,S=y.nodeName;y[po]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&y.rel.toLowerCase()==="stylesheet"||s.removeChild(y),y=_}}if(c===0){e.removeChild(d),ll(a);return}c--}else s==="$"||s==="$?"||s==="$!"?c++:o=s.charCodeAt(0)-48;else o=0;s=d}while(s);ll(a)}function Pm(e){var a=e.firstChild;for(a&&a.nodeType===10&&(a=a.nextSibling);a;){var s=a;switch(a=a.nextSibling,s.nodeName){case"HTML":case"HEAD":case"BODY":Pm(s),Yd(s);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(s.rel.toLowerCase()==="stylesheet")continue}e.removeChild(s)}}function xw(e,a,s,o){for(;e.nodeType===1;){var c=s;if(e.nodeName.toLowerCase()!==a.toLowerCase()){if(!o&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(o){if(!e[po])switch(a){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(d=e.getAttribute("rel"),d==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(d!==c.rel||e.getAttribute("href")!==(c.href==null||c.href===""?null:c.href)||e.getAttribute("crossorigin")!==(c.crossOrigin==null?null:c.crossOrigin)||e.getAttribute("title")!==(c.title==null?null:c.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(d=e.getAttribute("src"),(d!==(c.src==null?null:c.src)||e.getAttribute("type")!==(c.type==null?null:c.type)||e.getAttribute("crossorigin")!==(c.crossOrigin==null?null:c.crossOrigin))&&d&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(a==="input"&&e.type==="hidden"){var d=c.name==null?null:""+c.name;if(c.type==="hidden"&&e.getAttribute("name")===d)return e}else return e;if(e=Bn(e.nextSibling),e===null)break}return null}function Dw(e,a,s){if(a==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!s||(e=Bn(e.nextSibling),e===null))return null;return e}function Vm(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function kw(e,a){var s=e.ownerDocument;if(e.data!=="$?"||s.readyState==="complete")a();else{var o=function(){a(),s.removeEventListener("DOMContentLoaded",o)};s.addEventListener("DOMContentLoaded",o),e._reactRetry=o}}function Bn(e){for(;e!=null;e=e.nextSibling){var a=e.nodeType;if(a===1||a===3)break;if(a===8){if(a=e.data,a==="$"||a==="$!"||a==="$?"||a==="F!"||a==="F")break;if(a==="/$")return null}}return e}var Gm=null;function Nb(e){e=e.previousSibling;for(var a=0;e;){if(e.nodeType===8){var s=e.data;if(s==="$"||s==="$!"||s==="$?"){if(a===0)return e;a--}else s==="/$"&&a++}e=e.previousSibling}return null}function zb(e,a,s){switch(a=_u(s),e){case"html":if(e=a.documentElement,!e)throw Error(i(452));return e;case"head":if(e=a.head,!e)throw Error(i(453));return e;case"body":if(e=a.body,!e)throw Error(i(454));return e;default:throw Error(i(451))}}function tl(e){for(var a=e.attributes;a.length;)e.removeAttributeNode(a[0]);Yd(e)}var An=new Map,Lb=new Set;function bu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Ca=I.d;I.d={f:Nw,r:zw,D:Lw,C:$w,L:Uw,m:Fw,X:Bw,S:jw,M:qw};function Nw(){var e=Ca.f(),a=fu();return e||a}function zw(e){var a=Ms(e);a!==null&&a.tag===5&&a.type==="form"?e_(a):Ca.r(e)}var ii=typeof document>"u"?null:document;function $b(e,a,s){var o=ii;if(o&&typeof a=="string"&&a){var c=Sn(a);c='link[rel="'+e+'"][href="'+c+'"]',typeof s=="string"&&(c+='[crossorigin="'+s+'"]'),Lb.has(c)||(Lb.add(c),e={rel:e,crossOrigin:s,href:a},o.querySelector(c)===null&&(a=o.createElement("link"),je(a,"link",e),ke(a),o.head.appendChild(a)))}}function Lw(e){Ca.D(e),$b("dns-prefetch",e,null)}function $w(e,a){Ca.C(e,a),$b("preconnect",e,a)}function Uw(e,a,s){Ca.L(e,a,s);var o=ii;if(o&&e&&a){var c='link[rel="preload"][as="'+Sn(a)+'"]';a==="image"&&s&&s.imageSrcSet?(c+='[imagesrcset="'+Sn(s.imageSrcSet)+'"]',typeof s.imageSizes=="string"&&(c+='[imagesizes="'+Sn(s.imageSizes)+'"]')):c+='[href="'+Sn(e)+'"]';var d=c;switch(a){case"style":d=oi(e);break;case"script":d=li(e)}An.has(d)||(e=g({rel:"preload",href:a==="image"&&s&&s.imageSrcSet?void 0:e,as:a},s),An.set(d,e),o.querySelector(c)!==null||a==="style"&&o.querySelector(el(d))||a==="script"&&o.querySelector(nl(d))||(a=o.createElement("link"),je(a,"link",e),ke(a),o.head.appendChild(a)))}}function Fw(e,a){Ca.m(e,a);var s=ii;if(s&&e){var o=a&&typeof a.as=="string"?a.as:"script",c='link[rel="modulepreload"][as="'+Sn(o)+'"][href="'+Sn(e)+'"]',d=c;switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":d=li(e)}if(!An.has(d)&&(e=g({rel:"modulepreload",href:e},a),An.set(d,e),s.querySelector(c)===null)){switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(s.querySelector(nl(d)))return}o=s.createElement("link"),je(o,"link",e),ke(o),s.head.appendChild(o)}}}function jw(e,a,s){Ca.S(e,a,s);var o=ii;if(o&&e){var c=ws(o).hoistableStyles,d=oi(e);a=a||"default";var y=c.get(d);if(!y){var _={loading:0,preload:null};if(y=o.querySelector(el(d)))_.loading=5;else{e=g({rel:"stylesheet",href:e,"data-precedence":a},s),(s=An.get(d))&&Km(e,s);var S=y=o.createElement("link");ke(S),je(S,"link",e),S._p=new Promise(function(k,H){S.onload=k,S.onerror=H}),S.addEventListener("load",function(){_.loading|=1}),S.addEventListener("error",function(){_.loading|=2}),_.loading|=4,Su(y,a,o)}y={type:"stylesheet",instance:y,count:1,state:_},c.set(d,y)}}}function Bw(e,a){Ca.X(e,a);var s=ii;if(s&&e){var o=ws(s).hoistableScripts,c=li(e),d=o.get(c);d||(d=s.querySelector(nl(c)),d||(e=g({src:e,async:!0},a),(a=An.get(c))&&Qm(e,a),d=s.createElement("script"),ke(d),je(d,"link",e),s.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},o.set(c,d))}}function qw(e,a){Ca.M(e,a);var s=ii;if(s&&e){var o=ws(s).hoistableScripts,c=li(e),d=o.get(c);d||(d=s.querySelector(nl(c)),d||(e=g({src:e,async:!0,type:"module"},a),(a=An.get(c))&&Qm(e,a),d=s.createElement("script"),ke(d),je(d,"link",e),s.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},o.set(c,d))}}function Ub(e,a,s,o){var c=(c=lt.current)?bu(c):null;if(!c)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return typeof s.precedence=="string"&&typeof s.href=="string"?(a=oi(s.href),s=ws(c).hoistableStyles,o=s.get(a),o||(o={type:"style",instance:null,count:0,state:null},s.set(a,o)),o):{type:"void",instance:null,count:0,state:null};case"link":if(s.rel==="stylesheet"&&typeof s.href=="string"&&typeof s.precedence=="string"){e=oi(s.href);var d=ws(c).hoistableStyles,y=d.get(e);if(y||(c=c.ownerDocument||c,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},d.set(e,y),(d=c.querySelector(el(e)))&&!d._p&&(y.instance=d,y.state.loading=5),An.has(e)||(s={rel:"preload",as:"style",href:s.href,crossOrigin:s.crossOrigin,integrity:s.integrity,media:s.media,hrefLang:s.hrefLang,referrerPolicy:s.referrerPolicy},An.set(e,s),d||Hw(c,e,s,y.state))),a&&o===null)throw Error(i(528,""));return y}if(a&&o!==null)throw Error(i(529,""));return null;case"script":return a=s.async,s=s.src,typeof s=="string"&&a&&typeof a!="function"&&typeof a!="symbol"?(a=li(s),s=ws(c).hoistableScripts,o=s.get(a),o||(o={type:"script",instance:null,count:0,state:null},s.set(a,o)),o):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function oi(e){return'href="'+Sn(e)+'"'}function el(e){return'link[rel="stylesheet"]['+e+"]"}function Fb(e){return g({},e,{"data-precedence":e.precedence,precedence:null})}function Hw(e,a,s,o){e.querySelector('link[rel="preload"][as="style"]['+a+"]")?o.loading=1:(a=e.createElement("link"),o.preload=a,a.addEventListener("load",function(){return o.loading|=1}),a.addEventListener("error",function(){return o.loading|=2}),je(a,"link",s),ke(a),e.head.appendChild(a))}function li(e){return'[src="'+Sn(e)+'"]'}function nl(e){return"script[async]"+e}function jb(e,a,s){if(a.count++,a.instance===null)switch(a.type){case"style":var o=e.querySelector('style[data-href~="'+Sn(s.href)+'"]');if(o)return a.instance=o,ke(o),o;var c=g({},s,{"data-href":s.href,"data-precedence":s.precedence,href:null,precedence:null});return o=(e.ownerDocument||e).createElement("style"),ke(o),je(o,"style",c),Su(o,s.precedence,e),a.instance=o;case"stylesheet":c=oi(s.href);var d=e.querySelector(el(c));if(d)return a.state.loading|=4,a.instance=d,ke(d),d;o=Fb(s),(c=An.get(c))&&Km(o,c),d=(e.ownerDocument||e).createElement("link"),ke(d);var y=d;return y._p=new Promise(function(_,S){y.onload=_,y.onerror=S}),je(d,"link",o),a.state.loading|=4,Su(d,s.precedence,e),a.instance=d;case"script":return d=li(s.src),(c=e.querySelector(nl(d)))?(a.instance=c,ke(c),c):(o=s,(c=An.get(d))&&(o=g({},s),Qm(o,c)),e=e.ownerDocument||e,c=e.createElement("script"),ke(c),je(c,"link",o),e.head.appendChild(c),a.instance=c);case"void":return null;default:throw Error(i(443,a.type))}else a.type==="stylesheet"&&(a.state.loading&4)===0&&(o=a.instance,a.state.loading|=4,Su(o,s.precedence,e));return a.instance}function Su(e,a,s){for(var o=s.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),c=o.length?o[o.length-1]:null,d=c,y=0;y<o.length;y++){var _=o[y];if(_.dataset.precedence===a)d=_;else if(d!==c)break}d?d.parentNode.insertBefore(e,d.nextSibling):(a=s.nodeType===9?s.head:s,a.insertBefore(e,a.firstChild))}function Km(e,a){e.crossOrigin==null&&(e.crossOrigin=a.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=a.referrerPolicy),e.title==null&&(e.title=a.title)}function Qm(e,a){e.crossOrigin==null&&(e.crossOrigin=a.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=a.referrerPolicy),e.integrity==null&&(e.integrity=a.integrity)}var Eu=null;function Bb(e,a,s){if(Eu===null){var o=new Map,c=Eu=new Map;c.set(s,o)}else c=Eu,o=c.get(s),o||(o=new Map,c.set(s,o));if(o.has(e))return o;for(o.set(e,null),s=s.getElementsByTagName(e),c=0;c<s.length;c++){var d=s[c];if(!(d[po]||d[He]||e==="link"&&d.getAttribute("rel")==="stylesheet")&&d.namespaceURI!=="http://www.w3.org/2000/svg"){var y=d.getAttribute(a)||"";y=e+y;var _=o.get(y);_?_.push(d):o.set(y,[d])}}return o}function qb(e,a,s){e=e.ownerDocument||e,e.head.insertBefore(s,a==="title"?e.querySelector("head > title"):null)}function Iw(e,a,s){if(s===1||a.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof a.precedence!="string"||typeof a.href!="string"||a.href==="")break;return!0;case"link":if(typeof a.rel!="string"||typeof a.href!="string"||a.href===""||a.onLoad||a.onError)break;switch(a.rel){case"stylesheet":return e=a.disabled,typeof a.precedence=="string"&&e==null;default:return!0}case"script":if(a.async&&typeof a.async!="function"&&typeof a.async!="symbol"&&!a.onLoad&&!a.onError&&a.src&&typeof a.src=="string")return!0}return!1}function Hb(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var al=null;function Pw(){}function Vw(e,a,s){if(al===null)throw Error(i(475));var o=al;if(a.type==="stylesheet"&&(typeof s.media!="string"||matchMedia(s.media).matches!==!1)&&(a.state.loading&4)===0){if(a.instance===null){var c=oi(s.href),d=e.querySelector(el(c));if(d){e=d._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(o.count++,o=Tu.bind(o),e.then(o,o)),a.state.loading|=4,a.instance=d,ke(d);return}d=e.ownerDocument||e,s=Fb(s),(c=An.get(c))&&Km(s,c),d=d.createElement("link"),ke(d);var y=d;y._p=new Promise(function(_,S){y.onload=_,y.onerror=S}),je(d,"link",s),a.instance=d}o.stylesheets===null&&(o.stylesheets=new Map),o.stylesheets.set(a,e),(e=a.state.preload)&&(a.state.loading&3)===0&&(o.count++,a=Tu.bind(o),e.addEventListener("load",a),e.addEventListener("error",a))}}function Gw(){if(al===null)throw Error(i(475));var e=al;return e.stylesheets&&e.count===0&&Ym(e,e.stylesheets),0<e.count?function(a){var s=setTimeout(function(){if(e.stylesheets&&Ym(e,e.stylesheets),e.unsuspend){var o=e.unsuspend;e.unsuspend=null,o()}},6e4);return e.unsuspend=a,function(){e.unsuspend=null,clearTimeout(s)}}:null}function Tu(){if(this.count--,this.count===0){if(this.stylesheets)Ym(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ou=null;function Ym(e,a){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ou=new Map,a.forEach(Kw,e),Ou=null,Tu.call(e))}function Kw(e,a){if(!(a.state.loading&4)){var s=Ou.get(e);if(s)var o=s.get(null);else{s=new Map,Ou.set(e,s);for(var c=e.querySelectorAll("link[data-precedence],style[data-precedence]"),d=0;d<c.length;d++){var y=c[d];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(s.set(y.dataset.precedence,y),o=y)}o&&s.set(null,o)}c=a.instance,y=c.getAttribute("data-precedence"),d=s.get(y)||o,d===o&&s.set(null,c),s.set(y,c),this.count++,o=Tu.bind(this),c.addEventListener("load",o),c.addEventListener("error",o),d?d.parentNode.insertBefore(c,d.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(c,e.firstChild)),a.state.loading|=4}}var rl={$$typeof:q,Provider:null,Consumer:null,_currentValue:st,_currentValue2:st,_threadCount:0};function Qw(e,a,s,o,c,d,y,_){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Vd(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vd(0),this.hiddenUpdates=Vd(null),this.identifierPrefix=o,this.onUncaughtError=c,this.onCaughtError=d,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=_,this.incompleteTransitions=new Map}function Ib(e,a,s,o,c,d,y,_,S,k,H,V){return e=new Qw(e,a,s,y,_,S,k,V),a=1,d===!0&&(a|=24),d=dn(3,null,null,a),e.current=d,d.stateNode=e,a=xh(),a.refCount++,e.pooledCache=a,a.refCount++,d.memoizedState={element:o,isDehydrated:s,cache:a},zh(d),e}function Pb(e){return e?(e=js,e):js}function Vb(e,a,s,o,c,d){c=Pb(c),o.context===null?o.context=c:o.pendingContext=c,o=Qa(a),o.payload={element:s},d=d===void 0?null:d,d!==null&&(o.callback=d),s=Ya(e,o,a),s!==null&&(gn(s,e,a),zo(s,e,a))}function Gb(e,a){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var s=e.retryLane;e.retryLane=s!==0&&s<a?s:a}}function Xm(e,a){Gb(e,a),(e=e.alternate)&&Gb(e,a)}function Kb(e){if(e.tag===13){var a=Fs(e,67108864);a!==null&&gn(a,e,67108864),Xm(e,67108864)}}var Ru=!0;function Yw(e,a,s,o){var c=C.T;C.T=null;var d=I.p;try{I.p=2,Zm(e,a,s,o)}finally{I.p=d,C.T=c}}function Xw(e,a,s,o){var c=C.T;C.T=null;var d=I.p;try{I.p=8,Zm(e,a,s,o)}finally{I.p=d,C.T=c}}function Zm(e,a,s,o){if(Ru){var c=Jm(o);if(c===null)Fm(e,a,o,Mu,s),Yb(e,o);else if(Jw(c,e,a,s,o))o.stopPropagation();else if(Yb(e,o),a&4&&-1<Zw.indexOf(e)){for(;c!==null;){var d=Ms(c);if(d!==null)switch(d.tag){case 3:if(d=d.stateNode,d.current.memoizedState.isDehydrated){var y=Rr(d.pendingLanes);if(y!==0){var _=d;for(_.pendingLanes|=2,_.entangledLanes|=2;y;){var S=1<<31-un(y);_.entanglements[1]|=S,y&=~S}ea(d),(It&6)===0&&(cu=De()+500,Zo(0))}}break;case 13:_=Fs(d,2),_!==null&&gn(_,d,2),fu(),Xm(d,2)}if(d=Jm(o),d===null&&Fm(e,a,o,Mu,s),d===c)break;c=d}c!==null&&o.stopPropagation()}else Fm(e,a,o,null,s)}}function Jm(e){return e=ah(e),Wm(e)}var Mu=null;function Wm(e){if(Mu=null,e=Rs(e),e!==null){var a=u(e);if(a===null)e=null;else{var s=a.tag;if(s===13){if(e=f(a),e!==null)return e;e=null}else if(s===3){if(a.stateNode.current.memoizedState.isDehydrated)return a.tag===3?a.stateNode.containerInfo:null;e=null}else a!==e&&(e=null)}}return Mu=e,null}function Qb(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ha()){case uo:return 2;case fo:return 8;case Bt:case ie:return 32;case Ts:return 268435456;default:return 32}default:return 32}}var tp=!1,lr=null,cr=null,ur=null,sl=new Map,il=new Map,fr=[],Zw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Yb(e,a){switch(e){case"focusin":case"focusout":lr=null;break;case"dragenter":case"dragleave":cr=null;break;case"mouseover":case"mouseout":ur=null;break;case"pointerover":case"pointerout":sl.delete(a.pointerId);break;case"gotpointercapture":case"lostpointercapture":il.delete(a.pointerId)}}function ol(e,a,s,o,c,d){return e===null||e.nativeEvent!==d?(e={blockedOn:a,domEventName:s,eventSystemFlags:o,nativeEvent:d,targetContainers:[c]},a!==null&&(a=Ms(a),a!==null&&Kb(a)),e):(e.eventSystemFlags|=o,a=e.targetContainers,c!==null&&a.indexOf(c)===-1&&a.push(c),e)}function Jw(e,a,s,o,c){switch(a){case"focusin":return lr=ol(lr,e,a,s,o,c),!0;case"dragenter":return cr=ol(cr,e,a,s,o,c),!0;case"mouseover":return ur=ol(ur,e,a,s,o,c),!0;case"pointerover":var d=c.pointerId;return sl.set(d,ol(sl.get(d)||null,e,a,s,o,c)),!0;case"gotpointercapture":return d=c.pointerId,il.set(d,ol(il.get(d)||null,e,a,s,o,c)),!0}return!1}function Xb(e){var a=Rs(e.target);if(a!==null){var s=u(a);if(s!==null){if(a=s.tag,a===13){if(a=f(s),a!==null){e.blockedOn=a,VR(e.priority,function(){if(s.tag===13){var o=yn();o=Gd(o);var c=Fs(s,o);c!==null&&gn(c,s,o),Xm(s,o)}});return}}else if(a===3&&s.stateNode.current.memoizedState.isDehydrated){e.blockedOn=s.tag===3?s.stateNode.containerInfo:null;return}}}e.blockedOn=null}function wu(e){if(e.blockedOn!==null)return!1;for(var a=e.targetContainers;0<a.length;){var s=Jm(e.nativeEvent);if(s===null){s=e.nativeEvent;var o=new s.constructor(s.type,s);nh=o,s.target.dispatchEvent(o),nh=null}else return a=Ms(s),a!==null&&Kb(a),e.blockedOn=s,!1;a.shift()}return!0}function Zb(e,a,s){wu(e)&&s.delete(a)}function Ww(){tp=!1,lr!==null&&wu(lr)&&(lr=null),cr!==null&&wu(cr)&&(cr=null),ur!==null&&wu(ur)&&(ur=null),sl.forEach(Zb),il.forEach(Zb)}function Au(e,a){e.blockedOn===a&&(e.blockedOn=null,tp||(tp=!0,t.unstable_scheduleCallback(t.unstable_NormalPriority,Ww)))}var Cu=null;function Jb(e){Cu!==e&&(Cu=e,t.unstable_scheduleCallback(t.unstable_NormalPriority,function(){Cu===e&&(Cu=null);for(var a=0;a<e.length;a+=3){var s=e[a],o=e[a+1],c=e[a+2];if(typeof o!="function"){if(Wm(o||s)===null)continue;break}var d=Ms(s);d!==null&&(e.splice(a,3),a-=3,Wh(d,{pending:!0,data:c,method:s.method,action:o},o,c))}}))}function ll(e){function a(S){return Au(S,e)}lr!==null&&Au(lr,e),cr!==null&&Au(cr,e),ur!==null&&Au(ur,e),sl.forEach(a),il.forEach(a);for(var s=0;s<fr.length;s++){var o=fr[s];o.blockedOn===e&&(o.blockedOn=null)}for(;0<fr.length&&(s=fr[0],s.blockedOn===null);)Xb(s),s.blockedOn===null&&fr.shift();if(s=(e.ownerDocument||e).$$reactFormReplay,s!=null)for(o=0;o<s.length;o+=3){var c=s[o],d=s[o+1],y=c[tn]||null;if(typeof d=="function")y||Jb(s);else if(y){var _=null;if(d&&d.hasAttribute("formAction")){if(c=d,y=d[tn]||null)_=y.formAction;else if(Wm(c)!==null)continue}else _=y.action;typeof _=="function"?s[o+1]=_:(s.splice(o,3),o-=3),Jb(s)}}}function ep(e){this._internalRoot=e}xu.prototype.render=ep.prototype.render=function(e){var a=this._internalRoot;if(a===null)throw Error(i(409));var s=a.current,o=yn();Vb(s,o,e,a,null,null)},xu.prototype.unmount=ep.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var a=e.containerInfo;Vb(e.current,2,null,e,null,null),fu(),a[Os]=null}};function xu(e){this._internalRoot=e}xu.prototype.unstable_scheduleHydration=function(e){if(e){var a=p0();e={blockedOn:null,target:e,priority:a};for(var s=0;s<fr.length&&a!==0&&a<fr[s].priority;s++);fr.splice(s,0,e),s===0&&Xb(e)}};var Wb=n.version;if(Wb!=="19.1.0")throw Error(i(527,Wb,"19.1.0"));I.findDOMNode=function(e){var a=e._reactInternals;if(a===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=m(a),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var tA={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:C,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Du=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Du.isDisabled&&Du.supportsFiber)try{Or=Du.inject(tA),cn=Du}catch{}}return ul.createRoot=function(e,a){if(!l(e))throw Error(i(299));var s=!1,o="",c=p_,d=y_,y=g_,_=null;return a!=null&&(a.unstable_strictMode===!0&&(s=!0),a.identifierPrefix!==void 0&&(o=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(d=a.onCaughtError),a.onRecoverableError!==void 0&&(y=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(_=a.unstable_transitionCallbacks)),a=Ib(e,1,!1,null,null,s,o,c,d,y,_,null),e[Os]=a.current,Um(e),new ep(a)},ul.hydrateRoot=function(e,a,s){if(!l(e))throw Error(i(299));var o=!1,c="",d=p_,y=y_,_=g_,S=null,k=null;return s!=null&&(s.unstable_strictMode===!0&&(o=!0),s.identifierPrefix!==void 0&&(c=s.identifierPrefix),s.onUncaughtError!==void 0&&(d=s.onUncaughtError),s.onCaughtError!==void 0&&(y=s.onCaughtError),s.onRecoverableError!==void 0&&(_=s.onRecoverableError),s.unstable_transitionCallbacks!==void 0&&(S=s.unstable_transitionCallbacks),s.formState!==void 0&&(k=s.formState)),a=Ib(e,1,!0,a,s??null,o,c,d,y,_,S,k),a.context=Pb(null),s=a.current,o=yn(),o=Gd(o),c=Qa(o),c.callback=null,Ya(s,c,o),s=o,a.current.lanes=s,mo(a,s),ea(a),e[Os]=a.current,Um(e),new xu(a)},ul.version="19.1.0",ul}var cS;function uA(){if(cS)return ap.exports;cS=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(n){console.error(n)}}return t(),ap.exports=cA(),ap.exports}var fA=uA();function Y1(t){if(Array.isArray(t))return t.flatMap(g=>Y1(g));if(typeof t!="string")return[];const n=[];let r=0,i,l,u,f,h;const m=()=>{for(;r<t.length&&/\s/.test(t.charAt(r));)r+=1;return r<t.length},p=()=>(l=t.charAt(r),l!=="="&&l!==";"&&l!==",");for(;r<t.length;){for(i=r,h=!1;m();)if(l=t.charAt(r),l===","){for(u=r,r+=1,m(),f=r;r<t.length&&p();)r+=1;r<t.length&&t.charAt(r)==="="?(h=!0,r=f,n.push(t.slice(i,u)),i=r):r=u+1}else r+=1;(!h||r>=t.length)&&n.push(t.slice(i,t.length))}return n}function dA(t){return t instanceof Headers?new Headers(t):Array.isArray(t)?new Headers(t):typeof t=="object"?new Headers(t):new Headers}function hA(...t){return t.reduce((n,r)=>{const i=dA(r);for(const[l,u]of i.entries())l==="set-cookie"?Y1(u).forEach(h=>n.append("set-cookie",h)):n.set(l,u);return n},new Headers)}const bi=new WeakMap,Pu=new WeakMap,nf={current:[]};let lp=!1,El=0;const bl=new Set,ku=new Map;function X1(t){const n=Array.from(t).sort((r,i)=>r instanceof Si&&r.options.deps.includes(i)?1:i instanceof Si&&i.options.deps.includes(r)?-1:0);for(const r of n){if(nf.current.includes(r))continue;nf.current.push(r),r.recompute();const i=Pu.get(r);if(i)for(const l of i){const u=bi.get(l);u&&X1(u)}}}function mA(t){t.listeners.forEach(n=>n({prevVal:t.prevState,currentVal:t.state}))}function pA(t){t.listeners.forEach(n=>n({prevVal:t.prevState,currentVal:t.state}))}function Z1(t){if(El>0&&!ku.has(t)&&ku.set(t,t.prevState),bl.add(t),!(El>0)&&!lp)try{for(lp=!0;bl.size>0;){const n=Array.from(bl);bl.clear();for(const r of n){const i=ku.get(r)??r.prevState;r.prevState=i,mA(r)}for(const r of n){const i=bi.get(r);i&&(nf.current.push(r),X1(i))}for(const r of n){const i=bi.get(r);if(i)for(const l of i)pA(l)}}}finally{lp=!1,nf.current=[],ku.clear()}}function ci(t){El++;try{t()}finally{if(El--,El===0){const n=Array.from(bl)[0];n&&Z1(n)}}}class Np{constructor(n,r){this.listeners=new Set,this.subscribe=i=>{var l,u;this.listeners.add(i);const f=(u=(l=this.options)==null?void 0:l.onSubscribe)==null?void 0:u.call(l,i,this);return()=>{this.listeners.delete(i),f?.()}},this.setState=i=>{var l,u,f;this.prevState=this.state,this.state=(l=this.options)!=null&&l.updateFn?this.options.updateFn(this.prevState)(i):i(this.prevState),(f=(u=this.options)==null?void 0:u.onUpdate)==null||f.call(u),Z1(this)},this.prevState=n,this.state=n,this.options=r}}class Si{constructor(n){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{const r=[],i=[];for(const l of this.options.deps)r.push(l.prevState),i.push(l.state);return this.lastSeenDepValues=i,{prevDepVals:r,currDepVals:i,prevVal:this.prevState??void 0}},this.recompute=()=>{var r,i;this.prevState=this.state;const{prevDepVals:l,currDepVals:u,prevVal:f}=this.getDepVals();this.state=this.options.fn({prevDepVals:l,currDepVals:u,prevVal:f}),(i=(r=this.options).onUpdate)==null||i.call(r)},this.checkIfRecalculationNeededDeeply=()=>{for(const u of this.options.deps)u instanceof Si&&u.checkIfRecalculationNeededDeeply();let r=!1;const i=this.lastSeenDepValues,{currDepVals:l}=this.getDepVals();for(let u=0;u<l.length;u++)if(l[u]!==i[u]){r=!0;break}r&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const r of this._subscriptions)r()}),this.subscribe=r=>{var i,l;this.listeners.add(r);const u=(l=(i=this.options).onSubscribe)==null?void 0:l.call(i,r,this);return()=>{this.listeners.delete(r),u?.()}},this.options=n,this.state=n.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(n=this.options.deps){for(const r of n)if(r instanceof Si)r.registerOnGraph(),this.registerOnGraph(r.options.deps);else if(r instanceof Np){let i=bi.get(r);i||(i=new Set,bi.set(r,i)),i.add(this);let l=Pu.get(this);l||(l=new Set,Pu.set(this,l)),l.add(r)}}unregisterFromGraph(n=this.options.deps){for(const r of n)if(r instanceof Si)this.unregisterFromGraph(r.options.deps);else if(r instanceof Np){const i=bi.get(r);i&&i.delete(this);const l=Pu.get(this);l&&l.delete(r)}}}const mr="__TSR_index",uS="popstate",fS="beforeunload";function J1(t){let n=t.getLocation();const r=new Set,i=f=>{n=t.getLocation(),r.forEach(h=>h({location:n,action:f}))},l=f=>{t.notifyOnIndexChange??!0?i(f):n=t.getLocation()},u=async({task:f,navigateOpts:h,...m})=>{var p,g;if(h?.ignoreBlocker??!1){f();return}const b=((p=t.getBlockers)==null?void 0:p.call(t))??[],O=m.type==="PUSH"||m.type==="REPLACE";if(typeof document<"u"&&b.length&&O)for(const R of b){const A=xl(m.path,m.state);if(await R.blockerFn({currentLocation:n,nextLocation:A,action:m.type})){(g=t.onBlocked)==null||g.call(t);return}}f()};return{get location(){return n},get length(){return t.getLength()},subscribers:r,subscribe:f=>(r.add(f),()=>{r.delete(f)}),push:(f,h,m)=>{const p=n.state[mr];h=zp(p+1,h),u({task:()=>{t.pushState(f,h),i({type:"PUSH"})},navigateOpts:m,type:"PUSH",path:f,state:h})},replace:(f,h,m)=>{const p=n.state[mr];h=zp(p,h),u({task:()=>{t.replaceState(f,h),i({type:"REPLACE"})},navigateOpts:m,type:"REPLACE",path:f,state:h})},go:(f,h)=>{u({task:()=>{t.go(f),l({type:"GO",index:f})},navigateOpts:h,type:"GO"})},back:f=>{u({task:()=>{t.back(f?.ignoreBlocker??!1),l({type:"BACK"})},navigateOpts:f,type:"BACK"})},forward:f=>{u({task:()=>{t.forward(f?.ignoreBlocker??!1),l({type:"FORWARD"})},navigateOpts:f,type:"FORWARD"})},canGoBack:()=>n.state[mr]!==0,createHref:f=>t.createHref(f),block:f=>{var h;if(!t.setBlockers)return()=>{};const m=((h=t.getBlockers)==null?void 0:h.call(t))??[];return t.setBlockers([...m,f]),()=>{var p,g;const v=((p=t.getBlockers)==null?void 0:p.call(t))??[];(g=t.setBlockers)==null||g.call(t,v.filter(b=>b!==f))}},flush:()=>{var f;return(f=t.flush)==null?void 0:f.call(t)},destroy:()=>{var f;return(f=t.destroy)==null?void 0:f.call(t)},notify:i}}function zp(t,n){return n||(n={}),{...n,key:Fy(),[mr]:t}}function yA(t){var n;const r=typeof document<"u"?window:void 0,i=r.history.pushState,l=r.history.replaceState;let u=[];const f=()=>u,h=L=>u=L,m=L=>L,p=()=>xl(`${r.location.pathname}${r.location.search}${r.location.hash}`,r.history.state);(n=r.history.state)!=null&&n.key||r.history.replaceState({[mr]:0,key:Fy()},"");let g=p(),v,b=!1,O=!1,R=!1,A=!1;const j=()=>g;let U,B;const q=()=>{U&&(N._ignoreSubscribers=!0,(U.isPush?r.history.pushState:r.history.replaceState)(U.state,"",U.href),N._ignoreSubscribers=!1,U=void 0,B=void 0,v=void 0)},G=(L,rt,tt)=>{const Y=m(rt);B||(v=g),g=xl(rt,tt),U={href:Y,state:tt,isPush:U?.isPush||L==="push"},B||(B=Promise.resolve().then(()=>q()))},Z=L=>{g=p(),N.notify({type:L})},K=async()=>{if(O){O=!1;return}const L=p(),rt=L.state[mr]-g.state[mr],tt=rt===1,Y=rt===-1,it=!tt&&!Y||b;b=!1;const St=it?"GO":Y?"BACK":"FORWARD",ut=it?{type:"GO",index:rt}:{type:Y?"BACK":"FORWARD"};if(R)R=!1;else{const C=f();if(typeof document<"u"&&C.length){for(const I of C)if(await I.blockerFn({currentLocation:g,nextLocation:L,action:St})){O=!0,r.history.go(1),N.notify(ut);return}}}g=p(),N.notify(ut)},Q=L=>{if(A){A=!1;return}let rt=!1;const tt=f();if(typeof document<"u"&&tt.length)for(const Y of tt){const it=Y.enableBeforeUnload??!0;if(it===!0){rt=!0;break}if(typeof it=="function"&&it()===!0){rt=!0;break}}if(rt)return L.preventDefault(),L.returnValue=""},N=J1({getLocation:j,getLength:()=>r.history.length,pushState:(L,rt)=>G("push",L,rt),replaceState:(L,rt)=>G("replace",L,rt),back:L=>(L&&(R=!0),A=!0,r.history.back()),forward:L=>{L&&(R=!0),A=!0,r.history.forward()},go:L=>{b=!0,r.history.go(L)},createHref:L=>m(L),flush:q,destroy:()=>{r.history.pushState=i,r.history.replaceState=l,r.removeEventListener(fS,Q,{capture:!0}),r.removeEventListener(uS,K)},onBlocked:()=>{v&&g!==v&&(g=v)},getBlockers:f,setBlockers:h,notifyOnIndexChange:!1});return r.addEventListener(fS,Q,{capture:!0}),r.addEventListener(uS,K),r.history.pushState=function(...L){const rt=i.apply(r.history,L);return N._ignoreSubscribers||Z("PUSH"),rt},r.history.replaceState=function(...L){const rt=l.apply(r.history,L);return N._ignoreSubscribers||Z("REPLACE"),rt},N}function gA(t={initialEntries:["/"]}){const n=t.initialEntries;let r=t.initialIndex?Math.min(Math.max(t.initialIndex,0),n.length-1):n.length-1;const i=n.map((u,f)=>zp(f,void 0));return J1({getLocation:()=>xl(n[r],i[r]),getLength:()=>n.length,pushState:(u,f)=>{r<n.length-1&&(n.splice(r+1),i.splice(r+1)),i.push(f),n.push(u),r=Math.max(n.length-1,0)},replaceState:(u,f)=>{i[r]=f,n[r]=u},back:()=>{r=Math.max(r-1,0)},forward:()=>{r=Math.min(r+1,n.length-1)},go:u=>{r=Math.min(Math.max(r+u,0),n.length-1)},createHref:u=>u})}function xl(t,n){const r=t.indexOf("#"),i=t.indexOf("?");return{href:t,pathname:t.substring(0,r>0?i>0?Math.min(r,i):r:i>0?i:t.length),hash:r>-1?t.substring(r):"",search:i>-1?t.slice(i,r===-1?void 0:r):"",state:n||{[mr]:0,key:Fy()}}}function Fy(){return(Math.random()+1).toString(36).substring(7)}var vA="Invariant failed";function Nn(t,n){if(!t)throw new Error(vA)}function mi(t){return t[t.length-1]}function _A(t){return typeof t=="function"}function Kr(t,n){return _A(t)?t(n):t}function Lp(t,n){return n.reduce((r,i)=>(r[i]=t[i],r),{})}function Dn(t,n){if(t===n)return t;const r=n,i=hS(t)&&hS(r);if(i||sa(t)&&sa(r)){const l=i?t:Object.keys(t),u=l.length,f=i?r:Object.keys(r),h=f.length,m=i?[]:{};let p=0;for(let g=0;g<h;g++){const v=i?g:f[g];(!i&&l.includes(v)||i)&&t[v]===void 0&&r[v]===void 0?(m[v]=void 0,p++):(m[v]=Dn(t[v],r[v]),m[v]===t[v]&&t[v]!==void 0&&p++)}return u===h&&p===u?t:m}return r}function sa(t){if(!dS(t))return!1;const n=t.constructor;if(typeof n>"u")return!0;const r=n.prototype;return!(!dS(r)||!r.hasOwnProperty("isPrototypeOf"))}function dS(t){return Object.prototype.toString.call(t)==="[object Object]"}function hS(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function mS(t,n){let r=Object.keys(t);return n&&(r=r.filter(i=>t[i]!==void 0)),r}function Ei(t,n,r){if(t===n)return!0;if(typeof t!=typeof n)return!1;if(sa(t)&&sa(n)){const i=r?.ignoreUndefined??!0,l=mS(t,i),u=mS(n,i);return!r?.partial&&l.length!==u.length?!1:u.every(f=>Ei(t[f],n[f],r))}return Array.isArray(t)&&Array.isArray(n)?t.length!==n.length?!1:!t.some((i,l)=>!Ei(i,n[l],r)):!1}function hi(t){let n,r;const i=new Promise((l,u)=>{n=l,r=u});return i.status="pending",i.resolve=l=>{i.status="resolved",i.value=l,n(l),t?.(l)},i.reject=l=>{i.status="rejected",r(l)},i}function bA(t){return/%[0-9A-Fa-f]{2}/.test(t)}function za(t){return qf(t.filter(n=>n!==void 0).join("/"))}function qf(t){return t.replace(/\/{2,}/g,"/")}function jy(t){return t==="/"?t:t.replace(/^\/{1,}/,"")}function Qr(t){return t==="/"?t:t.replace(/\/{1,}$/,"")}function SA(t){return Qr(jy(t))}function af(t,n){return t?.endsWith("/")&&t!=="/"&&t!==`${n}/`?t.slice(0,-1):t}function EA(t,n,r){return af(t,r)===af(n,r)}function TA({basepath:t,base:n,to:r,trailingSlash:i="never",caseSensitive:l}){var u,f;n=rf(t,n,l),r=rf(t,r,l);let h=wi(n);const m=wi(r);h.length>1&&((u=mi(h))==null?void 0:u.value)==="/"&&h.pop(),m.forEach((g,v)=>{g.value==="/"?v?v===m.length-1&&h.push(g):h=[g]:g.value===".."?h.pop():g.value==="."||h.push(g)}),h.length>1&&(((f=mi(h))==null?void 0:f.value)==="/"?i==="never"&&h.pop():i==="always"&&h.push({type:"pathname",value:"/"}));const p=za([t,...h.map(g=>g.value)]);return qf(p)}function wi(t){if(!t)return[];t=qf(t);const n=[];if(t.slice(0,1)==="/"&&(t=t.substring(1),n.push({type:"pathname",value:"/"})),!t)return n;const r=t.split("/").filter(Boolean);return n.push(...r.map(i=>i==="$"||i==="*"?{type:"wildcard",value:i}:i.charAt(0)==="$"?{type:"param",value:i}:{type:"pathname",value:i.includes("%25")?i.split("%25").map(l=>decodeURI(l)).join("%25"):decodeURI(i)})),t.slice(-1)==="/"&&(t=t.substring(1),n.push({type:"pathname",value:"/"})),n}function Nu({path:t,params:n,leaveWildcards:r,leaveParams:i,decodeCharMap:l}){const u=wi(t);function f(g){const v=n[g],b=typeof v=="string";return["*","_splat"].includes(g)?b?encodeURI(v):v:b?OA(v,l):v}let h=!1;const m={},p=za(u.map(g=>{if(g.type==="wildcard"){m._splat=n._splat;const v=f("_splat");return r?`${g.value}${v??""}`:v}if(g.type==="param"){const v=g.value.substring(1);if(!h&&!(v in n)&&(h=!0),m[v]=n[v],i){const b=f(g.value);return`${g.value}${b??""}`}return f(v)??"undefined"}return g.value}));return{usedParams:m,interpolatedPath:p,isMissingParams:h}}function OA(t,n){let r=encodeURIComponent(t);if(n)for(const[i,l]of n)r=r.replaceAll(i,l);return r}function zu(t,n,r){const i=RA(t,n,r);if(!(r.to&&!i))return i??{}}function rf(t,n,r=!1){const i=r?t:t.toLowerCase(),l=r?n:n.toLowerCase();switch(!0){case i==="/":return n;case l===i:return"";case n.length<t.length:return n;case l[i.length]!=="/":return n;case l.startsWith(i):return n.slice(t.length);default:return n}}function RA(t,n,r){if(t!=="/"&&!n.startsWith(t))return;n=rf(t,n,r.caseSensitive);const i=rf(t,`${r.to??"$"}`,r.caseSensitive),l=wi(n),u=wi(i);n.startsWith("/")||l.unshift({type:"pathname",value:"/"}),i.startsWith("/")||u.unshift({type:"pathname",value:"/"});const f={};return(()=>{for(let m=0;m<Math.max(l.length,u.length);m++){const p=l[m],g=u[m],v=m>=l.length-1,b=m>=u.length-1;if(g){if(g.type==="wildcard"){const O=decodeURI(za(l.slice(m).map(R=>R.value)));return f["*"]=O,f._splat=O,!0}if(g.type==="pathname"){if(g.value==="/"&&!p?.value)return!0;if(p){if(r.caseSensitive){if(g.value!==p.value)return!1}else if(g.value.toLowerCase()!==p.value.toLowerCase())return!1}}if(!p)return!1;if(g.type==="param"){if(p.value==="/")return!1;p.value.charAt(0)!=="$"&&(f[g.value.substring(1)]=decodeURIComponent(p.value))}}if(!v&&b)return f["**"]=za(l.slice(m+1).map(O=>O.value)),!!r.fuzzy&&g?.value!=="/"}return!0})()?f:void 0}function sn(t){return!!t?.isNotFound}function MA(){try{if(typeof window<"u"&&typeof window.sessionStorage=="object")return window.sessionStorage}catch{return}}const sf="tsr-scroll-restoration-v1_3",wA=(t,n)=>{let r;return(...i)=>{r||(r=setTimeout(()=>{t(...i),r=null},n))}};function AA(){const t=MA();if(!t)return;const n=t.getItem(sf);let r=n?JSON.parse(n):{};return{state:r,set:i=>(r=Kr(i,r)||r,t.setItem(sf,JSON.stringify(r)))}}const cp=AA(),$p=t=>t.state.key||t.href;function CA(t){const n=[];let r;for(;r=t.parentNode;)n.unshift(`${t.tagName}:nth-child(${[].indexOf.call(r.children,t)+1})`),t=r;return`${n.join(" > ")}`.toLowerCase()}let of=!1;function W1(t,n,r,i,l){var u;let f;try{f=JSON.parse(sessionStorage.getItem(t)||"{}")}catch(p){console.error(p);return}const h=n||((u=window.history.state)==null?void 0:u.key),m=f[h];of=!0,(()=>{if(i&&m){for(const g in m){const v=m[g];if(g==="window")window.scrollTo({top:v.scrollY,left:v.scrollX,behavior:r});else if(g){const b=document.querySelector(g);b&&(b.scrollLeft=v.scrollX,b.scrollTop=v.scrollY)}}return}const p=window.location.hash.split("#")[1];if(p){const g=(window.history.state||{}).__hashScrollIntoViewOptions??!0;if(g){const v=document.getElementById(p);v&&v.scrollIntoView(g)}return}["window",...l?.filter(g=>g!=="window")??[]].forEach(g=>{const v=g==="window"?window:document.querySelector(g);v&&v.scrollTo({top:0,left:0,behavior:r})})})(),of=!1}function xA(t,n){if(cp===void 0||((t.options.scrollRestoration??!1)&&(t.isScrollRestoring=!0),typeof document>"u"||t.isScrollRestorationSetup))return;t.isScrollRestorationSetup=!0,of=!1;const i=t.options.getScrollRestorationKey||$p;window.history.scrollRestoration="manual";const l=u=>{if(of||!t.isScrollRestoring)return;let f="";if(u.target===document||u.target===window)f="window";else{const m=u.target.getAttribute("data-scroll-restoration-id");m?f=`[data-scroll-restoration-id="${m}"]`:f=CA(u.target)}const h=i(t.state.location);cp.set(m=>{const p=m[h]=m[h]||{},g=p[f]=p[f]||{};if(f==="window")g.scrollX=window.scrollX||0,g.scrollY=window.scrollY||0;else if(f){const v=document.querySelector(f);v&&(g.scrollX=v.scrollLeft||0,g.scrollY=v.scrollTop||0)}return m})};typeof document<"u"&&document.addEventListener("scroll",wA(l,100),!0),t.subscribe("onRendered",u=>{const f=i(u.toLocation);if(!t.resetNextScroll){t.resetNextScroll=!0;return}W1(sf,f,t.options.scrollRestorationBehavior||void 0,t.isScrollRestoring||void 0,t.options.scrollToTopSelectors||void 0),t.isScrollRestoring&&cp.set(h=>(h[f]=h[f]||{},h))})}function DA(t){if(typeof document<"u"&&document.querySelector){const n=t.state.location.state.__hashScrollIntoViewOptions??!0;if(n&&t.state.location.hash!==""){const r=document.getElementById(t.state.location.hash);r&&r.scrollIntoView(n)}}}function tE(t,n){const r=Object.entries(t).flatMap(([l,u])=>Array.isArray(u)?u.map(f=>[l,String(f)]):[[l,String(u)]]);return""+new URLSearchParams(r).toString()}function up(t){if(!t)return"";const n=bA(t)?decodeURIComponent(t):decodeURIComponent(encodeURIComponent(t));return n==="false"?!1:n==="true"?!0:+n*0===0&&+n+""===n?+n:n}function kA(t,n){const r=t;return[...new URLSearchParams(r).entries()].reduce((u,[f,h])=>{const m=u[f];return m==null?u[f]=up(h):u[f]=Array.isArray(m)?[...m,up(h)]:[m,up(h)],u},{})}const NA=LA(JSON.parse),zA=$A(JSON.stringify,JSON.parse);function LA(t){return n=>{n.substring(0,1)==="?"&&(n=n.substring(1));const r=kA(n);for(const i in r){const l=r[i];if(typeof l=="string")try{r[i]=t(l)}catch{}}return r}}function $A(t,n){function r(i){if(typeof i=="object"&&i!==null)try{return t(i)}catch{}else if(typeof i=="string"&&typeof n=="function")try{return n(i),t(i)}catch{}return i}return i=>{i={...i},Object.keys(i).forEach(u=>{const f=i[u];typeof f>"u"||f===void 0?delete i[u]:i[u]=r(f)});const l=tE(i).toString();return l?`?${l}`:""}}const qn="__root__";function In(t){return!!t?.isRedirect}function fp(t){return!!t?.isRedirect&&t.href}function UA(t){return t instanceof Error?{name:t.name,message:t.message}:{data:t}}function Xr(t){const n=t.resolvedLocation,r=t.location,i=n?.pathname!==r.pathname,l=n?.href!==r.href,u=n?.hash!==r.hash;return{fromLocation:n,toLocation:r,pathChanged:i,hrefChanged:l,hashChanged:u}}class FA{constructor(n){this.tempLocationKey=`${Math.round(Math.random()*1e7)}`,this.resetNextScroll=!0,this.shouldViewTransition=void 0,this.isViewTransitionTypesSupported=void 0,this.subscribers=new Set,this.isScrollRestoring=!1,this.isScrollRestorationSetup=!1,this.startTransition=r=>r(),this.update=r=>{var i;r.notFoundRoute&&console.warn("The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.");const l=this.options;this.options={...this.options,...r},this.isServer=this.options.isServer??typeof document>"u",this.pathParamsDecodeCharMap=this.options.pathParamsAllowedCharacters?new Map(this.options.pathParamsAllowedCharacters.map(u=>[encodeURIComponent(u),u])):void 0,(!this.basepath||r.basepath&&r.basepath!==l.basepath)&&(r.basepath===void 0||r.basepath===""||r.basepath==="/"?this.basepath="/":this.basepath=`/${SA(r.basepath)}`),(!this.history||this.options.history&&this.options.history!==this.history)&&(this.history=this.options.history??(this.isServer?gA({initialEntries:[this.basepath||"/"]}):yA()),this.latestLocation=this.parseLocation()),this.options.routeTree!==this.routeTree&&(this.routeTree=this.options.routeTree,this.buildRouteTree()),this.__store||(this.__store=new Np(BA(this.latestLocation),{onUpdate:()=>{this.__store.state={...this.state,cachedMatches:this.state.cachedMatches.filter(u=>!["redirected"].includes(u.status))}}}),xA(this)),typeof window<"u"&&"CSS"in window&&typeof((i=window.CSS)==null?void 0:i.supports)=="function"&&(this.isViewTransitionTypesSupported=window.CSS.supports("selector(:active-view-transition-type(a)"))},this.buildRouteTree=()=>{this.routesById={},this.routesByPath={};const r=this.options.notFoundRoute;r&&(r.init({originalIndex:99999999999,defaultSsr:this.options.defaultSsr}),this.routesById[r.id]=r);const i=f=>{f.forEach((h,m)=>{h.init({originalIndex:m,defaultSsr:this.options.defaultSsr});const p=this.routesById[h.id];if(Nn(!p,`Duplicate routes found with id: ${String(h.id)}`),this.routesById[h.id]=h,!h.isRoot&&h.path){const v=Qr(h.fullPath);(!this.routesByPath[v]||h.fullPath.endsWith("/"))&&(this.routesByPath[v]=h)}const g=h.children;g?.length&&i(g)})};i([this.routeTree]);const l=[];Object.values(this.routesById).forEach((f,h)=>{var m;if(f.isRoot||!f.path)return;const p=jy(f.fullPath),g=wi(p);for(;g.length>1&&((m=g[0])==null?void 0:m.value)==="/";)g.shift();const v=g.map(b=>b.value==="/"?.75:b.type==="param"?.5:b.type==="wildcard"?.25:1);l.push({child:f,trimmed:p,parsed:g,index:h,scores:v})}),this.flatRoutes=l.sort((f,h)=>{const m=Math.min(f.scores.length,h.scores.length);for(let p=0;p<m;p++)if(f.scores[p]!==h.scores[p])return h.scores[p]-f.scores[p];if(f.scores.length!==h.scores.length)return h.scores.length-f.scores.length;for(let p=0;p<m;p++)if(f.parsed[p].value!==h.parsed[p].value)return f.parsed[p].value>h.parsed[p].value?1:-1;return f.index-h.index}).map((f,h)=>(f.child.rank=h,f.child))},this.subscribe=(r,i)=>{const l={eventType:r,fn:i};return this.subscribers.add(l),()=>{this.subscribers.delete(l)}},this.emit=r=>{this.subscribers.forEach(i=>{i.eventType===r.type&&i.fn(r)})},this.parseLocation=(r,i)=>{const l=({pathname:m,search:p,hash:g,state:v})=>{const b=this.options.parseSearch(p),O=this.options.stringifySearch(b);return{pathname:m,searchStr:O,search:Dn(r?.search,b),hash:g.split("#").reverse()[0]??"",href:`${m}${O}${g}`,state:Dn(r?.state,v)}},u=l(i??this.history.location),{__tempLocation:f,__tempKey:h}=u.state;if(f&&(!h||h===this.tempLocationKey)){const m=l(f);return m.state.key=u.state.key,delete m.state.__tempLocation,{...m,maskedLocation:u}}return u},this.resolvePathWithBase=(r,i)=>TA({basepath:this.basepath,base:r,to:qf(i),trailingSlash:this.options.trailingSlash,caseSensitive:this.options.caseSensitive}),this.matchRoutes=(r,i,l)=>typeof r=="string"?this.matchRoutesInternal({pathname:r,search:i},l):this.matchRoutesInternal(r,i),this.getMatchedRoutes=(r,i)=>{let l={};const u=Qr(r.pathname),f=g=>zu(this.basepath,u,{to:g.fullPath,caseSensitive:g.options.caseSensitive??this.options.caseSensitive,fuzzy:!0});let h=i?.to!==void 0?this.routesByPath[i.to]:void 0;h?l=f(h):h=this.flatRoutes.find(g=>{const v=f(g);return v?(l=v,!0):!1});let m=h||this.routesById[qn];const p=[m];for(;m.parentRoute;)m=m.parentRoute,p.unshift(m);return{matchedRoutes:p,routeParams:l,foundRoute:h}},this.cancelMatch=r=>{const i=this.getMatch(r);i&&(i.abortController.abort(),clearTimeout(i.pendingTimeout))},this.cancelMatches=()=>{var r;(r=this.state.pendingMatches)==null||r.forEach(i=>{this.cancelMatch(i.id)})},this.buildLocation=r=>{const i=(u={},f)=>{var h,m,p,g,v,b,O;const R=u._fromLocation?this.matchRoutes(u._fromLocation,{_buildLocation:!0}):this.state.matches,A=u.from!=null?R.find(Y=>zu(this.basepath,Qr(Y.pathname),{to:u.from,caseSensitive:!1,fuzzy:!1})):void 0,j=A?.pathname||this.latestLocation.pathname;Nn(u.from==null||A!=null,"Could not find match for from: "+u.from);const U=(h=this.state.pendingMatches)!=null&&h.length?(m=mi(this.state.pendingMatches))==null?void 0:m.search:((p=mi(R))==null?void 0:p.search)||this.latestLocation.search,B=f?.matchedRoutes.filter(Y=>R.find(it=>it.routeId===Y.id));let q;if(u.to){const Y=A?.fullPath||((g=mi(R))==null?void 0:g.fullPath)||this.latestLocation.pathname;q=this.resolvePathWithBase(Y,`${u.to}`)}else{const Y=this.routesById[(v=B?.find(it=>{const St=Nu({path:it.fullPath,params:f?.routeParams??{},decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath;return za([this.basepath,St])===j}))==null?void 0:v.id];q=this.resolvePathWithBase(j,Y?.to??j)}const G={...(b=mi(R))==null?void 0:b.params};let Z=(u.params??!0)===!0?G:{...G,...Kr(u.params,G)};Object.keys(Z).length>0&&f?.matchedRoutes.map(Y=>{var it;return((it=Y.options.params)==null?void 0:it.stringify)??Y.options.stringifyParams}).filter(Boolean).forEach(Y=>{Z={...Z,...Y(Z)}}),q=Nu({path:q,params:Z??{},leaveWildcards:!1,leaveParams:r.leaveParams,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath;let K=U;if(r._includeValidateSearch&&((O=this.options.search)!=null&&O.strict)){let Y={};f?.matchedRoutes.forEach(it=>{try{it.options.validateSearch&&(Y={...Y,...dp(it.options.validateSearch,{...Y,...K})??{}})}catch{}}),K=Y}K=(Y=>{const it=f?.matchedRoutes.reduce((C,I)=>{var st;const bt=[];if("search"in I.options)(st=I.options.search)!=null&&st.middlewares&&bt.push(...I.options.search.middlewares);else if(I.options.preSearchFilters||I.options.postSearchFilters){const T=({search:F,next:nt})=>{let J=F;"preSearchFilters"in I.options&&I.options.preSearchFilters&&(J=I.options.preSearchFilters.reduce((ft,lt)=>lt(ft),F));const et=nt(J);return"postSearchFilters"in I.options&&I.options.postSearchFilters?I.options.postSearchFilters.reduce((ft,lt)=>lt(ft),et):et};bt.push(T)}if(r._includeValidateSearch&&I.options.validateSearch){const T=({search:F,next:nt})=>{const J=nt(F);try{return{...J,...dp(I.options.validateSearch,J)??{}}}catch{return J}};bt.push(T)}return C.concat(bt)},[])??[],St=({search:C})=>u.search?u.search===!0?C:Kr(u.search,C):{};it.push(St);const ut=(C,I)=>{if(C>=it.length)return I;const st=it[C];return st({search:I,next:T=>ut(C+1,T)})};return ut(0,Y)})(K),K=Dn(U,K);const N=this.options.stringifySearch(K),L=u.hash===!0?this.latestLocation.hash:u.hash?Kr(u.hash,this.latestLocation.hash):void 0,rt=L?`#${L}`:"";let tt=u.state===!0?this.latestLocation.state:u.state?Kr(u.state,this.latestLocation.state):{};return tt=Dn(this.latestLocation.state,tt),{pathname:q,search:K,searchStr:N,state:tt,hash:L??"",href:`${q}${N}${rt}`,unmaskOnReload:u.unmaskOnReload}},l=(u={},f)=>{var h;const m=i(u);let p=f?i(f):void 0;if(!p){let b={};const O=(h=this.options.routeMasks)==null?void 0:h.find(R=>{const A=zu(this.basepath,m.pathname,{to:R.from,caseSensitive:!1,fuzzy:!1});return A?(b=A,!0):!1});if(O){const{from:R,...A}=O;f={...Lp(r,["from"]),...A,params:b},p=i(f)}}const g=this.getMatchedRoutes(m,u),v=i(u,g);if(p){const b=this.getMatchedRoutes(p,f),O=i(f,b);v.maskedLocation=O}return v};return r.mask?l(r,{...Lp(r,["from"]),...r.mask}):l(r)},this.commitLocation=({viewTransition:r,ignoreBlocker:i,...l})=>{const u=()=>{const m=["key","__TSR_index","__hashScrollIntoViewOptions"];m.forEach(g=>{l.state[g]=this.latestLocation.state[g]});const p=Ei(l.state,this.latestLocation.state);return m.forEach(g=>{delete l.state[g]}),p},f=this.latestLocation.href===l.href,h=this.commitLocationPromise;if(this.commitLocationPromise=hi(()=>{h?.resolve()}),f&&u())this.load();else{let{maskedLocation:m,hashScrollIntoView:p,...g}=l;m&&(g={...m,state:{...m.state,__tempKey:void 0,__tempLocation:{...g,search:g.searchStr,state:{...g.state,__tempKey:void 0,__tempLocation:void 0,key:void 0}}}},(g.unmaskOnReload??this.options.unmaskOnReload??!1)&&(g.state.__tempKey=this.tempLocationKey)),g.state.__hashScrollIntoViewOptions=p??this.options.defaultHashScrollIntoView??!0,this.shouldViewTransition=r,this.history[l.replace?"replace":"push"](g.href,g.state,{ignoreBlocker:i})}return this.resetNextScroll=l.resetScroll??!0,this.history.subscribers.size||this.load(),this.commitLocationPromise},this.buildAndCommitLocation=({replace:r,resetScroll:i,hashScrollIntoView:l,viewTransition:u,ignoreBlocker:f,href:h,...m}={})=>{if(h){const g=this.history.location.state.__TSR_index,v=xl(h,{__TSR_index:r?g:g+1});m.to=v.pathname,m.search=this.options.parseSearch(v.search),m.hash=v.hash.slice(1)}const p=this.buildLocation({...m,_includeValidateSearch:!0});return this.commitLocation({...p,viewTransition:u,replace:r,resetScroll:i,hashScrollIntoView:l,ignoreBlocker:f})},this.navigate=({to:r,reloadDocument:i,href:l,...u})=>{if(i){if(!l){const f=this.buildLocation({to:r,...u});l=this.history.createHref(f.href)}u.replace?window.location.replace(l):window.location.href=l;return}return this.buildAndCommitLocation({...u,href:l,to:r})},this.load=async r=>{this.latestLocation=this.parseLocation(this.latestLocation);let i,l,u;for(u=new Promise(f=>{this.startTransition(async()=>{var h;try{const m=this.latestLocation,p=this.state.resolvedLocation;this.cancelMatches();let g;ci(()=>{g=this.matchRoutes(m),this.__store.setState(v=>({...v,status:"pending",isLoading:!0,location:m,pendingMatches:g,cachedMatches:v.cachedMatches.filter(b=>!g.find(O=>O.id===b.id))}))}),this.state.redirect||this.emit({type:"onBeforeNavigate",...Xr({resolvedLocation:p,location:m})}),this.emit({type:"onBeforeLoad",...Xr({resolvedLocation:p,location:m})}),await this.loadMatches({sync:r?.sync,matches:g,location:m,onReady:async()=>{this.startViewTransition(async()=>{let v,b,O;ci(()=>{this.__store.setState(R=>{const A=R.matches,j=R.pendingMatches||R.matches;return v=A.filter(U=>!j.find(B=>B.id===U.id)),b=j.filter(U=>!A.find(B=>B.id===U.id)),O=A.filter(U=>j.find(B=>B.id===U.id)),{...R,isLoading:!1,loadedAt:Date.now(),matches:j,pendingMatches:void 0,cachedMatches:[...R.cachedMatches,...v.filter(U=>U.status!=="error")]}}),this.clearExpiredCache()}),[[v,"onLeave"],[b,"onEnter"],[O,"onStay"]].forEach(([R,A])=>{R.forEach(j=>{var U,B;(B=(U=this.looseRoutesById[j.routeId].options)[A])==null||B.call(U,j)})})})}})}catch(m){fp(m)?(i=m,this.isServer||this.navigate({...i,replace:!0,ignoreBlocker:!0})):sn(m)&&(l=m),this.__store.setState(p=>({...p,statusCode:i?i.statusCode:l?404:p.matches.some(g=>g.status==="error")?500:200,redirect:i}))}this.latestLoadPromise===u&&((h=this.commitLocationPromise)==null||h.resolve(),this.latestLoadPromise=void 0,this.commitLocationPromise=void 0),f()})}),this.latestLoadPromise=u,await u;this.latestLoadPromise&&u!==this.latestLoadPromise;)await this.latestLoadPromise;this.hasNotFoundMatch()&&this.__store.setState(f=>({...f,statusCode:404}))},this.startViewTransition=r=>{const i=this.shouldViewTransition??this.options.defaultViewTransition;if(delete this.shouldViewTransition,i&&typeof document<"u"&&"startViewTransition"in document&&typeof document.startViewTransition=="function"){let l;if(typeof i=="object"&&this.isViewTransitionTypesSupported){const u=this.latestLocation,f=this.state.resolvedLocation,h=typeof i.types=="function"?i.types(Xr({resolvedLocation:f,location:u})):i.types;l={update:r,types:h}}else l=r;document.startViewTransition(l)}else r()},this.updateMatch=(r,i)=>{var l;let u;const f=(l=this.state.pendingMatches)==null?void 0:l.find(g=>g.id===r),h=this.state.matches.find(g=>g.id===r),m=this.state.cachedMatches.find(g=>g.id===r),p=f?"pendingMatches":h?"matches":m?"cachedMatches":"";return p&&this.__store.setState(g=>{var v;return{...g,[p]:(v=g[p])==null?void 0:v.map(b=>b.id===r?u=i(b):b)}}),u},this.getMatch=r=>[...this.state.cachedMatches,...this.state.pendingMatches??[],...this.state.matches].find(i=>i.id===r),this.loadMatches=async({location:r,matches:i,preload:l,onReady:u,updateMatch:f=this.updateMatch,sync:h})=>{let m,p=!1;const g=async()=>{p||(p=!0,await u?.())},v=O=>!!(l&&!this.state.matches.find(R=>R.id===O)),b=(O,R)=>{var A,j,U,B;if(fp(R)&&!R.reloadDocument)throw R;if(In(R)||sn(R)){if(f(O.id,q=>({...q,status:In(R)?"redirected":sn(R)?"notFound":"error",isFetching:!1,error:R,beforeLoadPromise:void 0,loaderPromise:void 0})),R.routeId||(R.routeId=O.routeId),(A=O.beforeLoadPromise)==null||A.resolve(),(j=O.loaderPromise)==null||j.resolve(),(U=O.loadPromise)==null||U.resolve(),In(R))throw p=!0,R=this.resolveRedirect({...R,_fromLocation:r}),R;if(sn(R))throw this._handleNotFound(i,R,{updateMatch:f}),(B=this.serverSsr)==null||B.onMatchSettled({router:this,match:this.getMatch(O.id)}),R}};try{await new Promise((O,R)=>{(async()=>{var A,j,U,B;try{const q=(K,Q,N)=>{var L,rt;const{id:tt,routeId:Y}=i[K],it=this.looseRoutesById[Y];if(Q instanceof Promise)throw Q;Q.routerCode=N,m=m??K,b(this.getMatch(tt),Q);try{(rt=(L=it.options).onError)==null||rt.call(L,Q)}catch(St){Q=St,b(this.getMatch(tt),Q)}f(tt,St=>{var ut,C;return(ut=St.beforeLoadPromise)==null||ut.resolve(),(C=St.loadPromise)==null||C.resolve(),{...St,error:Q,status:"error",isFetching:!1,updatedAt:Date.now(),abortController:new AbortController,beforeLoadPromise:void 0}})};for(const[K,{id:Q,routeId:N}]of i.entries()){const L=this.getMatch(Q),rt=(A=i[K-1])==null?void 0:A.id,tt=this.looseRoutesById[N],Y=tt.options.pendingMs??this.options.defaultPendingMs,it=!!(u&&!this.isServer&&!v(Q)&&(tt.options.loader||tt.options.beforeLoad||pS(tt))&&typeof Y=="number"&&Y!==1/0&&(tt.options.pendingComponent??((j=this.options)==null?void 0:j.defaultPendingComponent)));let St=!0;if((L.beforeLoadPromise||L.loaderPromise)&&(it&&setTimeout(()=>{try{g()}catch{}},Y),await L.beforeLoadPromise,St=this.getMatch(Q).status!=="success"),St){try{f(Q,Dt=>{const dt=Dt.loadPromise;return{...Dt,loadPromise:hi(()=>{dt?.resolve()}),beforeLoadPromise:hi()}});const ut=new AbortController;let C;it&&(C=setTimeout(()=>{try{g()}catch{}},Y));const{paramsError:I,searchError:st}=this.getMatch(Q);I&&q(K,I,"PARSE_PARAMS"),st&&q(K,st,"VALIDATE_SEARCH");const bt=()=>rt?this.getMatch(rt).context:this.options.context??{};f(Q,Dt=>({...Dt,isFetching:"beforeLoad",fetchCount:Dt.fetchCount+1,abortController:ut,pendingTimeout:C,context:{...bt(),...Dt.__routeContext}}));const{search:T,params:F,context:nt,cause:J}=this.getMatch(Q),et=v(Q),ft={search:T,abortController:ut,params:F,preload:et,context:nt,location:r,navigate:Dt=>this.navigate({...Dt,_fromLocation:r}),buildLocation:this.buildLocation,cause:et?"preload":J,matches:i},lt=await((B=(U=tt.options).beforeLoad)==null?void 0:B.call(U,ft))??{};(In(lt)||sn(lt))&&q(K,lt,"BEFORE_LOAD"),f(Q,Dt=>({...Dt,__beforeLoadContext:lt,context:{...bt(),...Dt.__routeContext,...lt},abortController:ut}))}catch(ut){q(K,ut,"BEFORE_LOAD")}f(Q,ut=>{var C;return(C=ut.beforeLoadPromise)==null||C.resolve(),{...ut,beforeLoadPromise:void 0,isFetching:!1}})}}const G=i.slice(0,m),Z=[];G.forEach(({id:K,routeId:Q},N)=>{Z.push((async()=>{const{loaderPromise:L}=this.getMatch(K);let rt=!1,tt=!1;if(L){await L;const Y=this.getMatch(K);Y.error&&b(Y,Y.error)}else{const Y=Z[N-1],it=this.looseRoutesById[Q],St=()=>{const{params:et,loaderDeps:ft,abortController:lt,context:Dt,cause:dt}=this.getMatch(K),kt=v(K);return{params:et,deps:ft,preload:!!kt,parentMatchPromise:Y,abortController:lt,context:Dt,location:r,navigate:Zt=>this.navigate({...Zt,_fromLocation:r}),cause:kt?"preload":dt,route:it}},ut=Date.now()-this.getMatch(K).updatedAt,C=v(K),I=C?it.options.preloadStaleTime??this.options.defaultPreloadStaleTime??3e4:it.options.staleTime??this.options.defaultStaleTime??0,st=it.options.shouldReload,bt=typeof st=="function"?st(St()):st;f(K,et=>({...et,loaderPromise:hi(),preload:!!C&&!this.state.matches.find(ft=>ft.id===K)}));const T=()=>{var et,ft,lt,Dt,dt,kt;const Zt=this.getMatch(K);if(!Zt)return;const fe={matches:i,match:Zt,params:Zt.params,loaderData:Zt.loaderData},Se=(ft=(et=it.options).head)==null?void 0:ft.call(et,fe),ma=Se?.meta,Un=Se?.links,qa=Se?.scripts,_n=(Dt=(lt=it.options).scripts)==null?void 0:Dt.call(lt,fe),De=(kt=(dt=it.options).headers)==null?void 0:kt.call(dt,fe);f(K,Ha=>({...Ha,meta:ma,links:Un,headScripts:qa,headers:De,scripts:_n}))},F=async()=>{var et,ft,lt,Dt,dt;try{const kt=async()=>{const Zt=this.getMatch(K);Zt.minPendingPromise&&await Zt.minPendingPromise};try{this.loadRouteChunk(it),f(K,fe=>({...fe,isFetching:"loader"}));const Zt=await((ft=(et=it.options).loader)==null?void 0:ft.call(et,St()));b(this.getMatch(K),Zt),await it._lazyPromise,await kt(),await it._componentsPromise,ci(()=>{f(K,fe=>({...fe,error:void 0,status:"success",isFetching:!1,updatedAt:Date.now(),loaderData:Zt})),T()})}catch(Zt){let fe=Zt;await kt(),b(this.getMatch(K),Zt);try{(Dt=(lt=it.options).onError)==null||Dt.call(lt,Zt)}catch(Se){fe=Se,b(this.getMatch(K),Se)}ci(()=>{f(K,Se=>({...Se,error:fe,status:"error",isFetching:!1})),T()})}(dt=this.serverSsr)==null||dt.onMatchSettled({router:this,match:this.getMatch(K)})}catch(kt){ci(()=>{f(K,Zt=>({...Zt,loaderPromise:void 0})),T()}),b(this.getMatch(K),kt)}},{status:nt,invalid:J}=this.getMatch(K);rt=nt==="success"&&(J||(bt??ut>I)),C&&it.options.preload===!1||(rt&&!h?(tt=!0,(async()=>{try{await F();const{loaderPromise:et,loadPromise:ft}=this.getMatch(K);et?.resolve(),ft?.resolve(),f(K,lt=>({...lt,loaderPromise:void 0}))}catch(et){fp(et)&&await this.navigate(et)}})()):nt!=="success"||rt&&h?await F():T())}if(!tt){const{loaderPromise:Y,loadPromise:it}=this.getMatch(K);Y?.resolve(),it?.resolve()}return f(K,Y=>({...Y,isFetching:tt?Y.isFetching:!1,loaderPromise:tt?Y.loaderPromise:void 0,invalid:!1})),this.getMatch(K)})())}),await Promise.all(Z),O()}catch(q){R(q)}})()}),await g()}catch(O){if(In(O)||sn(O))throw sn(O)&&!l&&await g(),O}return i},this.invalidate=r=>{const i=l=>{var u;return((u=r?.filter)==null?void 0:u.call(r,l))??!0?{...l,invalid:!0,...l.status==="error"?{status:"pending",error:void 0}:{}}:l};return this.__store.setState(l=>{var u;return{...l,matches:l.matches.map(i),cachedMatches:l.cachedMatches.map(i),pendingMatches:(u=l.pendingMatches)==null?void 0:u.map(i)}}),this.load({sync:r?.sync})},this.resolveRedirect=r=>{const i=r;return i.href||(i.href=this.buildLocation(i).href),i},this.clearCache=r=>{const i=r?.filter;i!==void 0?this.__store.setState(l=>({...l,cachedMatches:l.cachedMatches.filter(u=>!i(u))})):this.__store.setState(l=>({...l,cachedMatches:[]}))},this.clearExpiredCache=()=>{const r=i=>{const l=this.looseRoutesById[i.routeId];if(!l.options.loader)return!0;const u=(i.preload?l.options.preloadGcTime??this.options.defaultPreloadGcTime:l.options.gcTime??this.options.defaultGcTime)??5*60*1e3;return!(i.status!=="error"&&Date.now()-i.updatedAt<u)};this.clearCache({filter:r})},this.loadRouteChunk=r=>(r._lazyPromise===void 0&&(r.lazyFn?r._lazyPromise=r.lazyFn().then(i=>{const{id:l,...u}=i.options;Object.assign(r.options,u)}):r._lazyPromise=Promise.resolve()),r._componentsPromise===void 0&&(r._componentsPromise=r._lazyPromise.then(()=>Promise.all(eE.map(async i=>{const l=r.options[i];l?.preload&&await l.preload()})))),r._componentsPromise),this.preloadRoute=async r=>{const i=this.buildLocation(r);let l=this.matchRoutes(i,{throwOnError:!0,preload:!0,dest:r});const u=new Set([...this.state.matches,...this.state.pendingMatches??[]].map(h=>h.id)),f=new Set([...u,...this.state.cachedMatches.map(h=>h.id)]);ci(()=>{l.forEach(h=>{f.has(h.id)||this.__store.setState(m=>({...m,cachedMatches:[...m.cachedMatches,h]}))})});try{return l=await this.loadMatches({matches:l,location:i,preload:!0,updateMatch:(h,m)=>{u.has(h)?l=l.map(p=>p.id===h?m(p):p):this.updateMatch(h,m)}}),l}catch(h){if(In(h))return h.reloadDocument?void 0:await this.preloadRoute({...h,_fromLocation:i});sn(h)||console.error(h);return}},this.matchRoute=(r,i)=>{const l={...r,to:r.to?this.resolvePathWithBase(r.from||"",r.to):void 0,params:r.params||{},leaveParams:!0},u=this.buildLocation(l);if(i?.pending&&this.state.status!=="pending")return!1;const h=(i?.pending===void 0?!this.state.isLoading:i.pending)?this.latestLocation:this.state.resolvedLocation||this.state.location,m=zu(this.basepath,h.pathname,{...i,to:u.pathname});return!m||r.params&&!Ei(m,r.params,{partial:!0})?!1:m&&(i?.includeSearch??!0)?Ei(h.search,u.search,{partial:!0})?m:!1:m},this._handleNotFound=(r,i,{updateMatch:l=this.updateMatch}={})=>{var u;const f=this.routesById[i.routeId??""]??this.routeTree,h={};for(const p of r)h[p.routeId]=p;!f.options.notFoundComponent&&((u=this.options)!=null&&u.defaultNotFoundComponent)&&(f.options.notFoundComponent=this.options.defaultNotFoundComponent),Nn(f.options.notFoundComponent);const m=h[f.id];Nn(m,"Could not find match for route: "+f.id),l(m.id,p=>({...p,status:"notFound",error:i,isFetching:!1})),i.routerCode==="BEFORE_LOAD"&&f.parentRoute&&(i.routeId=f.parentRoute.id,this._handleNotFound(r,i,{updateMatch:l}))},this.hasNotFoundMatch=()=>this.__store.state.matches.some(r=>r.status==="notFound"||r.globalNotFound),this.update({defaultPreloadDelay:50,defaultPendingMs:1e3,defaultPendingMinMs:500,context:void 0,...n,caseSensitive:n.caseSensitive??!1,notFoundMode:n.notFoundMode??"fuzzy",stringifySearch:n.stringifySearch??zA,parseSearch:n.parseSearch??NA}),typeof document<"u"&&(window.__TSR_ROUTER__=this)}get state(){return this.__store.state}get looseRoutesById(){return this.routesById}matchRoutesInternal(n,r){const{foundRoute:i,matchedRoutes:l,routeParams:u}=this.getMatchedRoutes(n,r?.dest);let f=!1;(i?i.path!=="/"&&u["**"]:Qr(n.pathname))&&(this.options.notFoundRoute?l.push(this.options.notFoundRoute):f=!0);const h=(()=>{if(f){if(this.options.notFoundMode!=="root")for(let v=l.length-1;v>=0;v--){const b=l[v];if(b.children)return b.id}return qn}})(),m=l.map(v=>{var b;let O;const R=((b=v.options.params)==null?void 0:b.parse)??v.options.parseParams;if(R)try{const A=R(u);Object.assign(u,A)}catch(A){if(O=new jA(A.message,{cause:A}),r?.throwOnError)throw O;return O}}),p=[],g=v=>v?.id?v.context??this.options.context??{}:this.options.context??{};return l.forEach((v,b)=>{var O,R;const A=p[b-1],[j,U,B]=(()=>{const it=A?.search??n.search,St=A?._strictSearch??{};try{const ut=dp(v.options.validateSearch,{...it})??{};return[{...it,...ut},{...St,...ut},void 0]}catch(ut){let C=ut;if(ut instanceof lf||(C=new lf(ut.message,{cause:ut})),r?.throwOnError)throw C;return[it,{},C]}})(),q=((R=(O=v.options).loaderDeps)==null?void 0:R.call(O,{search:j}))??"",G=q?JSON.stringify(q):"",{usedParams:Z,interpolatedPath:K}=Nu({path:v.fullPath,params:u,decodeCharMap:this.pathParamsDecodeCharMap}),Q=Nu({path:v.id,params:u,leaveWildcards:!0,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath+G,N=this.getMatch(Q),L=this.state.matches.find(it=>it.routeId===v.id),rt=L?"stay":"enter";let tt;if(N)tt={...N,cause:rt,params:L?Dn(L.params,u):u,_strictParams:Z,search:Dn(L?L.search:N.search,j),_strictSearch:U};else{const it=v.options.loader||v.options.beforeLoad||v.lazyFn||pS(v)?"pending":"success";tt={id:Q,index:b,routeId:v.id,params:L?Dn(L.params,u):u,_strictParams:Z,pathname:za([this.basepath,K]),updatedAt:Date.now(),search:L?Dn(L.search,j):j,_strictSearch:U,searchError:void 0,status:it,isFetching:!1,error:void 0,paramsError:m[b],__routeContext:{},__beforeLoadContext:{},context:{},abortController:new AbortController,fetchCount:0,cause:rt,loaderDeps:L?Dn(L.loaderDeps,q):q,invalid:!1,preload:!1,links:void 0,scripts:void 0,headScripts:void 0,meta:void 0,staticData:v.options.staticData||{},loadPromise:hi(),fullPath:v.fullPath}}r?.preload||(tt.globalNotFound=h===v.id),tt.searchError=B;const Y=g(A);tt.context={...Y,...tt.__routeContext,...tt.__beforeLoadContext},p.push(tt)}),p.forEach((v,b)=>{var O,R;const A=this.looseRoutesById[v.routeId];if(!this.getMatch(v.id)&&r?._buildLocation!==!0){const U=p[b-1],B=g(U),q={deps:v.loaderDeps,params:v.params,context:B,location:n,navigate:G=>this.navigate({...G,_fromLocation:n}),buildLocation:this.buildLocation,cause:v.cause,abortController:v.abortController,preload:!!v.preload,matches:p};v.__routeContext=((R=(O=A.options).context)==null?void 0:R.call(O,q))??{},v.context={...B,...v.__routeContext,...v.__beforeLoadContext}}}),p}}class lf extends Error{}class jA extends Error{}function BA(t){return{loadedAt:0,isLoading:!1,isTransitioning:!1,status:"idle",resolvedLocation:void 0,location:t,matches:[],pendingMatches:[],cachedMatches:[],statusCode:200}}function dp(t,n){if(t==null)return{};if("~standard"in t){const r=t["~standard"].validate(n);if(r instanceof Promise)throw new lf("Async validation not supported");if(r.issues)throw new lf(JSON.stringify(r.issues,void 0,2),{cause:r});return r.value}return"parse"in t?t.parse(n):typeof t=="function"?t(n):{}}const eE=["component","errorComponent","pendingComponent","notFoundComponent"];function pS(t){var n;for(const r of eE)if((n=t.options[r])!=null&&n.preload)return!0;return!1}const na=Symbol.for("TSR_DEFERRED_PROMISE");function qA(t,n){const r=t;return r[na]||(r[na]={status:"pending"},r.then(i=>{r[na].status="success",r[na].data=i}).catch(i=>{r[na].status="error",r[na].error={data:UA(i),__isServerError:!0}})),r}const HA="Error preloading route! ☝️";class nE{constructor(n){if(this.init=r=>{var i,l;this.originalIndex=r.originalIndex;const u=this.options,f=!u?.path&&!u?.id;if(this.parentRoute=(l=(i=this.options).getParentRoute)==null?void 0:l.call(i),f)this._path=qn;else if(!this.parentRoute)throw new Error("Child Route instances must pass a 'getParentRoute: () => ParentRoute' option that returns a Route instance.");let h=f?qn:u?.path;h&&h!=="/"&&(h=jy(h));const m=u?.id||h;let p=f?qn:za([this.parentRoute.id===qn?"":this.parentRoute.id,m]);h===qn&&(h="/"),p!==qn&&(p=za(["/",p]));const g=p===qn?"/":za([this.parentRoute.fullPath,h]);this._path=h,this._id=p,this._fullPath=g,this._to=g,this._ssr=u?.ssr??r.defaultSsr??!0},this.clone=r=>{this._path=r._path,this._id=r._id,this._fullPath=r._fullPath,this._to=r._to,this._ssr=r._ssr,this.options.getParentRoute=r.options.getParentRoute,this.children=r.children},this.addChildren=r=>this._addFileChildren(r),this._addFileChildren=r=>(Array.isArray(r)&&(this.children=r),typeof r=="object"&&r!==null&&(this.children=Object.values(r)),this),this._addFileTypes=()=>this,this.updateLoader=r=>(Object.assign(this.options,r),this),this.update=r=>(Object.assign(this.options,r),this),this.lazy=r=>(this.lazyFn=r,this),this.options=n||{},this.isRoot=!n?.getParentRoute,n?.id&&n?.path)throw new Error("Route cannot have both an 'id' and a 'path' option.")}get to(){return this._to}get id(){return this._id}get path(){return this._path}get fullPath(){return this._fullPath}get ssr(){return this._ssr}}class IA extends nE{constructor(n){super(n)}}const Ge={stringify:t=>JSON.stringify(t,function(r,i){const l=this[r],u=Lu.find(f=>f.stringifyCondition(l));return u?u.stringify(l):i}),parse:t=>JSON.parse(t,function(r,i){const l=this[r];if(sa(l)){const u=Lu.find(f=>f.parseCondition(l));if(u)return u.parse(l)}return i}),encode:t=>{if(Array.isArray(t))return t.map(r=>Ge.encode(r));if(sa(t))return Object.fromEntries(Object.entries(t).map(([r,i])=>[r,Ge.encode(i)]));const n=Lu.find(r=>r.stringifyCondition(t));return n?n.stringify(t):t},decode:t=>{if(sa(t)){const n=Lu.find(r=>r.parseCondition(t));if(n)return n.parse(t)}return Array.isArray(t)?t.map(n=>Ge.decode(n)):sa(t)?Object.fromEntries(Object.entries(t).map(([n,r])=>[n,Ge.decode(r)])):t}},fl=(t,n,r,i)=>({key:t,stringifyCondition:n,stringify:l=>({[`$${t}`]:r(l)}),parseCondition:l=>Object.hasOwn(l,`$${t}`),parse:l=>i(l[`$${t}`])}),Lu=[fl("undefined",t=>t===void 0,()=>0,()=>{}),fl("date",t=>t instanceof Date,t=>t.toISOString(),t=>new Date(t)),fl("error",t=>t instanceof Error,t=>({...t,message:t.message,stack:void 0,cause:t.cause}),t=>Object.assign(new Error(t.message),t)),fl("formData",t=>t instanceof FormData,t=>{const n={};return t.forEach((r,i)=>{const l=n[i];l!==void 0?Array.isArray(l)?l.push(r):n[i]=[l,r]:n[i]=r}),n},t=>{const n=new FormData;return Object.entries(t).forEach(([r,i])=>{Array.isArray(i)?i.forEach(l=>n.append(r,l)):n.append(r,i)}),n}),fl("bigint",t=>typeof t=="bigint",t=>t.toString(),t=>BigInt(t))];function PA(t){var n,r,i;Nn((n=window.__TSR_SSR__)==null?void 0:n.dehydrated);const{manifest:l,dehydratedData:u}=Ge.parse(window.__TSR_SSR__.dehydrated);t.ssr={manifest:l,serializer:Ge},t.clientSsr={getStreamedValue:m=>{var p;if(t.isServer)return;const g=(p=window.__TSR_SSR__)==null?void 0:p.streamedValues[m];if(g)return g.parsed||(g.parsed=t.ssr.serializer.parse(g.value)),g.parsed}};const f=t.matchRoutes(t.state.location),h=Promise.all(f.map(m=>{const p=t.looseRoutesById[m.routeId];return t.loadRouteChunk(p)}));return f.forEach(m=>{var p;const g=window.__TSR_SSR__.matches.find(v=>v.id===m.id);return g?(Object.assign(m,g),g.__beforeLoadContext&&(m.__beforeLoadContext=t.ssr.serializer.parse(g.__beforeLoadContext)),g.loaderData&&(m.loaderData=t.ssr.serializer.parse(g.loaderData)),g.error&&(m.error=t.ssr.serializer.parse(g.error)),(p=m.extracted)==null||p.forEach(v=>{Up(m,["loaderData",...v.path],v.value)})):Object.assign(m,{status:"success",updatedAt:Date.now()}),m}),t.__store.setState(m=>({...m,matches:f})),(i=(r=t.options).hydrate)==null||i.call(r,u),t.state.matches.forEach(m=>{var p,g,v,b,O,R;const A=t.looseRoutesById[m.routeId],j=t.state.matches[m.index-1],U=j?.context??t.options.context??{},B={deps:m.loaderDeps,params:m.params,context:U,location:t.state.location,navigate:K=>t.navigate({...K,_fromLocation:t.state.location}),buildLocation:t.buildLocation,cause:m.cause,abortController:m.abortController,preload:!1,matches:f};m.__routeContext=((g=(p=A.options).context)==null?void 0:g.call(p,B))??{},m.context={...U,...m.__routeContext,...m.__beforeLoadContext};const q={matches:t.state.matches,match:m,params:m.params,loaderData:m.loaderData},G=(b=(v=A.options).head)==null?void 0:b.call(v,q),Z=(R=(O=A.options).scripts)==null?void 0:R.call(O,q);m.meta=G?.meta,m.links=G?.links,m.headScripts=G?.scripts,m.scripts=Z}),h}function Up(t,n,r){n.length===1&&(t[n[0]]=r);const[i,...l]=n;Array.isArray(t)?Up(t[Number(i)],l,r):sa(t)&&Up(t[i],l,r)}const VA="modulepreload",GA=function(t){return"/_build/"+t},yS={},ia=function(n,r,i){let l=Promise.resolve();if(r&&r.length>0){let f=function(p){return Promise.all(p.map(g=>Promise.resolve(g).then(v=>({status:"fulfilled",value:v}),v=>({status:"rejected",reason:v}))))};document.getElementsByTagName("link");const h=document.querySelector("meta[property=csp-nonce]"),m=h?.nonce||h?.getAttribute("nonce");l=f(r.map(p=>{if(p=GA(p),p in yS)return;yS[p]=!0;const g=p.endsWith(".css"),v=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${v}`))return;const b=document.createElement("link");if(b.rel=g?"stylesheet":VA,g||(b.as="script"),b.crossOrigin="",b.href=p,m&&b.setAttribute("nonce",m),document.head.appendChild(b),g)return new Promise((O,R)=>{b.addEventListener("load",O),b.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${p}`)))})}))}function u(f){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=f,window.dispatchEvent(h),!h.defaultPrevented)throw f}return l.then(f=>{for(const h of f||[])h.status==="rejected"&&u(h.reason);return n().catch(u)})};const KA=[];function Tl(t,n){const r=n||t||{};return typeof r.method>"u"&&(r.method="GET"),{options:r,middleware:i=>Tl(void 0,Object.assign(r,{middleware:i})),validator:i=>Tl(void 0,Object.assign(r,{validator:i})),type:i=>Tl(void 0,Object.assign(r,{type:i})),handler:(...i)=>{const[l,u]=i;Object.assign(r,{...l,extractedFn:l,serverFn:u});const f=[...r.middleware||[],tC(r)];return Object.assign(async h=>gS(f,"client",{...l,...r,data:h?.data,headers:h?.headers,signal:h?.signal,context:{}}).then(m=>{if(r.response==="full")return m;if(m.error)throw m.error;return m.result}),{...l,__executeServer:async(h,m)=>{const p=h instanceof FormData?XA(h):h;p.type=typeof r.type=="function"?r.type(p):r.type;const g={...l,...p,signal:m},v=()=>gS(f,"server",g).then(b=>({result:b.result,error:b.error,context:b.sendContext}));if(g.type==="static"){let b;if(Pn?.getItem&&(b=await Pn.getItem(g)),b||(b=await v().then(O=>({ctx:O,error:null})).catch(O=>({ctx:void 0,error:O})),Pn?.setItem&&await Pn.setItem(g,b)),Nn(b),b.error)throw b.error;return b.ctx}return v()}})}}}async function gS(t,n,r){const i=ZA([...KA,...t]),l=async u=>{const f=i.shift();if(!f)return u;f.options.validator&&(n!=="client"||f.options.validateClient)&&(u.data=await WA(f.options.validator,u.data));const h=n==="client"?f.options.client:f.options.server;return h?JA(h,u,async m=>l(m).catch(p=>{if(In(p)||sn(p))return{...m,error:p};throw p})):l(u)};return l({...r,headers:r.headers||{},sendContext:r.sendContext||{},context:r.context||{}})}let Pn;function QA(t){const n=Pn;return Pn=typeof t=="function"?t():t,()=>{Pn=n}}async function YA(t){const n=new TextEncoder().encode(t),r=await crypto.subtle.digest("SHA-1",n);return Array.from(new Uint8Array(r)).map(u=>u.toString(16).padStart(2,"0")).join("")}QA(()=>{const t=async(i,l)=>`/__tsr/staticServerFnCache/${await YA(`${i.functionId}__${l}`)}.json`,n=i=>JSON.stringify(i??"",(f,h)=>h&&typeof h=="object"&&!Array.isArray(h)?Object.keys(h).sort().reduce((m,p)=>(m[p]=h[p],m),{}):h).replace(/[/\\?%*:|"<>]/g,"-").replace(/\s+/g,"_"),r=typeof document<"u"?new Map:null;return{getItem:async i=>{if(typeof document>"u"){const l=n(i.data),u=await t(i,l),f="/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend/.output/public",{promises:h}=await ia(async()=>{const{promises:b}=await import("./__vite-browser-external-BIHI7g3E.js");return{promises:b}},[]),p=(await ia(()=>import("./__vite-browser-external-BIHI7g3E.js"),[])).join(f,u),[g,v]=await h.readFile(p,"utf-8").then(b=>[Ge.parse(b),null]).catch(b=>[null,b]);if(v&&v.code!=="ENOENT")throw v;return g}},setItem:async(i,l)=>{const{promises:u}=await ia(async()=>{const{promises:v}=await import("./__vite-browser-external-BIHI7g3E.js");return{promises:v}},[]),f=await ia(()=>import("./__vite-browser-external-BIHI7g3E.js"),[]),h=n(i.data),m=await t(i,h),g=f.join("/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend/.output/public",m);await u.mkdir(f.dirname(g),{recursive:!0}),await u.writeFile(g,Ge.stringify(l))},fetchItem:async i=>{const l=n(i.data),u=await t(i,l);let f=r?.get(u);return f||(f=await fetch(u,{method:"GET"}).then(h=>h.text()).then(h=>Ge.parse(h)),r?.set(u,f)),f}}});function XA(t){const n=t.get("__TSR_CONTEXT");if(t.delete("__TSR_CONTEXT"),typeof n!="string")return{context:{},data:t};try{return{context:Ge.parse(n),data:t}}catch{return{data:t}}}function ZA(t){const n=new Set,r=[],i=l=>{l.forEach(u=>{u.options.middleware&&i(u.options.middleware),n.has(u)||(n.add(u),r.push(u))})};return i(t),r}const JA=async(t,n,r)=>t({...n,next:async(i={})=>r({...n,...i,context:{...n.context,...i.context},sendContext:{...n.sendContext,...i.sendContext??{}},headers:hA(n.headers,i.headers),result:i.result!==void 0?i.result:n.response==="raw"?i:n.result,error:i.error??n.error})});function WA(t,n){if(t==null)return{};if("~standard"in t){const r=t["~standard"].validate(n);if(r instanceof Promise)throw new Error("Async validation not supported");if(r.issues)throw new Error(JSON.stringify(r.issues,void 0,2));return r.value}if("parse"in t)return t.parse(n);if(typeof t=="function")return t(n);throw new Error("Invalid validator type!")}function tC(t){return{_types:void 0,options:{validator:t.validator,validateClient:t.validateClient,client:async({next:n,sendContext:r,...i})=>{var l;const u={...i,context:r,type:typeof i.type=="function"?i.type(i):i.type};if(i.type==="static"&&typeof document<"u"){Nn(Pn);const h=await Pn.fetchItem(u);if(h){if(h.error)throw h.error;return n(h.ctx)}`${u.functionId}${JSON.stringify(u.data)}`}const f=await((l=t.extractedFn)==null?void 0:l.call(t,u));return n(f)},server:async({next:n,...r})=>{var i;const l=await((i=t.serverFn)==null?void 0:i.call(t,r));return n({...r,result:l})}}}}var at=Zl();const Ht=nA(at);function eC({promise:t}){const n=qA(t);if(n[na].status==="pending")throw n;if(n[na].status==="error")throw n[na].error;return[n[na].data,n]}function nC(t){const n=W.jsx(aC,{...t});return t.fallback?W.jsx(at.Suspense,{fallback:t.fallback,children:n}):n}function aC(t){const[n]=eC(t);return t.children(n)}function By(t){const n=t.errorComponent??Hf;return W.jsx(rC,{getResetKey:t.getResetKey,onCatch:t.onCatch,children:({error:r,reset:i})=>r?at.createElement(n,{error:r,reset:i}):t.children})}class rC extends at.Component{constructor(){super(...arguments),this.state={error:null}}static getDerivedStateFromProps(n){return{resetKey:n.getResetKey()}}static getDerivedStateFromError(n){return{error:n}}reset(){this.setState({error:null})}componentDidUpdate(n,r){r.error&&r.resetKey!==this.state.resetKey&&this.reset()}componentDidCatch(n,r){this.props.onCatch&&this.props.onCatch(n,r)}render(){return this.props.children({error:this.state.resetKey!==this.props.getResetKey()?null:this.state.error,reset:()=>{this.reset()}})}}function Hf({error:t}){const[n,r]=at.useState(!1);return W.jsxs("div",{style:{padding:".5rem",maxWidth:"100%"},children:[W.jsxs("div",{style:{display:"flex",alignItems:"center",gap:".5rem"},children:[W.jsx("strong",{style:{fontSize:"1rem"},children:"Something went wrong!"}),W.jsx("button",{style:{appearance:"none",fontSize:".6em",border:"1px solid currentColor",padding:".1rem .2rem",fontWeight:"bold",borderRadius:".25rem"},onClick:()=>r(i=>!i),children:n?"Hide Error":"Show Error"})]}),W.jsx("div",{style:{height:".25rem"}}),n?W.jsx("div",{children:W.jsx("pre",{style:{fontSize:".7em",border:"1px solid red",borderRadius:".25rem",padding:".3rem",color:"red",overflow:"auto"},children:t.message?W.jsx("code",{children:t.message}):null})}):null]})}function sC({children:t,fallback:n=null}){return iC()?W.jsx(Ht.Fragment,{children:t}):W.jsx(Ht.Fragment,{children:n})}function iC(){return Ht.useSyncExternalStore(oC,()=>!0,()=>!1)}function oC(){return()=>{}}var hp={exports:{}},mp={},pp={exports:{}},yp={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vS;function lC(){if(vS)return yp;vS=1;var t=Zl();function n(v,b){return v===b&&(v!==0||1/v===1/b)||v!==v&&b!==b}var r=typeof Object.is=="function"?Object.is:n,i=t.useState,l=t.useEffect,u=t.useLayoutEffect,f=t.useDebugValue;function h(v,b){var O=b(),R=i({inst:{value:O,getSnapshot:b}}),A=R[0].inst,j=R[1];return u(function(){A.value=O,A.getSnapshot=b,m(A)&&j({inst:A})},[v,O,b]),l(function(){return m(A)&&j({inst:A}),v(function(){m(A)&&j({inst:A})})},[v]),f(O),O}function m(v){var b=v.getSnapshot;v=v.value;try{var O=b();return!r(v,O)}catch{return!0}}function p(v,b){return b()}var g=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?p:h;return yp.useSyncExternalStore=t.useSyncExternalStore!==void 0?t.useSyncExternalStore:g,yp}var _S;function cC(){return _S||(_S=1,pp.exports=lC()),pp.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bS;function uC(){if(bS)return mp;bS=1;var t=Zl(),n=cC();function r(p,g){return p===g&&(p!==0||1/p===1/g)||p!==p&&g!==g}var i=typeof Object.is=="function"?Object.is:r,l=n.useSyncExternalStore,u=t.useRef,f=t.useEffect,h=t.useMemo,m=t.useDebugValue;return mp.useSyncExternalStoreWithSelector=function(p,g,v,b,O){var R=u(null);if(R.current===null){var A={hasValue:!1,value:null};R.current=A}else A=R.current;R=h(function(){function U(K){if(!B){if(B=!0,q=K,K=b(K),O!==void 0&&A.hasValue){var Q=A.value;if(O(Q,K))return G=Q}return G=K}if(Q=G,i(q,K))return Q;var N=b(K);return O!==void 0&&O(Q,N)?(q=K,Q):(q=K,G=N)}var B=!1,q,G,Z=v===void 0?null:v;return[function(){return U(g())},Z===null?void 0:function(){return U(Z())}]},[g,v,b,O]);var j=l(p,R[0],R[1]);return f(function(){A.hasValue=!0,A.value=j},[j]),m(j),j},mp}var SS;function fC(){return SS||(SS=1,hp.exports=uC()),hp.exports}var dC=fC();function hC(t,n=r=>r){return dC.useSyncExternalStoreWithSelector(t.subscribe,()=>t.state,()=>t.state,n,mC)}function mC(t,n){if(Object.is(t,n))return!0;if(typeof t!="object"||t===null||typeof n!="object"||n===null)return!1;if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(const[i,l]of t)if(!n.has(i)||!Object.is(l,n.get(i)))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(const i of t)if(!n.has(i))return!1;return!0}const r=Object.keys(t);if(r.length!==Object.keys(n).length)return!1;for(let i=0;i<r.length;i++)if(!Object.prototype.hasOwnProperty.call(n,r[i])||!Object.is(t[r[i]],n[r[i]]))return!1;return!0}const gp=at.createContext(null);function aE(){return typeof document>"u"?gp:window.__TSR_ROUTER_CONTEXT__?window.__TSR_ROUTER_CONTEXT__:(window.__TSR_ROUTER_CONTEXT__=gp,gp)}function ln(t){const n=at.useContext(aE());return t?.warn,n}function me(t){const n=ln({warn:t?.router===void 0}),r=t?.router||n,i=at.useRef(void 0);return hC(r.__store,l=>{if(t?.select){if(t.structuralSharing??r.options.defaultStructuralSharing){const u=Dn(i.current,t.select(l));return i.current=u,u}return t.select(l)}return l})}const If=at.createContext(void 0),pC=at.createContext(void 0);function vr(t){const n=at.useContext(t.from?pC:If);return me({select:i=>{const l=i.matches.find(u=>t.from?t.from===u.routeId:u.id===n);if(Nn(!((t.shouldThrow??!0)&&!l),`Could not find ${t.from?`an active match from "${t.from}"`:"a nearest match!"}`),l!==void 0)return t.select?t.select(l):l},structuralSharing:t.structuralSharing})}function rE(t){return vr({from:t.from,strict:t.strict,structuralSharing:t.structuralSharing,select:n=>t.select?t.select(n.loaderData):n.loaderData})}function sE(t){const{select:n,...r}=t;return vr({...r,select:i=>n?n(i.loaderDeps):i.loaderDeps})}function iE(t){return vr({from:t.from,strict:t.strict,shouldThrow:t.shouldThrow,structuralSharing:t.structuralSharing,select:n=>t.select?t.select(n.params):n.params})}function oE(t){return vr({from:t.from,strict:t.strict,shouldThrow:t.shouldThrow,structuralSharing:t.structuralSharing,select:n=>t.select?t.select(n.search):n.search})}function lE(t){const{navigate:n}=ln();return at.useCallback(r=>n({from:t?.from,...r}),[t?.from,n])}function W8(t){const n=ln(),r=at.useRef(null);return at.useEffect(()=>{r.current!==t&&(n.navigate({...t}),r.current=t)},[n,t]),null}var yC=Q1();const Sl=typeof window<"u"?at.useLayoutEffect:at.useEffect;function vp(t){const n=at.useRef({value:t,prev:null}),r=n.current.value;return t!==r&&(n.current={value:t,prev:r}),n.current.prev}function gC(t,n,r={},i={}){const l=at.useRef(typeof IntersectionObserver=="function"),u=at.useRef(null);return at.useEffect(()=>{if(!(!t.current||!l.current||i.disabled))return u.current=new IntersectionObserver(([f])=>{n(f)},r),u.current.observe(t.current),()=>{var f;(f=u.current)==null||f.disconnect()}},[n,r,i.disabled,t]),u.current}function vC(t){const n=at.useRef(null);return at.useImperativeHandle(t,()=>n.current,[]),n}function _C(){const t=ln(),n=at.useRef({router:t,mounted:!1}),r=me({select:({isLoading:v})=>v}),[i,l]=at.useState(!1),u=me({select:v=>v.matches.some(b=>b.status==="pending"),structuralSharing:!0}),f=vp(r),h=r||i||u,m=vp(h),p=r||u,g=vp(p);return t.isServer||(t.startTransition=v=>{l(!0),at.startTransition(()=>{v(),l(!1)})}),at.useEffect(()=>{const v=t.history.subscribe(t.load),b=t.buildLocation({to:t.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});return Qr(t.latestLocation.href)!==Qr(b.href)&&t.commitLocation({...b,replace:!0}),()=>{v()}},[t,t.history]),Sl(()=>{if(typeof window<"u"&&t.clientSsr||n.current.router===t&&n.current.mounted)return;n.current={router:t,mounted:!0},(async()=>{try{await t.load()}catch(b){console.error(b)}})()},[t]),Sl(()=>{f&&!r&&t.emit({type:"onLoad",...Xr(t.state)})},[f,t,r]),Sl(()=>{g&&!p&&t.emit({type:"onBeforeRouteMount",...Xr(t.state)})},[p,g,t]),Sl(()=>{m&&!h&&(t.emit({type:"onResolved",...Xr(t.state)}),t.__store.setState(v=>({...v,status:"idle",resolvedLocation:v.location})),DA(t))},[h,m,t]),null}function bC(t){const n=me({select:r=>`not-found-${r.location.pathname}-${r.status}`});return W.jsx(By,{getResetKey:()=>n,onCatch:(r,i)=>{var l;if(sn(r))(l=t.onCatch)==null||l.call(t,r,i);else throw r},errorComponent:({error:r})=>{var i;if(sn(r))return(i=t.fallback)==null?void 0:i.call(t,r);throw r},children:t.children})}function SC(){return W.jsx("p",{children:"Not Found"})}function Vu(t){return W.jsx(W.Fragment,{children:t.children})}function cE(t,n,r){return n.options.notFoundComponent?W.jsx(n.options.notFoundComponent,{data:r}):t.options.defaultNotFoundComponent?W.jsx(t.options.defaultNotFoundComponent,{data:r}):W.jsx(SC,{})}var _p,ES;function EC(){if(ES)return _p;ES=1;const t={},n=t.hasOwnProperty,r=(N,L)=>{for(const rt in N)n.call(N,rt)&&L(rt,N[rt])},i=(N,L)=>(L&&r(L,(rt,tt)=>{N[rt]=tt}),N),l=(N,L)=>{const rt=N.length;let tt=-1;for(;++tt<rt;)L(N[tt])},u=N=>"\\u"+("0000"+N).slice(-4),f=(N,L)=>{let rt=N.toString(16);return L?rt:rt.toUpperCase()},h=t.toString,m=Array.isArray,p=N=>typeof Buffer=="function"&&Buffer.isBuffer(N),g=N=>h.call(N)=="[object Object]",v=N=>typeof N=="string"||h.call(N)=="[object String]",b=N=>typeof N=="number"||h.call(N)=="[object Number]",O=N=>typeof N=="bigint",R=N=>typeof N=="function",A=N=>h.call(N)=="[object Map]",j=N=>h.call(N)=="[object Set]",U={"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},B=/[\\\b\f\n\r\t]/,q=/[0-9]/,G=/[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Z=/([\uD800-\uDBFF][\uDC00-\uDFFF])|([\uD800-\uDFFF])|(['"`])|[^]/g,K=/([\uD800-\uDBFF][\uDC00-\uDFFF])|([\uD800-\uDFFF])|(['"`])|[^ !#-&\(-\[\]-_a-~]/g,Q=(N,L)=>{const rt=()=>{I=C,++L.indentLevel,C=L.indent.repeat(L.indentLevel)},tt={escapeEverything:!1,minimal:!1,isScriptContext:!1,quotes:"single",wrap:!1,es6:!1,json:!1,compact:!0,lowercaseHex:!1,numbers:"decimal",indent:"	",indentLevel:0,__inline1__:!1,__inline2__:!1},Y=L&&L.json;Y&&(tt.quotes="double",tt.wrap=!0),L=i(tt,L),L.quotes!="single"&&L.quotes!="double"&&L.quotes!="backtick"&&(L.quotes="single");const it=L.quotes=="double"?'"':L.quotes=="backtick"?"`":"'",St=L.compact,ut=L.lowercaseHex;let C=L.indent.repeat(L.indentLevel),I="";const st=L.__inline1__,bt=L.__inline2__,T=St?"":`
`;let F,nt=!0;const J=L.numbers=="binary",et=L.numbers=="octal",ft=L.numbers=="decimal",lt=L.numbers=="hexadecimal";if(Y&&N&&R(N.toJSON)&&(N=N.toJSON()),!v(N)){if(A(N))return N.size==0?"new Map()":(St||(L.__inline1__=!0,L.__inline2__=!1),"new Map("+Q(Array.from(N),L)+")");if(j(N))return N.size==0?"new Set()":"new Set("+Q(Array.from(N),L)+")";if(p(N))return N.length==0?"Buffer.from([])":"Buffer.from("+Q(Array.from(N),L)+")";if(m(N))return F=[],L.wrap=!0,st&&(L.__inline1__=!1,L.__inline2__=!0),bt||rt(),l(N,dt=>{nt=!1,bt&&(L.__inline2__=!1),F.push((St||bt?"":C)+Q(dt,L))}),nt?"[]":bt?"["+F.join(", ")+"]":"["+T+F.join(","+T)+T+(St?"":I)+"]";if(b(N)||O(N)){if(Y)return JSON.stringify(Number(N));let dt;if(ft)dt=String(N);else if(lt){let kt=N.toString(16);ut||(kt=kt.toUpperCase()),dt="0x"+kt}else J?dt="0b"+N.toString(2):et&&(dt="0o"+N.toString(8));return O(N)?dt+"n":dt}else return O(N)?Y?JSON.stringify(Number(N)):N+"n":g(N)?(F=[],L.wrap=!0,rt(),r(N,(dt,kt)=>{nt=!1,F.push((St?"":C)+Q(dt,L)+":"+(St?"":" ")+Q(kt,L))}),nt?"{}":"{"+T+F.join(","+T)+T+(St?"":I)+"}"):Y?JSON.stringify(N)||"null":String(N)}const Dt=L.escapeEverything?Z:K;return F=N.replace(Dt,(dt,kt,Zt,fe,Se,ma)=>{if(kt){if(L.minimal)return kt;const qa=kt.charCodeAt(0),_n=kt.charCodeAt(1);if(L.es6){const De=(qa-55296)*1024+_n-56320+65536;return"\\u{"+f(De,ut)+"}"}return u(f(qa,ut))+u(f(_n,ut))}if(Zt)return u(f(Zt.charCodeAt(0),ut));if(dt=="\0"&&!Y&&!q.test(ma.charAt(Se+1)))return"\\0";if(fe)return fe==it||L.escapeEverything?"\\"+fe:fe;if(B.test(dt))return U[dt];if(L.minimal&&!G.test(dt))return dt;const Un=f(dt.charCodeAt(0),ut);return Y||Un.length>2?u(Un):"\\x"+("00"+Un).slice(-2)}),it=="`"&&(F=F.replace(/\$\{/g,"\\${")),L.isScriptContext&&(F=F.replace(/<\/(script|style)/gi,"<\\/$1").replace(/<!--/g,Y?"\\u003C!--":"\\x3C!--")),L.wrap&&(F=it+F+it),F};return Q.version="3.0.2",_p=Q,_p}EC();function TC({children:t,log:n}){return typeof document<"u"?null:W.jsx("script",{className:"tsr-once",dangerouslySetInnerHTML:{__html:[t,"",'if (typeof __TSR_SSR__ !== "undefined") __TSR_SSR__.cleanScripts()'].filter(Boolean).join(`
`)}})}function OC(){const t=ln(),r=(t.options.getScrollRestorationKey||$p)(t.latestLocation),i=r!==$p(t.latestLocation)?r:null;return!t.isScrollRestoring||!t.isServer?null:W.jsx(TC,{children:`(${W1.toString()})(${JSON.stringify(sf)},${JSON.stringify(i)}, undefined, true)`,log:!1})}const uE=at.memo(function({matchId:n}){var r,i;const l=ln(),u=me({select:U=>{var B;return(B=U.matches.find(q=>q.id===n))==null?void 0:B.routeId}});Nn(u);const f=l.routesById[u],h=f.options.pendingComponent??l.options.defaultPendingComponent,m=h?W.jsx(h,{}):null,p=f.options.errorComponent??l.options.defaultErrorComponent,g=f.options.onCatch??l.options.defaultOnCatch,v=f.isRoot?f.options.notFoundComponent??((r=l.options.notFoundRoute)==null?void 0:r.options.component):f.options.notFoundComponent,b=(!f.isRoot||f.options.wrapInSuspense)&&(f.options.wrapInSuspense??h??((i=f.options.errorComponent)==null?void 0:i.preload))?at.Suspense:Vu,O=p?By:Vu,R=v?bC:Vu,A=me({select:U=>U.loadedAt}),j=me({select:U=>{var B;const q=U.matches.findIndex(G=>G.id===n);return(B=U.matches[q-1])==null?void 0:B.routeId}});return W.jsxs(W.Fragment,{children:[W.jsx(If.Provider,{value:n,children:W.jsx(b,{fallback:m,children:W.jsx(O,{getResetKey:()=>A,errorComponent:p||Hf,onCatch:(U,B)=>{if(sn(U))throw U;g?.(U,B)},children:W.jsx(R,{fallback:U=>{if(!v||U.routeId&&U.routeId!==u||!U.routeId&&!f.isRoot)throw U;return at.createElement(v,U)},children:W.jsx(MC,{matchId:n})})})})}),j===qn&&l.options.scrollRestoration?W.jsxs(W.Fragment,{children:[W.jsx(RC,{}),W.jsx(OC,{})]}):null]})});function RC(){const t=ln(),n=at.useRef(void 0);return W.jsx("script",{suppressHydrationWarning:!0,ref:r=>{r&&(n.current===void 0||n.current.href!==t.latestLocation.href)&&(t.emit({type:"onRendered",...Xr(t.state)}),n.current=t.latestLocation)}},t.latestLocation.state.key)}const MC=at.memo(function({matchId:n}){var r,i,l;const u=ln(),{match:f,key:h,routeId:m}=me({select:b=>{const O=b.matches.findIndex(q=>q.id===n),R=b.matches[O],A=R.routeId,j=u.routesById[A].options.remountDeps??u.options.defaultRemountDeps,U=j?.({routeId:A,loaderDeps:R.loaderDeps,params:R._strictParams,search:R._strictSearch});return{key:U?JSON.stringify(U):void 0,routeId:A,match:Lp(R,["id","status","error"])}},structuralSharing:!0}),p=u.routesById[m],g=at.useMemo(()=>{const b=p.options.component??u.options.defaultComponent;return b?W.jsx(b,{},h):W.jsx(qy,{})},[h,p.options.component,u.options.defaultComponent]),v=(p.options.errorComponent??u.options.defaultErrorComponent)||Hf;if(f.status==="notFound")return Nn(sn(f.error)),cE(u,p,f.error);if(f.status==="redirected")throw Nn(In(f.error)),(r=u.getMatch(f.id))==null?void 0:r.loadPromise;if(f.status==="error"){if(u.isServer)return W.jsx(v,{error:f.error,reset:void 0,info:{componentStack:""}});throw f.error}if(f.status==="pending"){const b=p.options.pendingMinMs??u.options.defaultPendingMinMs;if(b&&!((i=u.getMatch(f.id))!=null&&i.minPendingPromise)&&!u.isServer){const O=hi();Promise.resolve().then(()=>{u.updateMatch(f.id,R=>({...R,minPendingPromise:O}))}),setTimeout(()=>{O.resolve(),u.updateMatch(f.id,R=>({...R,minPendingPromise:void 0}))},b)}throw(l=u.getMatch(f.id))==null?void 0:l.loadPromise}return g}),qy=at.memo(function(){const n=ln(),r=at.useContext(If),i=me({select:p=>{var g;return(g=p.matches.find(v=>v.id===r))==null?void 0:g.routeId}}),l=n.routesById[i],u=me({select:p=>{const v=p.matches.find(b=>b.id===r);return Nn(v),v.globalNotFound}}),f=me({select:p=>{var g;const v=p.matches,b=v.findIndex(O=>O.id===r);return(g=v[b+1])==null?void 0:g.id}});if(u)return cE(n,l,void 0);if(!f)return null;const h=W.jsx(uE,{matchId:f}),m=n.options.defaultPendingComponent?W.jsx(n.options.defaultPendingComponent,{}):null;return r===qn?W.jsx(at.Suspense,{fallback:m,children:h}):h});function wC(){const t=ln(),n=t.options.defaultPendingComponent?W.jsx(t.options.defaultPendingComponent,{}):null,r=t.isServer||typeof document<"u"&&t.clientSsr?Vu:at.Suspense,i=W.jsxs(r,{fallback:n,children:[W.jsx(_C,{}),W.jsx(AC,{})]});return t.options.InnerWrap?W.jsx(t.options.InnerWrap,{children:i}):i}function AC(){const t=me({select:r=>{var i;return(i=r.matches[0])==null?void 0:i.id}}),n=me({select:r=>r.loadedAt});return W.jsx(If.Provider,{value:t,children:W.jsx(By,{getResetKey:()=>n,errorComponent:Hf,onCatch:r=>{r.message||r.toString()},children:t?W.jsx(uE,{matchId:t}):null})})}function CC(t){return me({select:n=>{const r=n.matches;return t?.select?t.select(r):r},structuralSharing:t?.structuralSharing})}function xC(t,n){const r=ln(),[i,l]=at.useState(!1),u=at.useRef(!1),f=vC(n),{activeProps:h=()=>({className:"active"}),inactiveProps:m=()=>({}),activeOptions:p,to:g,preload:v,preloadDelay:b,hashScrollIntoView:O,replace:R,startTransition:A,resetScroll:j,viewTransition:U,children:B,target:q,disabled:G,style:Z,className:K,onClick:Q,onFocus:N,onMouseEnter:L,onMouseLeave:rt,onTouchStart:tt,ignoreBlocker:Y,...it}=t,{params:St,search:ut,hash:C,state:I,mask:st,reloadDocument:bt,...T}=it,F=at.useMemo(()=>{try{return new URL(`${g}`),"external"}catch{}return"internal"},[g]),nt=me({select:Bt=>Bt.location.search,structuralSharing:!0}),J=CC({select:Bt=>{var ie;return t.from??((ie=Bt[Bt.length-1])==null?void 0:ie.fullPath)}}),et=at.useMemo(()=>({...t,from:J}),[t,J]),ft=at.useMemo(()=>r.buildLocation(et),[r,et,nt]),lt=at.useMemo(()=>et.reloadDocument?!1:v??r.options.defaultPreload,[r.options.defaultPreload,v,et.reloadDocument]),Dt=b??r.options.defaultPreloadDelay??0,dt=me({select:Bt=>{if(p?.exact){if(!EA(Bt.location.pathname,ft.pathname,r.basepath))return!1}else{const ie=af(Bt.location.pathname,r.basepath).split("/");if(!af(ft.pathname,r.basepath).split("/").every((Pd,Or)=>Pd===ie[Or]))return!1}return(p?.includeSearch??!0)&&!Ei(Bt.location.search,ft.search,{partial:!p?.exact,ignoreUndefined:!p?.explicitUndefined})?!1:p?.includeHash?Bt.location.hash===ft.hash:!0}}),kt=at.useCallback(()=>{r.preloadRoute(et).catch(Bt=>{console.warn(Bt),console.warn(HA)})},[et,r]),Zt=at.useCallback(Bt=>{Bt?.isIntersecting&&kt()},[kt]);if(gC(f,Zt,{rootMargin:"100px"},{disabled:!!G||lt!=="viewport"}),Sl(()=>{u.current||!G&&lt==="render"&&(kt(),u.current=!0)},[G,kt,lt]),F==="external")return{...T,ref:f,type:F,href:g,...B&&{children:B},...q&&{target:q},...G&&{disabled:G},...Z&&{style:Z},...K&&{className:K},...Q&&{onClick:Q},...N&&{onFocus:N},...L&&{onMouseEnter:L},...rt&&{onMouseLeave:rt},...tt&&{onTouchStart:tt}};const fe=Bt=>{if(!G&&!DC(Bt)&&!Bt.defaultPrevented&&(!q||q==="_self")&&Bt.button===0){Bt.preventDefault(),yC.flushSync(()=>{l(!0)});const ie=r.subscribe("onResolved",()=>{ie(),l(!1)});return r.navigate({...et,replace:R,resetScroll:j,hashScrollIntoView:O,startTransition:A,viewTransition:U,ignoreBlocker:Y})}},Se=Bt=>{G||lt&&kt()},ma=Se,Un=Bt=>{if(G)return;const ie=Bt.target||{};if(lt){if(ie.preloadTimeout)return;ie.preloadTimeout=setTimeout(()=>{ie.preloadTimeout=null,kt()},Dt)}},qa=Bt=>{if(G)return;const ie=Bt.target||{};ie.preloadTimeout&&(clearTimeout(ie.preloadTimeout),ie.preloadTimeout=null)},_n=Bt=>ie=>{var Ts;(Ts=ie.persist)==null||Ts.call(ie),Bt.filter(Boolean).forEach(vc=>{ie.defaultPrevented||vc(ie)})},De=dt?Kr(h,{})??{}:{},Ha=dt?{}:Kr(m,{}),uo=[K,De.className,Ha.className].filter(Boolean).join(" "),fo={...Z,...De.style,...Ha.style};return{...T,...De,...Ha,href:G?void 0:ft.maskedLocation?r.history.createHref(ft.maskedLocation.href):r.history.createHref(ft.href),ref:f,onClick:_n([Q,fe]),onFocus:_n([N,Se]),onMouseEnter:_n([L,Un]),onMouseLeave:_n([rt,qa]),onTouchStart:_n([tt,ma]),disabled:!!G,target:q,...Object.keys(fo).length&&{style:fo},...uo&&{className:uo},...G&&{role:"link","aria-disabled":!0},...dt&&{"data-status":"active","aria-current":"page"},...i&&{"data-transitioning":"transitioning"}}}const fE=at.forwardRef((t,n)=>{const{_asChild:r,...i}=t,{type:l,ref:u,...f}=xC(i,n),h=typeof i.children=="function"?i.children({isActive:f["data-status"]==="active"}):i.children;return typeof r>"u"&&delete f.disabled,at.createElement(r||"a",{...f,ref:u},h)});function DC(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}let kC=class extends nE{constructor(n){super(n),this.useMatch=r=>vr({select:r?.select,from:this.id,structuralSharing:r?.structuralSharing}),this.useRouteContext=r=>vr({...r,from:this.id,select:i=>r?.select?r.select(i.context):i.context}),this.useSearch=r=>oE({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useParams=r=>iE({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useLoaderDeps=r=>sE({...r,from:this.id}),this.useLoaderData=r=>rE({...r,from:this.id}),this.useNavigate=()=>lE({from:this.fullPath}),this.Link=Ht.forwardRef((r,i)=>W.jsx(fE,{ref:i,from:this.fullPath,...r})),this.$$typeof=Symbol.for("react.memo")}};function NC(t){return new kC(t)}function zC(){return t=>$C(t)}class LC extends IA{constructor(n){super(n),this.useMatch=r=>vr({select:r?.select,from:this.id,structuralSharing:r?.structuralSharing}),this.useRouteContext=r=>vr({...r,from:this.id,select:i=>r?.select?r.select(i.context):i.context}),this.useSearch=r=>oE({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useParams=r=>iE({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useLoaderDeps=r=>sE({...r,from:this.id}),this.useLoaderData=r=>rE({...r,from:this.id}),this.useNavigate=()=>lE({from:this.fullPath}),this.Link=Ht.forwardRef((r,i)=>W.jsx(fE,{ref:i,from:this.fullPath,...r})),this.$$typeof=Symbol.for("react.memo")}}function $C(t){return new LC(t)}function Vi(t){return new UC(t,{silent:!0}).createRoute}class UC{constructor(n,r){this.path=n,this.createRoute=i=>{this.silent;const l=NC(i);return l.isRoot=!1,l},this.silent=r?.silent}}function FC(t){return typeof t?.message!="string"?!1:t.message.startsWith("Failed to fetch dynamically imported module")||t.message.startsWith("error loading dynamically imported module")||t.message.startsWith("Importing a module script failed")}function Gi(t,n,r){let i,l,u,f;const h=()=>typeof document>"u"&&r?.()===!1?(l=()=>null,Promise.resolve()):(i||(i=t().then(p=>{i=void 0,l=p[n]}).catch(p=>{if(u=p,FC(u)&&u instanceof Error&&typeof window<"u"&&typeof sessionStorage<"u"){const g=`tanstack_router_reload:${u.message}`;sessionStorage.getItem(g)||(sessionStorage.setItem(g,"1"),f=!0)}})),i),m=function(g){if(f)throw window.location.reload(),new Promise(()=>{});if(u)throw u;if(!l)throw h();return r?.()===!1?W.jsx(sC,{fallback:W.jsx(qy,{}),children:at.createElement(l,g)}):at.createElement(l,g)};return m.preload=h,m}const jC=t=>new BC(t);class BC extends FA{constructor(n){super(n)}}function qC({router:t,children:n,...r}){t.update({...t.options,...r,context:{...t.options.context,...r.context}});const i=aE(),l=W.jsx(i.Provider,{value:t,children:n});return t.options.Wrap?W.jsx(t.options.Wrap,{children:l}):l}function HC({router:t,...n}){return W.jsx(qC,{router:t,...n,children:W.jsx(wC,{})})}function dE({tag:t,attrs:n,children:r}){switch(t){case"title":return W.jsx("title",{...n,suppressHydrationWarning:!0,children:r});case"meta":return W.jsx("meta",{...n,suppressHydrationWarning:!0});case"link":return W.jsx("link",{...n,suppressHydrationWarning:!0});case"style":return W.jsx("style",{...n,dangerouslySetInnerHTML:{__html:r}});case"script":return n&&n.src?W.jsx("script",{...n,suppressHydrationWarning:!0}):typeof r=="string"?W.jsx("script",{...n,dangerouslySetInnerHTML:{__html:r},suppressHydrationWarning:!0}):null;default:return null}}const IC=()=>{const t=ln(),n=me({select:f=>f.matches.map(h=>h.meta).filter(Boolean)}),r=at.useMemo(()=>{const f=[],h={};let m;return[...n].reverse().forEach(p=>{[...p].reverse().forEach(g=>{if(g)if(g.title)m||(m={tag:"title",children:g.title});else{const v=g.name??g.property;if(v){if(h[v])return;h[v]=!0}f.push({tag:"meta",attrs:{...g}})}})}),m&&f.push(m),f.reverse(),f},[n]),i=me({select:f=>f.matches.map(h=>h.links).filter(Boolean).flat(1).map(h=>({tag:"link",attrs:{...h}})),structuralSharing:!0}),l=me({select:f=>{const h=[];return f.matches.map(m=>t.looseRoutesById[m.routeId]).forEach(m=>{var p,g,v,b;return(b=(v=(g=(p=t.ssr)==null?void 0:p.manifest)==null?void 0:g.routes[m.id])==null?void 0:v.preloads)==null?void 0:b.filter(Boolean).forEach(O=>{h.push({tag:"link",attrs:{rel:"modulepreload",href:O}})})}),h},structuralSharing:!0}),u=me({select:f=>f.matches.map(h=>h.headScripts).flat(1).filter(Boolean).map(({children:h,...m})=>({tag:"script",attrs:{...m},children:h})),structuralSharing:!0});return VC([...r,...l,...i,...u],f=>JSON.stringify(f))};function PC(){return IC().map(n=>at.createElement(dE,{...n,key:`tsr-meta-${JSON.stringify(n)}`}))}function VC(t,n){const r=new Set;return t.filter(i=>{const l=n(i);return r.has(l)?!1:(r.add(l),!0)})}const GC=()=>{const t=ln(),n=me({select:l=>{var u;const f=[],h=(u=t.ssr)==null?void 0:u.manifest;return h?(l.matches.map(m=>t.looseRoutesById[m.routeId]).forEach(m=>{var p,g;return(g=(p=h.routes[m.id])==null?void 0:p.assets)==null?void 0:g.filter(v=>v.tag==="script").forEach(v=>{f.push({tag:"script",attrs:v.attrs,children:v.children})})}),f):[]},structuralSharing:!0}),{scripts:r}=me({select:l=>({scripts:l.matches.map(u=>u.scripts).flat(1).filter(Boolean).map(({children:u,...f})=>({tag:"script",attrs:{...f,suppressHydrationWarning:!0},children:u}))})}),i=[...r,...n];return W.jsx(W.Fragment,{children:i.map((l,u)=>at.createElement(dE,{...l,key:`tsr-scripts-${l.tag}-${u}`}))})};let $u;function KC(t){return $u||(t.router.state.matches.length?$u=Promise.resolve():$u=PA(t.router)),W.jsx(nC,{promise:$u,children:()=>W.jsx(HC,{router:t.router})})}var Pf=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Vf=typeof window>"u"||"Deno"in globalThis;function kn(){}function QC(t,n){return typeof t=="function"?t(n):t}function YC(t){return typeof t=="number"&&t>=0&&t!==1/0}function XC(t,n){return Math.max(t+(n||0)-Date.now(),0)}function TS(t,n){return typeof t=="function"?t(n):t}function ZC(t,n){return typeof t=="function"?t(n):t}function OS(t,n){const{type:r="all",exact:i,fetchStatus:l,predicate:u,queryKey:f,stale:h}=t;if(f){if(i){if(n.queryHash!==Hy(f,n.options))return!1}else if(!Dl(n.queryKey,f))return!1}if(r!=="all"){const m=n.isActive();if(r==="active"&&!m||r==="inactive"&&m)return!1}return!(typeof h=="boolean"&&n.isStale()!==h||l&&l!==n.state.fetchStatus||u&&!u(n))}function RS(t,n){const{exact:r,status:i,predicate:l,mutationKey:u}=t;if(u){if(!n.options.mutationKey)return!1;if(r){if(rs(n.options.mutationKey)!==rs(u))return!1}else if(!Dl(n.options.mutationKey,u))return!1}return!(i&&n.state.status!==i||l&&!l(n))}function Hy(t,n){return(n?.queryKeyHashFn||rs)(t)}function rs(t){return JSON.stringify(t,(n,r)=>Fp(r)?Object.keys(r).sort().reduce((i,l)=>(i[l]=r[l],i),{}):r)}function Dl(t,n){return t===n?!0:typeof t!=typeof n?!1:t&&n&&typeof t=="object"&&typeof n=="object"?Object.keys(n).every(r=>Dl(t[r],n[r])):!1}function hE(t,n){if(t===n)return t;const r=MS(t)&&MS(n);if(r||Fp(t)&&Fp(n)){const i=r?t:Object.keys(t),l=i.length,u=r?n:Object.keys(n),f=u.length,h=r?[]:{};let m=0;for(let p=0;p<f;p++){const g=r?p:u[p];(!r&&i.includes(g)||r)&&t[g]===void 0&&n[g]===void 0?(h[g]=void 0,m++):(h[g]=hE(t[g],n[g]),h[g]===t[g]&&t[g]!==void 0&&m++)}return l===f&&m===l?t:h}return n}function e7(t,n){if(!n||Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}function MS(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function Fp(t){if(!wS(t))return!1;const n=t.constructor;if(n===void 0)return!0;const r=n.prototype;return!(!wS(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function wS(t){return Object.prototype.toString.call(t)==="[object Object]"}function JC(t){return new Promise(n=>{setTimeout(n,t)})}function WC(t,n,r){return typeof r.structuralSharing=="function"?r.structuralSharing(t,n):r.structuralSharing!==!1?hE(t,n):n}function tx(t,n,r=0){const i=[...t,n];return r&&i.length>r?i.slice(1):i}function ex(t,n,r=0){const i=[n,...t];return r&&i.length>r?i.slice(0,-1):i}var Iy=Symbol();function mE(t,n){return!t.queryFn&&n?.initialPromise?()=>n.initialPromise:!t.queryFn||t.queryFn===Iy?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}function n7(t,n){return typeof t=="function"?t(...n):!!t}var nx=class extends Pf{#t;#e;#n;constructor(){super(),this.#n=t=>{if(!Vf&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(n=>{typeof n=="boolean"?this.setFocused(n):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){return typeof this.#t=="boolean"?this.#t:globalThis.document?.visibilityState!=="hidden"}},pE=new nx,ax=class extends Pf{#t=!0;#e;#n;constructor(){super(),this.#n=t=>{if(!Vf&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#t!==t&&(this.#t=t,this.listeners.forEach(r=>{r(t)}))}isOnline(){return this.#t}},cf=new ax;function rx(){let t,n;const r=new Promise((l,u)=>{t=l,n=u});r.status="pending",r.catch(()=>{});function i(l){Object.assign(r,l),delete r.resolve,delete r.reject}return r.resolve=l=>{i({status:"fulfilled",value:l}),t(l)},r.reject=l=>{i({status:"rejected",reason:l}),n(l)},r}function sx(t){let n;if(t.then(r=>(n=r,r))?.catch(kn),n!==void 0)return{data:n}}function ix(t){return Math.min(1e3*2**t,3e4)}function yE(t){return(t??"online")==="online"?cf.isOnline():!0}var gE=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function bp(t){return t instanceof gE}function vE(t){let n=!1,r=0,i=!1,l;const u=rx(),f=A=>{i||(b(new gE(A)),t.abort?.())},h=()=>{n=!0},m=()=>{n=!1},p=()=>pE.isFocused()&&(t.networkMode==="always"||cf.isOnline())&&t.canRun(),g=()=>yE(t.networkMode)&&t.canRun(),v=A=>{i||(i=!0,t.onSuccess?.(A),l?.(),u.resolve(A))},b=A=>{i||(i=!0,t.onError?.(A),l?.(),u.reject(A))},O=()=>new Promise(A=>{l=j=>{(i||p())&&A(j)},t.onPause?.()}).then(()=>{l=void 0,i||t.onContinue?.()}),R=()=>{if(i)return;let A;const j=r===0?t.initialPromise:void 0;try{A=j??t.fn()}catch(U){A=Promise.reject(U)}Promise.resolve(A).then(v).catch(U=>{if(i)return;const B=t.retry??(Vf?0:3),q=t.retryDelay??ix,G=typeof q=="function"?q(r,U):q,Z=B===!0||typeof B=="number"&&r<B||typeof B=="function"&&B(r,U);if(n||!Z){b(U);return}r++,t.onFail?.(r,U),JC(G).then(()=>p()?void 0:O()).then(()=>{n?b(U):R()})})};return{promise:u,cancel:f,continue:()=>(l?.(),u),cancelRetry:h,continueRetry:m,canStart:g,start:()=>(g()?R():O().then(R),u)}}var ox=t=>setTimeout(t,0);function lx(){let t=[],n=0,r=h=>{h()},i=h=>{h()},l=ox;const u=h=>{n?t.push(h):l(()=>{r(h)})},f=()=>{const h=t;t=[],h.length&&l(()=>{i(()=>{h.forEach(m=>{r(m)})})})};return{batch:h=>{let m;n++;try{m=h()}finally{n--,n||f()}return m},batchCalls:h=>(...m)=>{u(()=>{h(...m)})},schedule:u,setNotifyFunction:h=>{r=h},setBatchNotifyFunction:h=>{i=h},setScheduler:h=>{l=h}}}var Ye=lx(),_E=class{#t;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),YC(this.gcTime)&&(this.#t=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(Vf?1/0:5*60*1e3))}clearGcTimeout(){this.#t&&(clearTimeout(this.#t),this.#t=void 0)}},cx=class extends _E{#t;#e;#n;#r;#a;#i;#o;constructor(t){super(),this.#o=!1,this.#i=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#r=t.client,this.#n=this.#r.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#t=fx(this.options),this.state=t.state??this.#t,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#a?.promise}setOptions(t){this.options={...this.#i,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#n.remove(this)}setData(t,n){const r=WC(this.state.data,t,this.options);return this.#s({data:r,type:"success",dataUpdatedAt:n?.updatedAt,manual:n?.manual}),r}setState(t,n){this.#s({type:"setState",state:t,setStateOptions:n})}cancel(t){const n=this.#a?.promise;return this.#a?.cancel(t),n?n.then(kn).catch(kn):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#t)}isActive(){return this.observers.some(t=>ZC(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Iy||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!XC(this.state.dataUpdatedAt,t)}onFocus(){this.observers.find(n=>n.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#a?.continue()}onOnline(){this.observers.find(n=>n.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#a?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(this.#a&&(this.#o?this.#a.cancel({revert:!0}):this.#a.cancelRetry()),this.scheduleGc()),this.#n.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#s({type:"invalidate"})}fetch(t,n){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&n?.cancelRefetch)this.cancel({silent:!0});else if(this.#a)return this.#a.continueRetry(),this.#a.promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(m=>m.options.queryFn);h&&this.setOptions(h.options)}const r=new AbortController,i=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(this.#o=!0,r.signal)})},l=()=>{const h=mE(this.options,n),m={client:this.#r,queryKey:this.queryKey,meta:this.meta};return i(m),this.#o=!1,this.options.persister?this.options.persister(h,m,this):h(m)},u={fetchOptions:n,options:this.options,queryKey:this.queryKey,client:this.#r,state:this.state,fetchFn:l};i(u),this.options.behavior?.onFetch(u,this),this.#e=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==u.fetchOptions?.meta)&&this.#s({type:"fetch",meta:u.fetchOptions?.meta});const f=h=>{bp(h)&&h.silent||this.#s({type:"error",error:h}),bp(h)||(this.#n.config.onError?.(h,this),this.#n.config.onSettled?.(this.state.data,h,this)),this.scheduleGc()};return this.#a=vE({initialPromise:n?.initialPromise,fn:u.fetchFn,abort:r.abort.bind(r),onSuccess:h=>{if(h===void 0){f(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(m){f(m);return}this.#n.config.onSuccess?.(h,this),this.#n.config.onSettled?.(h,this.state.error,this),this.scheduleGc()},onError:f,onFail:(h,m)=>{this.#s({type:"failed",failureCount:h,error:m})},onPause:()=>{this.#s({type:"pause"})},onContinue:()=>{this.#s({type:"continue"})},retry:u.options.retry,retryDelay:u.options.retryDelay,networkMode:u.options.networkMode,canRun:()=>!0}),this.#a.start()}#s(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...ux(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const i=t.error;return bp(i)&&i.revert&&this.#e?{...this.#e,fetchStatus:"idle"}:{...r,error:i,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Ye.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),this.#n.notify({query:this,type:"updated",action:t})})}};function ux(t,n){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:yE(n.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function fx(t){const n=typeof t.initialData=="function"?t.initialData():t.initialData,r=n!==void 0,i=r?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:n,dataUpdateCount:0,dataUpdatedAt:r?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var dx=class extends Pf{constructor(t={}){super(),this.config=t,this.#t=new Map}#t;build(t,n,r){const i=n.queryKey,l=n.queryHash??Hy(i,n);let u=this.get(l);return u||(u=new cx({client:t,queryKey:i,queryHash:l,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(i)}),this.add(u)),u}add(t){this.#t.has(t.queryHash)||(this.#t.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=this.#t.get(t.queryHash);n&&(t.destroy(),n===t&&this.#t.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ye.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#t.get(t)}getAll(){return[...this.#t.values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>OS(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>OS(t,r)):n}notify(t){Ye.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Ye.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Ye.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},hx=class extends _E{#t;#e;#n;constructor(t){super(),this.mutationId=t.mutationId,this.#e=t.mutationCache,this.#t=[],this.state=t.state||mx(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#t.includes(t)||(this.#t.push(t),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#t=this.#t.filter(n=>n!==t),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#t.length||(this.state.status==="pending"?this.scheduleGc():this.#e.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(t){const n=()=>{this.#r({type:"continue"})};this.#n=vE({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(l,u)=>{this.#r({type:"failed",failureCount:l,error:u})},onPause:()=>{this.#r({type:"pause"})},onContinue:n,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});const r=this.state.status==="pending",i=!this.#n.canStart();try{if(r)n();else{this.#r({type:"pending",variables:t,isPaused:i}),await this.#e.config.onMutate?.(t,this);const u=await this.options.onMutate?.(t);u!==this.state.context&&this.#r({type:"pending",context:u,variables:t,isPaused:i})}const l=await this.#n.start();return await this.#e.config.onSuccess?.(l,t,this.state.context,this),await this.options.onSuccess?.(l,t,this.state.context),await this.#e.config.onSettled?.(l,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(l,null,t,this.state.context),this.#r({type:"success",data:l}),l}catch(l){try{throw await this.#e.config.onError?.(l,t,this.state.context,this),await this.options.onError?.(l,t,this.state.context),await this.#e.config.onSettled?.(void 0,l,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,l,t,this.state.context),l}finally{this.#r({type:"error",error:l})}}finally{this.#e.runNext(this)}}#r(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Ye.batch(()=>{this.#t.forEach(r=>{r.onMutationUpdate(t)}),this.#e.notify({mutation:this,type:"updated",action:t})})}};function mx(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var px=class extends Pf{constructor(t={}){super(),this.config=t,this.#t=new Set,this.#e=new Map,this.#n=0}#t;#e;#n;build(t,n,r){const i=new hx({mutationCache:this,mutationId:++this.#n,options:t.defaultMutationOptions(n),state:r});return this.add(i),i}add(t){this.#t.add(t);const n=Uu(t);if(typeof n=="string"){const r=this.#e.get(n);r?r.push(t):this.#e.set(n,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#t.delete(t)){const n=Uu(t);if(typeof n=="string"){const r=this.#e.get(n);if(r)if(r.length>1){const i=r.indexOf(t);i!==-1&&r.splice(i,1)}else r[0]===t&&this.#e.delete(n)}}this.notify({type:"removed",mutation:t})}canRun(t){const n=Uu(t);if(typeof n=="string"){const i=this.#e.get(n)?.find(l=>l.state.status==="pending");return!i||i===t}else return!0}runNext(t){const n=Uu(t);return typeof n=="string"?this.#e.get(n)?.find(i=>i!==t&&i.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){Ye.batch(()=>{this.#t.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#t.clear(),this.#e.clear()})}getAll(){return Array.from(this.#t)}find(t){const n={exact:!0,...t};return this.getAll().find(r=>RS(n,r))}findAll(t={}){return this.getAll().filter(n=>RS(t,n))}notify(t){Ye.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Ye.batch(()=>Promise.all(t.map(n=>n.continue().catch(kn))))}};function Uu(t){return t.options.scope?.id}function AS(t){return{onFetch:(n,r)=>{const i=n.options,l=n.fetchOptions?.meta?.fetchMore?.direction,u=n.state.data?.pages||[],f=n.state.data?.pageParams||[];let h={pages:[],pageParams:[]},m=0;const p=async()=>{let g=!1;const v=R=>{Object.defineProperty(R,"signal",{enumerable:!0,get:()=>(n.signal.aborted?g=!0:n.signal.addEventListener("abort",()=>{g=!0}),n.signal)})},b=mE(n.options,n.fetchOptions),O=async(R,A,j)=>{if(g)return Promise.reject();if(A==null&&R.pages.length)return Promise.resolve(R);const U={client:n.client,queryKey:n.queryKey,pageParam:A,direction:j?"backward":"forward",meta:n.options.meta};v(U);const B=await b(U),{maxPages:q}=n.options,G=j?ex:tx;return{pages:G(R.pages,B,q),pageParams:G(R.pageParams,A,q)}};if(l&&u.length){const R=l==="backward",A=R?yx:CS,j={pages:u,pageParams:f},U=A(i,j);h=await O(j,U,R)}else{const R=t??u.length;do{const A=m===0?f[0]??i.initialPageParam:CS(i,h);if(m>0&&A==null)break;h=await O(h,A),m++}while(m<R)}return h};n.options.persister?n.fetchFn=()=>n.options.persister?.(p,{client:n.client,queryKey:n.queryKey,meta:n.options.meta,signal:n.signal},r):n.fetchFn=p}}}function CS(t,{pages:n,pageParams:r}){const i=n.length-1;return n.length>0?t.getNextPageParam(n[i],n,r[i],r):void 0}function yx(t,{pages:n,pageParams:r}){return n.length>0?t.getPreviousPageParam?.(n[0],n,r[0],r):void 0}var gx=class{#t;#e;#n;#r;#a;#i;#o;#s;constructor(t={}){this.#t=t.queryCache||new dx,this.#e=t.mutationCache||new px,this.#n=t.defaultOptions||{},this.#r=new Map,this.#a=new Map,this.#i=0}mount(){this.#i++,this.#i===1&&(this.#o=pE.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onFocus())}),this.#s=cf.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onOnline())}))}unmount(){this.#i--,this.#i===0&&(this.#o?.(),this.#o=void 0,this.#s?.(),this.#s=void 0)}isFetching(t){return this.#t.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#e.findAll({...t,status:"pending"}).length}getQueryData(t){const n=this.defaultQueryOptions({queryKey:t});return this.#t.get(n.queryHash)?.state.data}ensureQueryData(t){const n=this.defaultQueryOptions(t),r=this.#t.build(this,n),i=r.state.data;return i===void 0?this.fetchQuery(t):(t.revalidateIfStale&&r.isStaleByTime(TS(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(i))}getQueriesData(t){return this.#t.findAll(t).map(({queryKey:n,state:r})=>{const i=r.data;return[n,i]})}setQueryData(t,n,r){const i=this.defaultQueryOptions({queryKey:t}),u=this.#t.get(i.queryHash)?.state.data,f=QC(n,u);if(f!==void 0)return this.#t.build(this,i).setData(f,{...r,manual:!0})}setQueriesData(t,n,r){return Ye.batch(()=>this.#t.findAll(t).map(({queryKey:i})=>[i,this.setQueryData(i,n,r)]))}getQueryState(t){const n=this.defaultQueryOptions({queryKey:t});return this.#t.get(n.queryHash)?.state}removeQueries(t){const n=this.#t;Ye.batch(()=>{n.findAll(t).forEach(r=>{n.remove(r)})})}resetQueries(t,n){const r=this.#t;return Ye.batch(()=>(r.findAll(t).forEach(i=>{i.reset()}),this.refetchQueries({type:"active",...t},n)))}cancelQueries(t,n={}){const r={revert:!0,...n},i=Ye.batch(()=>this.#t.findAll(t).map(l=>l.cancel(r)));return Promise.all(i).then(kn).catch(kn)}invalidateQueries(t,n={}){return Ye.batch(()=>(this.#t.findAll(t).forEach(r=>{r.invalidate()}),t?.refetchType==="none"?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},n)))}refetchQueries(t,n={}){const r={...n,cancelRefetch:n.cancelRefetch??!0},i=Ye.batch(()=>this.#t.findAll(t).filter(l=>!l.isDisabled()).map(l=>{let u=l.fetch(void 0,r);return r.throwOnError||(u=u.catch(kn)),l.state.fetchStatus==="paused"?Promise.resolve():u}));return Promise.all(i).then(kn)}fetchQuery(t){const n=this.defaultQueryOptions(t);n.retry===void 0&&(n.retry=!1);const r=this.#t.build(this,n);return r.isStaleByTime(TS(n.staleTime,r))?r.fetch(n):Promise.resolve(r.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(kn).catch(kn)}fetchInfiniteQuery(t){return t.behavior=AS(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(kn).catch(kn)}ensureInfiniteQueryData(t){return t.behavior=AS(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return cf.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#t}getMutationCache(){return this.#e}getDefaultOptions(){return this.#n}setDefaultOptions(t){this.#n=t}setQueryDefaults(t,n){this.#r.set(rs(t),{queryKey:t,defaultOptions:n})}getQueryDefaults(t){const n=[...this.#r.values()],r={};return n.forEach(i=>{Dl(t,i.queryKey)&&Object.assign(r,i.defaultOptions)}),r}setMutationDefaults(t,n){this.#a.set(rs(t),{mutationKey:t,defaultOptions:n})}getMutationDefaults(t){const n=[...this.#a.values()],r={};return n.forEach(i=>{Dl(t,i.mutationKey)&&Object.assign(r,i.defaultOptions)}),r}defaultQueryOptions(t){if(t._defaulted)return t;const n={...this.#n.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return n.queryHash||(n.queryHash=Hy(n.queryKey,n)),n.refetchOnReconnect===void 0&&(n.refetchOnReconnect=n.networkMode!=="always"),n.throwOnError===void 0&&(n.throwOnError=!!n.suspense),!n.networkMode&&n.persister&&(n.networkMode="offlineFirst"),n.queryFn===Iy&&(n.enabled=!1),n}defaultMutationOptions(t){return t?._defaulted?t:{...this.#n.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#t.clear(),this.#e.clear()}};function bE(t){return t}function vx(t){return{mutationKey:t.options.mutationKey,state:t.state,...t.options.scope&&{scope:t.options.scope},...t.meta&&{meta:t.meta}}}function _x(t,n,r){return{dehydratedAt:Date.now(),state:{...t.state,...t.state.data!==void 0&&{data:n(t.state.data)}},queryKey:t.queryKey,queryHash:t.queryHash,...t.state.status==="pending"&&{promise:t.promise?.then(n).catch(i=>r(i)?Promise.reject(new Error("redacted")):Promise.reject(i))},...t.meta&&{meta:t.meta}}}function bx(t){return t.state.isPaused}function Sx(t){return t.state.status==="success"}function Ex(t){return!0}function xS(t,n={}){const r=n.shouldDehydrateMutation??t.getDefaultOptions().dehydrate?.shouldDehydrateMutation??bx,i=t.getMutationCache().getAll().flatMap(m=>r(m)?[vx(m)]:[]),l=n.shouldDehydrateQuery??t.getDefaultOptions().dehydrate?.shouldDehydrateQuery??Sx,u=n.shouldRedactErrors??t.getDefaultOptions().dehydrate?.shouldRedactErrors??Ex,f=n.serializeData??t.getDefaultOptions().dehydrate?.serializeData??bE,h=t.getQueryCache().getAll().flatMap(m=>l(m)?[_x(m,f,u)]:[]);return{mutations:i,queries:h}}function DS(t,n,r){if(typeof n!="object"||n===null)return;const i=t.getMutationCache(),l=t.getQueryCache(),u=t.getDefaultOptions().hydrate?.deserializeData??bE,f=n.mutations||[],h=n.queries||[];f.forEach(({state:m,...p})=>{i.build(t,{...t.getDefaultOptions().hydrate?.mutations,...r?.defaultOptions?.mutations,...p},m)}),h.forEach(({queryKey:m,state:p,queryHash:g,meta:v,promise:b,dehydratedAt:O})=>{const R=b?sx(b):void 0,A=p.data===void 0?R?.data:p.data,j=A===void 0?A:u(A);let U=l.get(g);const B=U?.state.status==="pending";if(U){const q=R&&O!==void 0&&O>U.state.dataUpdatedAt;if(p.dataUpdatedAt>U.state.dataUpdatedAt||q){const{fetchStatus:G,...Z}=p;U.setState({...Z,data:j})}}else U=l.build(t,{...t.getDefaultOptions().hydrate?.queries,...r?.defaultOptions?.queries,queryKey:m,queryHash:g,meta:v},{...p,data:j,fetchStatus:"idle",status:j!==void 0?"success":p.status});b&&!B&&(O===void 0||O>U.state.dataUpdatedAt)&&U.fetch(void 0,{initialPromise:Promise.resolve(b).then(u)})})}var SE=at.createContext(void 0),a7=t=>{const n=at.useContext(SE);if(!n)throw new Error("No QueryClient set, use QueryClientProvider to set one");return n},Tx=({client:t,children:n})=>(at.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),W.jsx(SE.Provider,{value:t,children:n})),Ox=function(){return null};const Rx=function(){return null};function EE(t){var n,r,i="";if(typeof t=="string"||typeof t=="number")i+=t;else if(typeof t=="object")if(Array.isArray(t)){var l=t.length;for(n=0;n<l;n++)t[n]&&(r=EE(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}function Zr(){for(var t,n,r=0,i="",l=arguments.length;r<l;r++)(t=arguments[r])&&(n=EE(t))&&(i&&(i+=" "),i+=n);return i}function Mx(t){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}Mx(`:root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}
`);var Jl=t=>typeof t=="number"&&!isNaN(t),ss=t=>typeof t=="string",$a=t=>typeof t=="function",wx=t=>ss(t)||Jl(t),jp=t=>ss(t)||$a(t)?t:null,Ax=(t,n)=>t===!1||Jl(t)&&t>0?t:n,Bp=t=>at.isValidElement(t)||ss(t)||$a(t)||Jl(t);function Cx(t,n,r=300){let{scrollHeight:i,style:l}=t;requestAnimationFrame(()=>{l.minHeight="initial",l.height=i+"px",l.transition=`all ${r}ms`,requestAnimationFrame(()=>{l.height="0",l.padding="0",l.margin="0",setTimeout(n,r)})})}function xx({enter:t,exit:n,appendPosition:r=!1,collapse:i=!0,collapseDuration:l=300}){return function({children:u,position:f,preventExitTransition:h,done:m,nodeRef:p,isIn:g,playToast:v}){let b=r?`${t}--${f}`:t,O=r?`${n}--${f}`:n,R=at.useRef(0);return at.useLayoutEffect(()=>{let A=p.current,j=b.split(" "),U=B=>{B.target===p.current&&(v(),A.removeEventListener("animationend",U),A.removeEventListener("animationcancel",U),R.current===0&&B.type!=="animationcancel"&&A.classList.remove(...j))};A.classList.add(...j),A.addEventListener("animationend",U),A.addEventListener("animationcancel",U)},[]),at.useEffect(()=>{let A=p.current,j=()=>{A.removeEventListener("animationend",j),i?Cx(A,m,l):m()};g||(h?j():(R.current=1,A.className+=` ${O}`,A.addEventListener("animationend",j)))},[g]),Ht.createElement(Ht.Fragment,null,u)}}function kS(t,n){return{content:TE(t.content,t.props),containerId:t.props.containerId,id:t.props.toastId,theme:t.props.theme,type:t.props.type,data:t.props.data||{},isLoading:t.props.isLoading,icon:t.props.icon,reason:t.removalReason,status:n}}function TE(t,n,r=!1){return at.isValidElement(t)&&!ss(t.type)?at.cloneElement(t,{closeToast:n.closeToast,toastProps:n,data:n.data,isPaused:r}):$a(t)?t({closeToast:n.closeToast,toastProps:n,data:n.data,isPaused:r}):t}function Dx({closeToast:t,theme:n,ariaLabel:r="close"}){return Ht.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:i=>{i.stopPropagation(),t(!0)},"aria-label":r},Ht.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},Ht.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function kx({delay:t,isRunning:n,closeToast:r,type:i="default",hide:l,className:u,controlledProgress:f,progress:h,rtl:m,isIn:p,theme:g}){let v=l||f&&h===0,b={animationDuration:`${t}ms`,animationPlayState:n?"running":"paused"};f&&(b.transform=`scaleX(${h})`);let O=Zr("Toastify__progress-bar",f?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${i}`,{"Toastify__progress-bar--rtl":m}),R=$a(u)?u({rtl:m,type:i,defaultClassName:O}):Zr(O,u),A={[f&&h>=1?"onTransitionEnd":"onAnimationEnd"]:f&&h<1?null:()=>{p&&r()}};return Ht.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":v},Ht.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${i}`}),Ht.createElement("div",{role:"progressbar","aria-hidden":v?"true":"false","aria-label":"notification timer",className:R,style:b,...A}))}var Nx=1,OE=()=>`${Nx++}`;function zx(t,n,r){let i=1,l=0,u=[],f=[],h=n,m=new Map,p=new Set,g=B=>(p.add(B),()=>p.delete(B)),v=()=>{f=Array.from(m.values()),p.forEach(B=>B())},b=({containerId:B,toastId:q,updateId:G})=>{let Z=B?B!==t:t!==1,K=m.has(q)&&G==null;return Z||K},O=(B,q)=>{m.forEach(G=>{var Z;(q==null||q===G.props.toastId)&&((Z=G.toggle)==null||Z.call(G,B))})},R=B=>{var q,G;(G=(q=B.props)==null?void 0:q.onClose)==null||G.call(q,B.removalReason),B.isActive=!1},A=B=>{if(B==null)m.forEach(R);else{let q=m.get(B);q&&R(q)}v()},j=()=>{l-=u.length,u=[]},U=B=>{var q,G;let{toastId:Z,updateId:K}=B.props,Q=K==null;B.staleId&&m.delete(B.staleId),B.isActive=!0,m.set(Z,B),v(),r(kS(B,Q?"added":"updated")),Q&&((G=(q=B.props).onOpen)==null||G.call(q))};return{id:t,props:h,observe:g,toggle:O,removeToast:A,toasts:m,clearQueue:j,buildToast:(B,q)=>{if(b(q))return;let{toastId:G,updateId:Z,data:K,staleId:Q,delay:N}=q,L=Z==null;L&&l++;let rt={...h,style:h.toastStyle,key:i++,...Object.fromEntries(Object.entries(q).filter(([Y,it])=>it!=null)),toastId:G,updateId:Z,data:K,isIn:!1,className:jp(q.className||h.toastClassName),progressClassName:jp(q.progressClassName||h.progressClassName),autoClose:q.isLoading?!1:Ax(q.autoClose,h.autoClose),closeToast(Y){m.get(G).removalReason=Y,A(G)},deleteToast(){let Y=m.get(G);if(Y!=null){if(r(kS(Y,"removed")),m.delete(G),l--,l<0&&(l=0),u.length>0){U(u.shift());return}v()}}};rt.closeButton=h.closeButton,q.closeButton===!1||Bp(q.closeButton)?rt.closeButton=q.closeButton:q.closeButton===!0&&(rt.closeButton=Bp(h.closeButton)?h.closeButton:!0);let tt={content:B,props:rt,staleId:Q};h.limit&&h.limit>0&&l>h.limit&&L?u.push(tt):Jl(N)?setTimeout(()=>{U(tt)},N):U(tt)},setProps(B){h=B},setToggle:(B,q)=>{let G=m.get(B);G&&(G.toggle=q)},isToastActive:B=>{var q;return(q=m.get(B))==null?void 0:q.isActive},getSnapshot:()=>f}}var Xe=new Map,kl=[],qp=new Set,Lx=t=>qp.forEach(n=>n(t)),RE=()=>Xe.size>0;function $x(){kl.forEach(t=>wE(t.content,t.options)),kl=[]}var Ux=(t,{containerId:n})=>{var r;return(r=Xe.get(n||1))==null?void 0:r.toasts.get(t)};function ME(t,n){var r;if(n)return!!((r=Xe.get(n))!=null&&r.isToastActive(t));let i=!1;return Xe.forEach(l=>{l.isToastActive(t)&&(i=!0)}),i}function Fx(t){if(!RE()){kl=kl.filter(n=>t!=null&&n.options.toastId!==t);return}if(t==null||wx(t))Xe.forEach(n=>{n.removeToast(t)});else if(t&&("containerId"in t||"id"in t)){let n=Xe.get(t.containerId);n?n.removeToast(t.id):Xe.forEach(r=>{r.removeToast(t.id)})}}var jx=(t={})=>{Xe.forEach(n=>{n.props.limit&&(!t.containerId||n.id===t.containerId)&&n.clearQueue()})};function wE(t,n){Bp(t)&&(RE()||kl.push({content:t,options:n}),Xe.forEach(r=>{r.buildToast(t,n)}))}function Bx(t){var n;(n=Xe.get(t.containerId||1))==null||n.setToggle(t.id,t.fn)}function AE(t,n){Xe.forEach(r=>{(n==null||!(n!=null&&n.containerId)||n?.containerId===r.id)&&r.toggle(t,n?.id)})}function qx(t){let n=t.containerId||1;return{subscribe(r){let i=zx(n,t,Lx);Xe.set(n,i);let l=i.observe(r);return $x(),()=>{l(),Xe.delete(n)}},setProps(r){var i;(i=Xe.get(n))==null||i.setProps(r)},getSnapshot(){var r;return(r=Xe.get(n))==null?void 0:r.getSnapshot()}}}function Hx(t){return qp.add(t),()=>{qp.delete(t)}}function Ix(t){return t&&(ss(t.toastId)||Jl(t.toastId))?t.toastId:OE()}function Wl(t,n){return wE(t,n),n.toastId}function Gf(t,n){return{...n,type:n&&n.type||t,toastId:Ix(n)}}function Kf(t){return(n,r)=>Wl(n,Gf(t,r))}function te(t,n){return Wl(t,Gf("default",n))}te.loading=(t,n)=>Wl(t,Gf("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...n}));function Px(t,{pending:n,error:r,success:i},l){let u;n&&(u=ss(n)?te.loading(n,l):te.loading(n.render,{...l,...n}));let f={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},h=(p,g,v)=>{if(g==null){te.dismiss(u);return}let b={type:p,...f,...l,data:v},O=ss(g)?{render:g}:g;return u?te.update(u,{...b,...O}):te(O.render,{...b,...O}),v},m=$a(t)?t():t;return m.then(p=>h("success",i,p)).catch(p=>h("error",r,p)),m}te.promise=Px;te.success=Kf("success");te.info=Kf("info");te.error=Kf("error");te.warning=Kf("warning");te.warn=te.warning;te.dark=(t,n)=>Wl(t,Gf("default",{theme:"dark",...n}));function Vx(t){Fx(t)}te.dismiss=Vx;te.clearWaitingQueue=jx;te.isActive=ME;te.update=(t,n={})=>{let r=Ux(t,n);if(r){let{props:i,content:l}=r,u={delay:100,...i,...n,toastId:n.toastId||t,updateId:OE()};u.toastId!==t&&(u.staleId=t);let f=u.render||l;delete u.render,Wl(f,u)}};te.done=t=>{te.update(t,{progress:1})};te.onChange=Hx;te.play=t=>AE(!0,t);te.pause=t=>AE(!1,t);function Gx(t){var n;let{subscribe:r,getSnapshot:i,setProps:l}=at.useRef(qx(t)).current;l(t);let u=(n=at.useSyncExternalStore(r,i,i))==null?void 0:n.slice();function f(h){if(!u)return[];let m=new Map;return t.newestOnTop&&u.reverse(),u.forEach(p=>{let{position:g}=p.props;m.has(g)||m.set(g,[]),m.get(g).push(p)}),Array.from(m,p=>h(p[0],p[1]))}return{getToastToRender:f,isToastActive:ME,count:u?.length}}function Kx(t){let[n,r]=at.useState(!1),[i,l]=at.useState(!1),u=at.useRef(null),f=at.useRef({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:h,pauseOnHover:m,closeToast:p,onClick:g,closeOnClick:v}=t;Bx({id:t.toastId,containerId:t.containerId,fn:r}),at.useEffect(()=>{if(t.pauseOnFocusLoss)return b(),()=>{O()}},[t.pauseOnFocusLoss]);function b(){document.hasFocus()||U(),window.addEventListener("focus",j),window.addEventListener("blur",U)}function O(){window.removeEventListener("focus",j),window.removeEventListener("blur",U)}function R(Q){if(t.draggable===!0||t.draggable===Q.pointerType){B();let N=u.current;f.canCloseOnClick=!0,f.canDrag=!0,N.style.transition="none",t.draggableDirection==="x"?(f.start=Q.clientX,f.removalDistance=N.offsetWidth*(t.draggablePercent/100)):(f.start=Q.clientY,f.removalDistance=N.offsetHeight*(t.draggablePercent===80?t.draggablePercent*1.5:t.draggablePercent)/100)}}function A(Q){let{top:N,bottom:L,left:rt,right:tt}=u.current.getBoundingClientRect();Q.nativeEvent.type!=="touchend"&&t.pauseOnHover&&Q.clientX>=rt&&Q.clientX<=tt&&Q.clientY>=N&&Q.clientY<=L?U():j()}function j(){r(!0)}function U(){r(!1)}function B(){f.didMove=!1,document.addEventListener("pointermove",G),document.addEventListener("pointerup",Z)}function q(){document.removeEventListener("pointermove",G),document.removeEventListener("pointerup",Z)}function G(Q){let N=u.current;if(f.canDrag&&N){f.didMove=!0,n&&U(),t.draggableDirection==="x"?f.delta=Q.clientX-f.start:f.delta=Q.clientY-f.start,f.start!==Q.clientX&&(f.canCloseOnClick=!1);let L=t.draggableDirection==="x"?`${f.delta}px, var(--y)`:`0, calc(${f.delta}px + var(--y))`;N.style.transform=`translate3d(${L},0)`,N.style.opacity=`${1-Math.abs(f.delta/f.removalDistance)}`}}function Z(){q();let Q=u.current;if(f.canDrag&&f.didMove&&Q){if(f.canDrag=!1,Math.abs(f.delta)>f.removalDistance){l(!0),t.closeToast(!0),t.collapseAll();return}Q.style.transition="transform 0.2s, opacity 0.2s",Q.style.removeProperty("transform"),Q.style.removeProperty("opacity")}}let K={onPointerDown:R,onPointerUp:A};return h&&m&&(K.onMouseEnter=U,t.stacked||(K.onMouseLeave=j)),v&&(K.onClick=Q=>{g&&g(Q),f.canCloseOnClick&&p(!0)}),{playToast:j,pauseToast:U,isRunning:n,preventExitTransition:i,toastRef:u,eventHandlers:K}}var Qx=typeof window<"u"?at.useLayoutEffect:at.useEffect,Qf=({theme:t,type:n,isLoading:r,...i})=>Ht.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:t==="colored"?"currentColor":`var(--toastify-icon-color-${n})`,...i});function Yx(t){return Ht.createElement(Qf,{...t},Ht.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function Xx(t){return Ht.createElement(Qf,{...t},Ht.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function Zx(t){return Ht.createElement(Qf,{...t},Ht.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function Jx(t){return Ht.createElement(Qf,{...t},Ht.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function Wx(){return Ht.createElement("div",{className:"Toastify__spinner"})}var Hp={info:Xx,warning:Yx,success:Zx,error:Jx,spinner:Wx},t3=t=>t in Hp;function e3({theme:t,type:n,isLoading:r,icon:i}){let l=null,u={theme:t,type:n};return i===!1||($a(i)?l=i({...u,isLoading:r}):at.isValidElement(i)?l=at.cloneElement(i,u):r?l=Hp.spinner():t3(n)&&(l=Hp[n](u))),l}var n3=t=>{let{isRunning:n,preventExitTransition:r,toastRef:i,eventHandlers:l,playToast:u}=Kx(t),{closeButton:f,children:h,autoClose:m,onClick:p,type:g,hideProgressBar:v,closeToast:b,transition:O,position:R,className:A,style:j,progressClassName:U,updateId:B,role:q,progress:G,rtl:Z,toastId:K,deleteToast:Q,isIn:N,isLoading:L,closeOnClick:rt,theme:tt,ariaLabel:Y}=t,it=Zr("Toastify__toast",`Toastify__toast-theme--${tt}`,`Toastify__toast--${g}`,{"Toastify__toast--rtl":Z},{"Toastify__toast--close-on-click":rt}),St=$a(A)?A({rtl:Z,position:R,type:g,defaultClassName:it}):Zr(it,A),ut=e3(t),C=!!G||!m,I={closeToast:b,type:g,theme:tt},st=null;return f===!1||($a(f)?st=f(I):at.isValidElement(f)?st=at.cloneElement(f,I):st=Dx(I)),Ht.createElement(O,{isIn:N,done:Q,position:R,preventExitTransition:r,nodeRef:i,playToast:u},Ht.createElement("div",{id:K,tabIndex:0,onClick:p,"data-in":N,className:St,...l,style:j,ref:i,...N&&{role:q,"aria-label":Y}},ut!=null&&Ht.createElement("div",{className:Zr("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!L})},ut),TE(h,t,!n),st,!t.customProgressBar&&Ht.createElement(kx,{...B&&!C?{key:`p-${B}`}:{},rtl:Z,theme:tt,delay:m,isRunning:n,isIn:N,closeToast:b,hide:v,type:g,className:U,controlledProgress:C,progress:G||0})))},a3=(t,n=!1)=>({enter:`Toastify--animate Toastify__${t}-enter`,exit:`Toastify--animate Toastify__${t}-exit`,appendPosition:n}),r3=xx(a3("bounce",!0)),s3={position:"top-right",transition:r3,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:t=>t.altKey&&t.code==="KeyT"};function i3(t){let n={...s3,...t},r=t.stacked,[i,l]=at.useState(!0),u=at.useRef(null),{getToastToRender:f,isToastActive:h,count:m}=Gx(n),{className:p,style:g,rtl:v,containerId:b,hotKeys:O}=n;function R(j){let U=Zr("Toastify__toast-container",`Toastify__toast-container--${j}`,{"Toastify__toast-container--rtl":v});return $a(p)?p({position:j,rtl:v,defaultClassName:U}):Zr(U,jp(p))}function A(){r&&(l(!0),te.play())}return Qx(()=>{var j;if(r){let U=u.current.querySelectorAll('[data-in="true"]'),B=12,q=(j=n.position)==null?void 0:j.includes("top"),G=0,Z=0;Array.from(U).reverse().forEach((K,Q)=>{let N=K;N.classList.add("Toastify__toast--stacked"),Q>0&&(N.dataset.collapsed=`${i}`),N.dataset.pos||(N.dataset.pos=q?"top":"bot");let L=G*(i?.2:1)+(i?0:B*Q);N.style.setProperty("--y",`${q?L:L*-1}px`),N.style.setProperty("--g",`${B}`),N.style.setProperty("--s",`${1-(i?Z:0)}`),G+=N.offsetHeight,Z+=.025})}},[i,m,r]),at.useEffect(()=>{function j(U){var B;let q=u.current;O(U)&&((B=q.querySelector('[tabIndex="0"]'))==null||B.focus(),l(!1),te.pause()),U.key==="Escape"&&(document.activeElement===q||q!=null&&q.contains(document.activeElement))&&(l(!0),te.play())}return document.addEventListener("keydown",j),()=>{document.removeEventListener("keydown",j)}},[O]),Ht.createElement("section",{ref:u,className:"Toastify",id:b,onMouseEnter:()=>{r&&(l(!1),te.pause())},onMouseLeave:A,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":n["aria-label"]},f((j,U)=>{let B=U.length?{...g}:{...g,pointerEvents:"none"};return Ht.createElement("div",{tabIndex:-1,className:R(j),"data-stacked":r,style:B,key:`c-${j}`},U.map(({content:q,props:G})=>Ht.createElement(n3,{...G,stacked:r,collapseAll:A,isIn:h(G.toastId,G.containerId),key:`t-${G.key}`},q)))}))}const CE=at.createContext(void 0),o3=({children:t,service:n})=>W.jsx(CE.Provider,{value:n,children:t}),r7=()=>{const t=at.useContext(CE);if(!t)throw new Error("No service found");return t};async function l3(t,n,r){var i;const l=n[0];if(sa(l)&&l.method){const h=l,m=h.data instanceof FormData?"formData":"payload",p=new Headers({...m==="payload"?{"content-type":"application/json",accept:"application/json"}:{},...h.headers instanceof Headers?Object.fromEntries(h.headers.entries()):h.headers});if(h.method==="GET"){const b=tE({payload:Ge.stringify({data:h.data,context:h.context})});b&&(t.includes("?")?t+=`&${b}`:t+=`?${b}`)}t.includes("?")?t+="&createServerFn":t+="?createServerFn",h.response==="raw"&&(t+="&raw");const g=await r(t,{method:h.method,headers:p,signal:h.signal,...c3(h)}),v=await NS(g);if((i=v.headers.get("content-type"))!=null&&i.includes("application/json")){const b=Ge.decode(await v.json());if(In(b)||sn(b)||b instanceof Error)throw b;return b}return v}const u=await NS(await r(t,{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(n)})),f=u.headers.get("content-type");return f&&f.includes("application/json")?Ge.decode(await u.json()):u.text()}function c3(t){return t.method==="POST"?t.data instanceof FormData?(t.data.set("__TSR_CONTEXT",Ge.stringify(t.context)),{body:t.data}):{body:Ge.stringify({data:t.data??null,context:t.context})}:{}}async function NS(t){if(!t.ok){const n=t.headers.get("content-type");throw n&&n.includes("application/json")?Ge.decode(await t.json()):new Error(await t.text())}return t}function u3(t){return t.replace(/^\/|\/$/g,"")}const xE=(t,n)=>{const r=`/${u3(n)}/${t}`;return Object.assign((...l)=>l3(r,l,fetch),{url:r,functionId:t})},f3=xE("app_modules_auth_server_theme_ts--getThemeServerFn_createServerFn_handler","/_server"),d3=Tl().handler(f3),h3=xE("app_modules_auth_server_theme_ts--setThemeServerFn_createServerFn_handler","/_server"),m3=Tl({method:"POST"}).handler(h3),DE=at.createContext(null);function p3({children:t,theme:n}){const r=ln();function i(l){m3({data:l}),r.invalidate()}return W.jsx(DE,{value:{theme:n,setTheme:i},children:t})}function y3(){const t=at.use(DE);if(!t)throw new Error("useTheme called outside of ThemeProvider!");return t}const g3=t=>typeof t=="function",E=function(t,n){if(typeof t=="function")return function(){return t(arguments)?n.apply(this,arguments):r=>n(r,...arguments)};switch(t){case 0:case 1:throw new RangeError(`Invalid arity ${t}`);case 2:return function(r,i){return arguments.length>=2?n(r,i):function(l){return n(l,r)}};case 3:return function(r,i,l){return arguments.length>=3?n(r,i,l):function(u){return n(u,r,i)}};case 4:return function(r,i,l,u){return arguments.length>=4?n(r,i,l,u):function(f){return n(f,r,i,l)}};case 5:return function(r,i,l,u,f){return arguments.length>=5?n(r,i,l,u,f):function(h){return n(h,r,i,l,u)}};default:return function(){if(arguments.length>=t)return n.apply(this,arguments);const r=arguments;return function(i){return n(i,...r)}}}},Vt=t=>t,Yf=t=>()=>t,zS=Yf(!0),uf=Yf(!1),kE=Yf(void 0),Ip=kE;function x(t,n,r,i,l,u,f,h,m){switch(arguments.length){case 1:return t;case 2:return n(t);case 3:return r(n(t));case 4:return i(r(n(t)));case 5:return l(i(r(n(t))));case 6:return u(l(i(r(n(t)))));case 7:return f(u(l(i(r(n(t))))));case 8:return h(f(u(l(i(r(n(t)))))));case 9:return m(h(f(u(l(i(r(n(t))))))));default:{let p=arguments[0];for(let g=1;g<arguments.length;g++)p=arguments[g](p);return p}}}const Py=t=>(n,r)=>n===r||t(n,r),v3=E(2,(t,n)=>Py((r,i)=>t(n(r),n(i)))),_3=t=>Py((n,r)=>{if(n.length!==r.length)return!1;for(let i=0;i<n.length;i++)if(!t(n[i],r[i]))return!1;return!0});let b3="3.15.3";const Xf=()=>b3,LS=`effect/GlobalValue/globalStoreId/${Xf()}`;let dl;const $t=(t,n)=>(dl||(globalThis[LS]??=new Map,dl=globalThis[LS]),dl.has(t)||dl.set(t,n()),dl.get(t)),NE=t=>typeof t=="string",Pp=t=>typeof t=="number",s7=t=>typeof t=="boolean",S3=t=>typeof t=="bigint",i7=t=>typeof t=="symbol",Zf=g3,o7=t=>t===void 0,l7=t=>!1,zE=t=>typeof t=="object"&&t!==null,Jf=t=>zE(t)||Zf(t),Rt=E(2,(t,n)=>Jf(t)&&n in t),LE=E(2,(t,n)=>Rt(t,"_tag")&&t._tag===n),ui=t=>t==null,c7=t=>t!=null,u7=t=>t instanceof Date,$E=t=>Rt(t,Symbol.iterator),f7=t=>zE(t)&&!Array.isArray(t),UE=t=>Rt(t,"then")&&Zf(t.then),Wf=t=>`BUG: ${t} - please report an issue at https://github.com/Effect-TS/effect/issues`;let FE=class jE{self;called=!1;constructor(n){this.self=n}next(n){return this.called?{value:n,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(n){return{value:n,done:!0}}throw(n){throw n}[Symbol.iterator](){return new jE(this.self)}};const E3=335903614,T3=4150755663,O3=1481765933,R3=1284865837,M3=9007199254740992,w3=134217728;class A3{_state;constructor(n,r,i,l){return ui(r)&&ui(n)?(r=Math.random()*4294967295>>>0,n=0):ui(r)&&(r=n,n=0),ui(l)&&ui(i)?(l=this._state?this._state[3]:T3,i=this._state?this._state[2]:E3):ui(l)&&(l=i,i=0),this._state=new Int32Array([0,0,i>>>0,((l||0)|1)>>>0]),this._next(),$S(this._state,this._state[0],this._state[1],n>>>0,r>>>0),this._next(),this}getState(){return[this._state[0],this._state[1],this._state[2],this._state[3]]}setState(n){this._state[0]=n[0],this._state[1]=n[1],this._state[2]=n[2],this._state[3]=n[3]|1}integer(n){return Math.round(this.number()*Number.MAX_SAFE_INTEGER)%n}number(){const n=(this._next()&67108863)*1,r=(this._next()&134217727)*1;return(n*w3+r)/M3}_next(){const n=this._state[0]>>>0,r=this._state[1]>>>0;C3(this._state,n,r,O3,R3),$S(this._state,this._state[0],this._state[1],this._state[2],this._state[3]);let i=n>>>18,l=(r>>>18|n<<14)>>>0;i=(i^n)>>>0,l=(l^r)>>>0;const u=(l>>>27|i<<5)>>>0,f=n>>>27,h=(-f>>>0&31)>>>0;return(u>>>f|u<<h)>>>0}}function C3(t,n,r,i,l){let u=(r>>>16)*(l&65535)>>>0,f=(r&65535)*(l>>>16)>>>0,h=(r&65535)*(l&65535)>>>0,m=(r>>>16)*(l>>>16)+((f>>>16)+(u>>>16))>>>0;f=f<<16>>>0,h=h+f>>>0,h>>>0<f>>>0&&(m=m+1>>>0),u=u<<16>>>0,h=h+u>>>0,h>>>0<u>>>0&&(m=m+1>>>0),m=m+Math.imul(r,i)>>>0,m=m+Math.imul(n,l)>>>0,t[0]=m,t[1]=h}function $S(t,n,r,i,l){let u=n+i>>>0;const f=r+l>>>0;f>>>0<r>>>0&&(u=u+1|0),t[0]=u,t[1]=f}const Vp=Symbol.for("effect/Utils/YieldWrap");class tc{#t;constructor(n){this.#t=n}[Vp](){return this.#t}}function x3(t){if(typeof t=="object"&&t!==null&&Vp in t)return t[Vp]();throw new Error(Wf("yieldWrapGet"))}const Cn=$t("effect/Utils/isStructuralRegion",()=>({enabled:!1,tester:void 0})),BE={effect_internal_function:t=>t()},D3={effect_internal_function:t=>{try{return t()}finally{}}},k3=BE.effect_internal_function(()=>new Error().stack)?.includes("effect_internal_function")===!0,Be=k3?BE.effect_internal_function:D3.effect_internal_function,Sp=$t(Symbol.for("effect/Hash/randomHashCache"),()=>new WeakMap),At=Symbol.for("effect/Hash"),mt=t=>{if(Cn.enabled===!0)return 0;switch(typeof t){case"number":return Gy(t);case"bigint":return ve(t.toString(10));case"boolean":return ve(String(t));case"symbol":return ve(String(t));case"string":return ve(t);case"undefined":return ve("undefined");case"function":case"object":return t===null?ve("null"):t instanceof Date?mt(t.toISOString()):t instanceof URL?mt(t.href):N3(t)?t[At]():Vy(t);default:throw new Error(`BUG: unhandled typeof ${typeof t} - please report an issue at https://github.com/Effect-TS/effect/issues`)}},Vy=t=>(Sp.has(t)||Sp.set(t,Gy(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),Sp.get(t)),Lt=t=>n=>n*53^t,td=t=>t&3221225471|t>>>1&1073741824,N3=t=>Rt(t,At),Gy=t=>{if(t!==t||t===1/0)return 0;let n=t|0;for(n!==t&&(n^=t*4294967295);t>4294967295;)n^=t/=4294967295;return td(n)},ve=t=>{let n=5381,r=t.length;for(;r;)n=n*33^t.charCodeAt(--r);return td(n)},z3=(t,n)=>{let r=12289;for(let i=0;i<n.length;i++)r^=x(ve(n[i]),Lt(mt(t[n[i]])));return td(r)},qE=t=>z3(t,Object.keys(t)),ec=t=>{let n=6151;for(let r=0;r<t.length;r++)n=x(n,Lt(mt(t[r])));return td(n)},ue=function(){if(arguments.length===1){const r=arguments[0];return function(i){return Object.defineProperty(r,At,{value(){return i},enumerable:!1}),i}}const t=arguments[0],n=arguments[1];return Object.defineProperty(t,At,{value(){return n},enumerable:!1}),n},wt=Symbol.for("effect/Equal");function Ot(){return arguments.length===1?t=>ff(t,arguments[0]):ff(arguments[0],arguments[1])}function ff(t,n){if(t===n)return!0;const r=typeof t;if(r!==typeof n)return!1;if(r==="object"||r==="function"){if(t!==null&&n!==null){if(df(t)&&df(n))return mt(t)===mt(n)&&t[wt](n)?!0:Cn.enabled&&Cn.tester?Cn.tester(t,n):!1;if(t instanceof Date&&n instanceof Date)return t.toISOString()===n.toISOString();if(t instanceof URL&&n instanceof URL)return t.href===n.href}if(Cn.enabled){if(Array.isArray(t)&&Array.isArray(n))return t.length===n.length&&t.every((i,l)=>ff(i,n[l]));if(Object.getPrototypeOf(t)===Object.prototype&&Object.getPrototypeOf(t)===Object.prototype){const i=Object.keys(t),l=Object.keys(n);if(i.length===l.length){for(const u of i)if(!(u in n&&ff(t[u],n[u])))return Cn.tester?Cn.tester(t,n):!1;return!0}}return Cn.tester?Cn.tester(t,n):!1}}return Cn.enabled&&Cn.tester?Cn.tester(t,n):!1}const df=t=>Rt(t,wt),Ky=()=>Ot,re=Symbol.for("nodejs.util.inspect.custom"),pe=t=>{try{if(Rt(t,"toJSON")&&Zf(t.toJSON)&&t.toJSON.length===0)return t.toJSON();if(Array.isArray(t))return t.map(pe)}catch{return{}}return $3(t)},_e=t=>JSON.stringify(t,null,2),d7={toJSON(){return pe(this)},[re](){return this.toJSON()},toString(){return _e(this.toJSON())}};let h7=class{[re](){return this.toJSON()}toString(){return _e(this.toJSON())}};const Ai=(t,n=2)=>{if(typeof t=="string")return t;try{return typeof t=="object"?HE(t,n):String(t)}catch{return String(t)}},HE=(t,n)=>{let r=[];const i=JSON.stringify(t,(l,u)=>typeof u=="object"&&u!==null?r.includes(u)?void 0:r.push(u)&&(Jr.fiberRefs!==void 0&&IE(u)?u[Qy](Jr.fiberRefs):u):u,n);return r=void 0,i},Qy=Symbol.for("effect/Inspectable/Redactable"),IE=t=>typeof t=="object"&&t!==null&&Qy in t,Jr=$t("effect/Inspectable/redactableState",()=>({fiberRefs:void 0})),L3=(t,n)=>{const r=Jr.fiberRefs;Jr.fiberRefs=t;try{return n()}finally{Jr.fiberRefs=r}},$3=t=>IE(t)&&Jr.fiberRefs!==void 0?t[Qy](Jr.fiberRefs):t,Et=(t,n)=>{switch(n.length){case 0:return t;case 1:return n[0](t);case 2:return n[1](n[0](t));case 3:return n[2](n[1](n[0](t)));case 4:return n[3](n[2](n[1](n[0](t))));case 5:return n[4](n[3](n[2](n[1](n[0](t)))));case 6:return n[5](n[4](n[3](n[2](n[1](n[0](t))))));case 7:return n[6](n[5](n[4](n[3](n[2](n[1](n[0](t)))))));case 8:return n[7](n[6](n[5](n[4](n[3](n[2](n[1](n[0](t))))))));case 9:return n[8](n[7](n[6](n[5](n[4](n[3](n[2](n[1](n[0](t)))))))));default:{let r=t;for(let i=0,l=n.length;i<l;i++)r=n[i](r);return r}}},Ol="Async",ed="Commit",Ce="Failure",Gu="OnFailure",hf="OnSuccess",mf="OnSuccessAndFailure",xe="Success",PE="Sync",U3="Tag",Ki="UpdateRuntimeFlags",pf="While",Rl="Iterator",VE="WithRuntime",Ku="Yield",Yy="RevertFlags",F3=Symbol.for("effect/Effect"),j3=Symbol.for("effect/Stream"),B3=Symbol.for("effect/Sink"),q3=Symbol.for("effect/Channel"),Ci={_R:t=>t,_E:t=>t,_A:t=>t,_V:Xf()},H3={_A:t=>t,_In:t=>t,_L:t=>t,_E:t=>t,_R:t=>t},I3={_Env:t=>t,_InErr:t=>t,_InElem:t=>t,_InDone:t=>t,_OutErr:t=>t,_OutElem:t=>t,_OutDone:t=>t},nc={[F3]:Ci,[j3]:Ci,[B3]:H3,[q3]:I3,[wt](t){return this===t},[At](){return ue(this,Vy(this))},[Symbol.iterator](){return new FE(new tc(this))},pipe(){return Et(this,arguments)}},Xy={[At](){return ue(this,qE(this))},[wt](t){const n=Object.keys(this),r=Object.keys(t);if(n.length!==r.length)return!1;for(const i of n)if(!(i in t&&Ot(this[i],t[i])))return!1;return!0}},Qi={...nc,_op:ed},P3={...Qi,...Xy},V3=function(){function t(){}return t.prototype=Qi,t}(),GE=Symbol.for("effect/Option"),KE={...nc,[GE]:{_A:t=>t},[re](){return this.toJSON()},toString(){return _e(this.toJSON())}},G3=Object.assign(Object.create(KE),{_tag:"Some",_op:"Some",[wt](t){return QE(t)&&XE(t)&&Ot(this.value,t.value)},[At](){return ue(this,Lt(mt(this._tag))(mt(this.value)))},toJSON(){return{_id:"Option",_tag:this._tag,value:pe(this.value)}}}),K3=mt("None"),Q3=Object.assign(Object.create(KE),{_tag:"None",_op:"None",[wt](t){return QE(t)&&YE(t)},[At](){return K3},toJSON(){return{_id:"Option",_tag:this._tag}}}),QE=t=>Rt(t,GE),YE=t=>t._tag==="None",XE=t=>t._tag==="Some",ZE=Object.create(Q3),Gp=t=>{const n=Object.create(G3);return n.value=t,n},JE=Symbol.for("effect/Either"),WE={...nc,[JE]:{_R:t=>t},[re](){return this.toJSON()},toString(){return _e(this.toJSON())}},Y3=Object.assign(Object.create(WE),{_tag:"Right",_op:"Right",[wt](t){return Zy(t)&&eT(t)&&Ot(this.right,t.right)},[At](){return Lt(mt(this._tag))(mt(this.right))},toJSON(){return{_id:"Either",_tag:this._tag,right:pe(this.right)}}}),X3=Object.assign(Object.create(WE),{_tag:"Left",_op:"Left",[wt](t){return Zy(t)&&tT(t)&&Ot(this.left,t.left)},[At](){return Lt(mt(this._tag))(mt(this.left))},toJSON(){return{_id:"Either",_tag:this._tag,left:pe(this.left)}}}),Zy=t=>Rt(t,JE),tT=t=>t._tag==="Left",eT=t=>t._tag==="Right",Z3=t=>{const n=Object.create(X3);return n.left=t,n},J3=t=>{const n=Object.create(Y3);return n.right=t,n},Vn=J3,_r=Z3,p7=Zy,pi=tT,yi=eT,y7=E(2,(t,n)=>pi(t)?_r(n(t.left)):Vn(t.right)),g7=E(2,(t,n)=>yi(t)?Vn(n(t.right)):_r(t.left)),W3=E(2,(t,{onLeft:n,onRight:r})=>pi(t)?n(t.left):r(t.right)),tD=W3({onLeft:Vt,onRight:Vt}),eD=E(2,(t,n)=>{if(yi(t))return t.right;throw n(t.left)}),v7=eD(()=>new Error("getOrThrow called on a Left")),nT=t=>t.length>0,aT=t=>(n,r)=>n===r?0:t(n,r),nD=aT((t,n)=>t<n?-1:1),aD=E(2,(t,n)=>aT((r,i)=>t(n(r),n(i)))),rD=t=>E(2,(n,r)=>t(n,r)===1),yt=()=>ZE,Tt=Gp,$e=YE,ca=XE,Fa=E(2,(t,{onNone:n,onSome:r})=>$e(t)?n():r(t.value)),zn=E(2,(t,n)=>$e(t)?n():t.value),_7=E(2,(t,n)=>$e(t)?n():t),sD=E(2,(t,n)=>$e(t)?Tt(n()):t),nd=t=>t==null?yt():Tt(t),Pr=zn(kE),b7=t=>(...n)=>{try{return Tt(t(...n))}catch{return yt()}},Ml=E(2,(t,n)=>$e(t)?yt():Tt(n(t.value))),rT=E(2,(t,n)=>$e(t)?yt():n(t.value)),S7=E(2,(t,n)=>$e(t)?yt():nd(n(t.value))),iD=t=>E(2,(n,r)=>$e(n)?!1:t(n.value,r)),oD=Ky(),lD=iD(oD),E7=E(2,(t,n)=>$e(t)?!1:n(t.value)),cD=(...t)=>t,Jy=t=>new Array(t),uD=E(2,(t,n)=>{const r=Math.max(1,Math.floor(t)),i=new Array(r);for(let l=0;l<r;l++)i[l]=n(l);return i}),ce=t=>Array.isArray(t)?t:Array.from(t),fD=t=>Array.isArray(t)?t:[t],T7=E(2,(t,{onEmpty:n,onNonEmpty:r})=>on(t)?r(Ze(t),is(t)):n()),yf=E(2,(t,n)=>[n,...t]),dD=E(2,(t,n)=>[...t,n]),sT=E(2,(t,n)=>ce(t).concat(ce(n))),O7=Array.isArray,hD=t=>t.length===0,mD=hD,pD=nT,on=nT,iT=(t,n)=>t<0||t>=n.length,yD=(t,n)=>Math.floor(Math.min(Math.max(0,t),n.length)),gD=E(2,(t,n)=>{const r=Math.floor(n);return iT(r,t)?yt():Tt(t[r])}),oT=E(2,(t,n)=>{const r=Math.floor(n);if(iT(r,t))throw new Error(`Index ${r} out of bounds`);return t[r]}),wl=gD(0),Ze=oT(0),vD=t=>on(t)?Tt(lT(t)):yt(),lT=t=>t[t.length-1],is=t=>t.slice(1),_D=(t,n)=>{let r=0;for(const i of t){if(!n(i,r))break;r++}return r},bD=E(2,(t,n)=>OD(t,_D(t,n))),SD=E(2,(t,n)=>{const r=ce(t);return r.slice(yD(n,r),r.length)}),US=t=>Array.from(t).reverse(),gf=E(2,(t,n)=>{const r=Array.from(t);return r.sort(n),r}),FS=E(2,(t,n)=>ED(t,n,cD)),ED=E(3,(t,n,r)=>{const i=ce(t),l=ce(n);if(on(i)&&on(l)){const u=[r(Ze(i),Ze(l))],f=Math.min(i.length,l.length);for(let h=1;h<f;h++)u[h]=r(i[h],l[h]);return u}return[]}),TD=Ky(),OD=E(2,(t,n)=>{const r=Array.from(t),i=Math.floor(n);return on(r)?i>=1?RD(r,i):[[],r]:[r,[]]}),RD=E(2,(t,n)=>{const r=Math.max(1,Math.floor(n));return r>=t.length?[MD(t),[]]:[yf(t.slice(1,r),Ze(t)),t.slice(r)]}),MD=t=>t.slice(),wD=E(3,(t,n,r)=>{const i=ce(t),l=ce(n);return on(i)?on(l)?cT(r)(sT(i,l)):i:l}),Qu=E(2,(t,n)=>wD(t,n,TD)),os=()=>[],Hn=t=>[t],Wr=E(2,(t,n)=>t.map(n)),AD=E(2,(t,n)=>{if(mD(t))return[];const r=[];for(let i=0;i<t.length;i++){const l=n(t[i],i);for(let u=0;u<l.length;u++)r.push(l[u])}return r}),CD=AD(Vt),xD=E(2,(t,n)=>{const r=ce(t),i=[];for(let l=0;l<r.length;l++)n(r[l],l)&&i.push(r[l]);return i}),Wy=E(3,(t,n,r)=>ce(t).reduce((i,l,u)=>r(i,l,u),n)),jS=(t,n)=>{const r=[];let i=t,l;for(;ca(l=n(i));){const[u,f]=l.value;r.push(u),i=f}return r},tg=_3,cT=E(2,(t,n)=>{const r=ce(t);if(on(r)){const i=[Ze(r)],l=is(r);for(const u of l)i.every(f=>!n(u,f))&&i.push(u);return i}return[]}),DD=t=>cT(t,Ky()),Yi=E(2,(t,n)=>ce(t).join(n)),Nl=nD,kD=t=>t.replace(/[/\\^$*+?.()|[\]{}]/g,"\\$&"),uT=Symbol.for("effect/Context/Tag"),vf=Symbol.for("effect/Context/Reference"),ND="effect/STM",zD=Symbol.for(ND),ad={...nc,_op:"Tag",[zD]:Ci,[uT]:{_Service:t=>t,_Identifier:t=>t},toString(){return _e(this.toJSON())},toJSON(){return{_id:"Tag",key:this.key,stack:this.stack}},[re](){return this.toJSON()},of(t){return t},context(t){return hT(this,t)}},LD={...ad,[vf]:vf},$D=t=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=2;const r=new Error;Error.stackTraceLimit=n;const i=Object.create(ad);return Object.defineProperty(i,"stack",{get(){return r.stack}}),i.key=t,i},UD=t=>()=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=2;const r=new Error;Error.stackTraceLimit=n;function i(){}return Object.setPrototypeOf(i,ad),i.key=t,Object.defineProperty(i,"stack",{get(){return r.stack}}),i},FD=()=>(t,n)=>{const r=Error.stackTraceLimit;Error.stackTraceLimit=2;const i=new Error;Error.stackTraceLimit=r;function l(){}return Object.setPrototypeOf(l,LD),l.key=t,l.defaultValue=n.defaultValue,Object.defineProperty(l,"stack",{get(){return i.stack}}),l},fT=Symbol.for("effect/Context"),jD={[fT]:{_Services:t=>t},[wt](t){if(dT(t)&&this.unsafeMap.size===t.unsafeMap.size){for(const n of this.unsafeMap.keys())if(!t.unsafeMap.has(n)||!Ot(this.unsafeMap.get(n),t.unsafeMap.get(n)))return!1;return!0}return!1},[At](){return ue(this,Gy(this.unsafeMap.size))},pipe(){return Et(this,arguments)},toString(){return _e(this.toJSON())},toJSON(){return{_id:"Context",services:Array.from(this.unsafeMap).map(pe)}},[re](){return this.toJSON()}},xi=t=>{const n=Object.create(jD);return n.unsafeMap=t,n},BD=t=>{const n=new Error(`Service not found${t.key?`: ${String(t.key)}`:""}`);if(t.stack){const r=t.stack.split(`
`);if(r.length>2){const i=r[2].match(/at (.*)/);i&&(n.message=n.message+` (defined at ${i[1]})`)}}if(n.stack){const r=n.stack.split(`
`);r.splice(1,3),n.stack=r.join(`
`)}return n},dT=t=>Rt(t,fT),qD=t=>Rt(t,uT),HD=t=>Rt(t,vf),ID=xi(new Map),PD=()=>ID,hT=(t,n)=>xi(new Map([[t.key,n]])),VD=E(3,(t,n,r)=>{const i=new Map(t.unsafeMap);return i.set(n.key,r),xi(i)}),Ep=$t("effect/Context/defaultValueCache",()=>new Map),eg=t=>{if(Ep.has(t.key))return Ep.get(t.key);const n=t.defaultValue();return Ep.set(t.key,n),n},GD=(t,n)=>t.unsafeMap.has(n.key)?t.unsafeMap.get(n.key):eg(n),mT=E(2,(t,n)=>{if(!t.unsafeMap.has(n.key)){if(vf in n)return eg(n);throw BD(n)}return t.unsafeMap.get(n.key)}),KD=mT,QD=E(2,(t,n)=>t.unsafeMap.has(n.key)?Gp(t.unsafeMap.get(n.key)):HD(n)?Gp(eg(n)):ZE),YD=E(2,(t,n)=>{const r=new Map(t.unsafeMap);for(const[i,l]of n.unsafeMap)r.set(i,l);return xi(r)}),bs=$D,XD=dT,pT=qD,zl=PD,ng=hT,Gr=VD,ts=KD,yT=mT,Xi=QD,Zi=YD,R7=UD,rd=FD,gT=Symbol.for("effect/Chunk");function ZD(t,n,r,i,l){for(let u=n;u<Math.min(t.length,n+l);u++)r[i+u-n]=t[u];return r}const vT=[],JD=t=>Py((n,r)=>n.length===r.length&&la(n).every((i,l)=>t(i,es(r,l)))),WD=JD(Ot),tk={[gT]:{_A:t=>t},toString(){return _e(this.toJSON())},toJSON(){return{_id:"Chunk",values:la(this).map(pe)}},[re](){return this.toJSON()},[wt](t){return _T(t)&&WD(this,t)},[At](){return ue(this,ec(la(this)))},[Symbol.iterator](){switch(this.backing._tag){case"IArray":return this.backing.array[Symbol.iterator]();case"IEmpty":return vT[Symbol.iterator]();default:return la(this)[Symbol.iterator]()}},pipe(){return Et(this,arguments)}},we=t=>{const n=Object.create(tk);switch(n.backing=t,t._tag){case"IEmpty":{n.length=0,n.depth=0,n.left=n,n.right=n;break}case"IConcat":{n.length=t.left.length+t.right.length,n.depth=1+Math.max(t.left.depth,t.right.depth),n.left=t.left,n.right=t.right;break}case"IArray":{n.length=t.array.length,n.depth=0,n.left=aa,n.right=aa;break}case"ISingleton":{n.length=1,n.depth=0,n.left=aa,n.right=aa;break}case"ISlice":{n.length=t.length,n.depth=t.chunk.depth+1,n.left=aa,n.right=aa;break}}return n},_T=t=>Rt(t,gT),aa=we({_tag:"IEmpty"}),Yn=()=>aa,Tp=(...t)=>rk(t),Je=t=>we({_tag:"ISingleton",a:t}),bT=t=>_T(t)?t:Ji(ce(t)),Kp=(t,n,r)=>{switch(t.backing._tag){case"IArray":{ZD(t.backing.array,0,n,r,t.length);break}case"IConcat":{Kp(t.left,n,r),Kp(t.right,n,r+t.left.length);break}case"ISingleton":{n[r]=t.backing.a;break}case"ISlice":{let i=0,l=r;for(;i<t.length;)n[l]=es(t,i),i+=1,l+=1;break}}},ek=t=>{switch(t.backing._tag){case"IEmpty":return vT;case"IArray":return t.backing.array;default:{const n=new Array(t.length);return Kp(t,n,0),t.backing={_tag:"IArray",array:n},t.left=aa,t.right=aa,t.depth=0,n}}},la=ek,nk=t=>{switch(t.backing._tag){case"IEmpty":case"ISingleton":return t;case"IArray":return we({_tag:"IArray",array:US(t.backing.array)});case"IConcat":return we({_tag:"IConcat",left:ls(t.backing.right),right:ls(t.backing.left)});case"ISlice":return Ji(US(la(t)))}},ls=nk,ak=E(2,(t,n)=>n<0||n>=t.length?yt():Tt(es(t,n))),Ji=t=>t.length===0?Yn():t.length===1?Je(t[0]):we({_tag:"IArray",array:t}),rk=t=>Ji(t),es=E(2,(t,n)=>{switch(t.backing._tag){case"IEmpty":throw new Error("Index out of bounds");case"ISingleton":{if(n!==0)throw new Error("Index out of bounds");return t.backing.a}case"IArray":{if(n>=t.length||n<0)throw new Error("Index out of bounds");return t.backing.array[n]}case"IConcat":return n<t.left.length?es(t.left,n):es(t.right,n-t.left.length);case"ISlice":return es(t.backing.chunk,n+t.backing.offset)}}),sk=E(2,(t,n)=>Gn(t,Je(n))),Ln=E(2,(t,n)=>Gn(Je(n),t)),Qp=E(2,(t,n)=>{if(n<=0)return t;if(n>=t.length)return aa;switch(t.backing._tag){case"ISlice":return we({_tag:"ISlice",chunk:t.backing.chunk,offset:t.backing.offset+n,length:t.backing.length-n});case"IConcat":return n>t.left.length?Qp(t.right,n-t.left.length):we({_tag:"IConcat",left:Qp(t.left,n),right:t.right});default:return we({_tag:"ISlice",chunk:t,offset:n,length:t.length-n})}}),Gn=E(2,(t,n)=>{if(t.backing._tag==="IEmpty")return n;if(n.backing._tag==="IEmpty")return t;const r=n.depth-t.depth;if(Math.abs(r)<=1)return we({_tag:"IConcat",left:t,right:n});if(r<-1)if(t.left.depth>=t.right.depth){const i=Gn(t.right,n);return we({_tag:"IConcat",left:t.left,right:i})}else{const i=Gn(t.right.right,n);if(i.depth===t.depth-3){const l=we({_tag:"IConcat",left:t.right.left,right:i});return we({_tag:"IConcat",left:t.left,right:l})}else{const l=we({_tag:"IConcat",left:t.left,right:t.right.left});return we({_tag:"IConcat",left:l,right:i})}}else if(n.right.depth>=n.left.depth){const i=Gn(t,n.left);return we({_tag:"IConcat",left:i,right:n.right})}else{const i=Gn(t,n.left.left);if(i.depth===n.depth-3){const l=we({_tag:"IConcat",left:i,right:n.left.right});return we({_tag:"IConcat",left:l,right:n.right})}else{const l=we({_tag:"IConcat",left:n.left.right,right:n.right});return we({_tag:"IConcat",left:i,right:l})}}}),M7=E(2,(t,n)=>Ji(xD(t,n))),ik=t=>t.length===0,Ua=t=>t.length>0,ag=ak(0),ST=t=>es(t,0),oa=ST,w7=E(2,(t,n)=>t.backing._tag==="ISingleton"?Je(n(t.backing.a,0)):Ji(x(la(t),Wr((r,i)=>n(r,i))))),ka=t=>Qp(t,1),A7=Wy,Yp=Symbol.for("effect/Duration"),ET=BigInt(0),BS=BigInt(24),Fu=BigInt(60),Xp=BigInt(1e3),qS=BigInt(1e6),HS=BigInt(1e9),ok=/^(-?\d+(?:\.\d+)?)\s+(nanos?|micros?|millis?|seconds?|minutes?|hours?|days?|weeks?)$/,ua=t=>{if(TT(t))return t;if(Pp(t))return _f(t);if(S3(t))return Op(t);if(Array.isArray(t)&&t.length===2&&t.every(Pp))return t[0]===-1/0||t[1]===-1/0||Number.isNaN(t[0])||Number.isNaN(t[1])?OT:t[0]===1/0||t[1]===1/0?fk:Op(BigInt(Math.round(t[0]*1e9))+BigInt(Math.round(t[1])));if(NE(t)){const n=ok.exec(t);if(n){const[r,i,l]=n,u=Number(i);switch(l){case"nano":case"nanos":return Op(BigInt(i));case"micro":case"micros":return dk(BigInt(i));case"milli":case"millis":return _f(u);case"second":case"seconds":return hk(u);case"minute":case"minutes":return mk(u);case"hour":case"hours":return pk(u);case"day":case"days":return yk(u);case"week":case"weeks":return gk(u)}}}throw new Error("Invalid DurationInput")},IS={_tag:"Millis",millis:0},lk={_tag:"Infinity"},ck={[Yp]:Yp,[At](){return ue(this,qE(this.value))},[wt](t){return TT(t)&&Ok(this,t)},toString(){return`Duration(${Mk(this)})`},toJSON(){switch(this.value._tag){case"Millis":return{_id:"Duration",_tag:"Millis",millis:this.value.millis};case"Nanos":return{_id:"Duration",_tag:"Nanos",hrtime:_k(this)};case"Infinity":return{_id:"Duration",_tag:"Infinity"}}},[re](){return this.toJSON()},pipe(){return Et(this,arguments)}},da=t=>{const n=Object.create(ck);return Pp(t)?isNaN(t)||t<=0?n.value=IS:Number.isFinite(t)?Number.isInteger(t)?n.value={_tag:"Millis",millis:t}:n.value={_tag:"Nanos",nanos:BigInt(Math.round(t*1e6))}:n.value=lk:t<=ET?n.value=IS:n.value={_tag:"Nanos",nanos:t},n},TT=t=>Rt(t,Yp),uk=t=>{switch(t.value._tag){case"Millis":return t.value.millis===0;case"Nanos":return t.value.nanos===ET;case"Infinity":return!1}},OT=da(0),fk=da(1/0),Op=t=>da(t),dk=t=>da(t*Xp),_f=t=>da(t),hk=t=>da(t*1e3),mk=t=>da(t*6e4),pk=t=>da(t*36e5),yk=t=>da(t*864e5),gk=t=>da(t*6048e5),Zp=t=>bk(t,{onMillis:n=>n,onNanos:n=>Number(n)/1e6}),vk=t=>{const n=ua(t);switch(n.value._tag){case"Infinity":throw new Error("Cannot convert infinite duration to nanos");case"Nanos":return n.value.nanos;case"Millis":return BigInt(Math.round(n.value.millis*1e6))}},_k=t=>{const n=ua(t);switch(n.value._tag){case"Infinity":return[1/0,0];case"Nanos":return[Number(n.value.nanos/HS),Number(n.value.nanos%HS)];case"Millis":return[Math.floor(n.value.millis/1e3),Math.round(n.value.millis%1e3*1e6)]}},bk=E(2,(t,n)=>{const r=ua(t);switch(r.value._tag){case"Nanos":return n.onNanos(r.value.nanos);case"Infinity":return n.onMillis(1/0);case"Millis":return n.onMillis(r.value.millis)}}),rg=E(3,(t,n,r)=>{const i=ua(t),l=ua(n);if(i.value._tag==="Infinity"||l.value._tag==="Infinity")return r.onMillis(Zp(i),Zp(l));if(i.value._tag==="Nanos"||l.value._tag==="Nanos"){const u=i.value._tag==="Nanos"?i.value.nanos:BigInt(Math.round(i.value.millis*1e6)),f=l.value._tag==="Nanos"?l.value.nanos:BigInt(Math.round(l.value.millis*1e6));return r.onNanos(u,f)}return r.onMillis(i.value.millis,l.value.millis)}),Sk=(t,n)=>rg(t,n,{onMillis:(r,i)=>r===i,onNanos:(r,i)=>r===i}),Ek=E(2,(t,n)=>rg(t,n,{onMillis:(r,i)=>r<=i,onNanos:(r,i)=>r<=i})),Tk=E(2,(t,n)=>rg(t,n,{onMillis:(r,i)=>r>=i,onNanos:(r,i)=>r>=i})),Ok=E(2,(t,n)=>Sk(ua(t),ua(n))),Rk=t=>{const n=ua(t);if(n.value._tag==="Infinity")return{days:1/0,hours:1/0,minutes:1/0,seconds:1/0,millis:1/0,nanos:1/0};const r=vk(n),i=r/qS,l=i/Xp,u=l/Fu,f=u/Fu,h=f/BS;return{days:Number(h),hours:Number(f%BS),minutes:Number(u%Fu),seconds:Number(l%Fu),millis:Number(i%Xp),nanos:Number(r%qS)}},Mk=t=>{const n=ua(t);if(n.value._tag==="Infinity")return"Infinity";if(uk(n))return"0";const r=Rk(n),i=[];return r.days!==0&&i.push(`${r.days}d`),r.hours!==0&&i.push(`${r.hours}h`),r.minutes!==0&&i.push(`${r.minutes}m`),r.seconds!==0&&i.push(`${r.seconds}s`),r.millis!==0&&i.push(`${r.millis}ms`),r.nanos!==0&&i.push(`${r.nanos}ns`),i.join(" ")},cs=5,sg=Math.pow(2,cs),wk=sg-1,Ak=sg/2,Ck=sg/4;function xk(t){return t-=t>>1&1431655765,t=(t&858993459)+(t>>2&858993459),t=t+(t>>4)&252645135,t+=t>>8,t+=t>>16,t&127}function Di(t,n){return n>>>t&wk}function gi(t){return 1<<t}function RT(t,n){return xk(t&n-1)}const Dk=(t,n)=>({value:t,previous:n});function Ti(t,n,r,i){let l=i;if(!t){const u=i.length;l=new Array(u);for(let f=0;f<u;++f)l[f]=i[f]}return l[n]=r,l}function MT(t,n,r){const i=r.length-1;let l=0,u=0,f=r;if(t)l=u=n;else for(f=new Array(i);l<n;)f[u++]=r[l++];for(++l;l<=i;)f[u++]=r[l++];return t&&(f.length=i),f}function kk(t,n,r,i){const l=i.length;if(t){let m=l;for(;m>=n;)i[m--]=i[m];return i[n]=r,i}let u=0,f=0;const h=new Array(l+1);for(;u<n;)h[f++]=i[u++];for(h[n]=r;u<l;)h[++f]=i[u++];return h}class br{_tag="EmptyNode";modify(n,r,i,l,u,f){const h=i(yt());return $e(h)?new br:(++f.value,new ns(n,l,u,h))}}function Kn(t){return LE(t,"EmptyNode")}function Nk(t){return Kn(t)||t._tag==="LeafNode"||t._tag==="CollisionNode"}function sd(t,n){return Kn(t)?!1:n===t.edit}class ns{edit;hash;key;value;_tag="LeafNode";constructor(n,r,i,l){this.edit=n,this.hash=r,this.key=i,this.value=l}modify(n,r,i,l,u,f){if(Ot(u,this.key)){const m=i(this.value);return m===this.value?this:$e(m)?(--f.value,new br):sd(this,n)?(this.value=m,this):new ns(n,l,u,m)}const h=i(yt());return $e(h)?this:(++f.value,wT(n,r,this.hash,this,l,new ns(n,l,u,h)))}}class ig{edit;hash;children;_tag="CollisionNode";constructor(n,r,i){this.edit=n,this.hash=r,this.children=i}modify(n,r,i,l,u,f){if(l===this.hash){const m=sd(this,n),p=this.updateCollisionList(m,n,this.hash,this.children,i,u,f);return p===this.children?this:p.length>1?new ig(n,this.hash,p):p[0]}const h=i(yt());return $e(h)?this:(++f.value,wT(n,r,this.hash,this,l,new ns(n,l,u,h)))}updateCollisionList(n,r,i,l,u,f,h){const m=l.length;for(let g=0;g<m;++g){const v=l[g];if("key"in v&&Ot(f,v.key)){const b=v.value,O=u(b);return O===b?l:$e(O)?(--h.value,MT(n,g,l)):Ti(n,g,new ns(r,i,f,O),l)}}const p=u(yt());return $e(p)?l:(++h.value,Ti(n,m,new ns(r,i,f,p),l))}}class ki{edit;mask;children;_tag="IndexedNode";constructor(n,r,i){this.edit=n,this.mask=r,this.children=i}modify(n,r,i,l,u,f){const h=this.mask,m=this.children,p=Di(r,l),g=gi(p),v=RT(h,g),b=h&g,O=sd(this,n);if(!b){const B=new br().modify(n,r+cs,i,l,u,f);return B?m.length>=Ak?Lk(n,p,B,h,m):new ki(n,h|g,kk(O,v,B,m)):this}const R=m[v],A=R.modify(n,r+cs,i,l,u,f);if(R===A)return this;let j=h,U;if(Kn(A)){if(j&=~g,!j)return new br;if(m.length<=2&&Nk(m[v^1]))return m[v^1];U=MT(O,v,m)}else U=Ti(O,v,A,m);return O?(this.mask=j,this.children=U,this):new ki(n,j,U)}}class og{edit;size;children;_tag="ArrayNode";constructor(n,r,i){this.edit=n,this.size=r,this.children=i}modify(n,r,i,l,u,f){let h=this.size;const m=this.children,p=Di(r,l),g=m[p],v=(g||new br).modify(n,r+cs,i,l,u,f);if(g===v)return this;const b=sd(this,n);let O;if(Kn(g)&&!Kn(v))++h,O=Ti(b,p,v,m);else if(!Kn(g)&&Kn(v)){if(--h,h<=Ck)return zk(n,h,p,m);O=Ti(b,p,new br,m)}else O=Ti(b,p,v,m);return b?(this.size=h,this.children=O,this):new og(n,h,O)}}function zk(t,n,r,i){const l=new Array(n-1);let u=0,f=0;for(let h=0,m=i.length;h<m;++h)if(h!==r){const p=i[h];p&&!Kn(p)&&(l[u++]=p,f|=1<<h)}return new ki(t,f,l)}function Lk(t,n,r,i,l){const u=[];let f=i,h=0;for(let m=0;f;++m)f&1&&(u[m]=l[h++]),f>>>=1;return u[n]=r,new og(t,h+1,u)}function $k(t,n,r,i,l,u){if(r===l)return new ig(t,r,[u,i]);const f=Di(n,r),h=Di(n,l);if(f===h)return m=>new ki(t,gi(f)|gi(h),[m]);{const m=f<h?[i,u]:[u,i];return new ki(t,gi(f)|gi(h),m)}}function wT(t,n,r,i,l,u){let f,h=n;for(;;){const m=$k(t,h,r,i,l,u);if(typeof m=="function")f=Dk(m,f),h=h+cs;else{let p=m;for(;f!=null;)p=f.value(p),f=f.previous;return p}}}const AT="effect/HashMap",Jp=Symbol.for(AT),Uk={[Jp]:Jp,[Symbol.iterator](){return new id(this,(t,n)=>[t,n])},[At](){let t=mt(AT);for(const n of this)t^=x(mt(n[0]),Lt(mt(n[1])));return ue(this,t)},[wt](t){if(Bk(t)){if(t._size!==this._size)return!1;for(const n of this){const r=x(t,cg(n[0],mt(n[0])));if($e(r))return!1;if(!Ot(n[1],r.value))return!1}return!0}return!1},toString(){return _e(this.toJSON())},toJSON(){return{_id:"HashMap",values:Array.from(this).map(pe)}},[re](){return this.toJSON()},pipe(){return Et(this,arguments)}},lg=(t,n,r,i)=>{const l=Object.create(Uk);return l._editable=t,l._edit=n,l._root=r,l._size=i,l};class id{map;f;v;constructor(n,r){this.map=n,this.f=r,this.v=CT(this.map._root,this.f,void 0)}next(){if($e(this.v))return{done:!0,value:void 0};const n=this.v.value;return this.v=bf(n.cont),{done:!1,value:n.value}}[Symbol.iterator](){return new id(this.map,this.f)}}const bf=t=>t?xT(t[0],t[1],t[2],t[3],t[4]):yt(),CT=(t,n,r=void 0)=>{switch(t._tag){case"LeafNode":return ca(t.value)?Tt({value:n(t.key,t.value.value),cont:r}):bf(r);case"CollisionNode":case"ArrayNode":case"IndexedNode":{const i=t.children;return xT(i.length,i,0,n,r)}default:return bf(r)}},xT=(t,n,r,i,l)=>{for(;r<t;){const u=n[r++];if(u&&!Kn(u))return CT(u,i,[t,n,r,i,l])}return bf(l)},Fk=lg(!1,0,new br,0),od=()=>Fk,jk=t=>{const n=kT(od());for(const r of t)Ll(n,r[0],r[1]);return Vk(n)},Bk=t=>Rt(t,Jp),qk=t=>t&&Kn(t._root),Hk=E(2,(t,n)=>cg(t,n,mt(n))),cg=E(3,(t,n,r)=>{let i=t._root,l=0;for(;;)switch(i._tag){case"LeafNode":return Ot(n,i.key)?i.value:yt();case"CollisionNode":{if(r===i.hash){const u=i.children;for(let f=0,h=u.length;f<h;++f){const m=u[f];if("key"in m&&Ot(n,m.key))return m.value}}return yt()}case"IndexedNode":{const u=Di(l,r),f=gi(u);if(i.mask&f){i=i.children[RT(i.mask,f)],l+=cs;break}return yt()}case"ArrayNode":{if(i=i.children[Di(l,r)],i){l+=cs;break}return yt()}default:return yt()}}),Ik=E(2,(t,n)=>ca(cg(t,n,mt(n)))),Ll=E(3,(t,n,r)=>ug(t,n,()=>Tt(r))),Pk=E(3,(t,n,r)=>t._editable?(t._root=n,t._size=r,t):n===t._root?t:lg(t._editable,t._edit,n,r)),DT=t=>new id(t,n=>n),Wp=t=>t._size,kT=t=>lg(!0,t._edit+1,t._root,t._size),Vk=t=>(t._editable=!1,t),ug=E(3,(t,n,r)=>Gk(t,n,mt(n),r)),Gk=E(4,(t,n,r,i)=>{const l={value:t._size},u=t._root.modify(t._editable?t._edit:NaN,0,i,r,n,l);return x(t,Pk(u,l.value))}),PS=E(2,(t,n)=>ug(t,n,yt)),Kk=E(2,(t,n)=>ld(t,od(),(r,i,l)=>Ll(r,l,n(i,l)))),NT=E(2,(t,n)=>ld(t,void 0,(r,i,l)=>n(i,l))),ld=E(3,(t,n,r)=>{const i=t._root;if(i._tag==="LeafNode")return ca(i.value)?r(n,i.value.value,i.key):n;if(i._tag==="EmptyNode")return n;const l=[i.children];let u;for(;u=l.pop();)for(let f=0,h=u.length;f<h;){const m=u[f++];m&&!Kn(m)&&(m._tag==="LeafNode"?ca(m.value)&&(n=r(n,m.value.value,m.key)):l.push(m.children))}return n}),zT="effect/HashSet",ty=Symbol.for(zT),Qk={[ty]:ty,[Symbol.iterator](){return DT(this._keyMap)},[At](){return ue(this,Lt(mt(this._keyMap))(mt(zT)))},[wt](t){return Yk(t)?Wp(this._keyMap)===Wp(t._keyMap)&&Ot(this._keyMap,t._keyMap):!1},toString(){return _e(this.toJSON())},toJSON(){return{_id:"HashSet",values:Array.from(this).map(pe)}},[re](){return this.toJSON()},pipe(){return Et(this,arguments)}},cd=t=>{const n=Object.create(Qk);return n._keyMap=t,n},Yk=t=>Rt(t,ty),Xk=cd(od()),ud=()=>Xk,Zk=t=>{const n=fg(ud());for(const r of t)$l(n,r);return dg(n)},Jk=(...t)=>{const n=fg(ud());for(const r of t)$l(n,r);return dg(n)},Wk=E(2,(t,n)=>Ik(t._keyMap,n)),tN=t=>Wp(t._keyMap),fg=t=>cd(kT(t._keyMap)),dg=t=>(t._keyMap._editable=!1,t),LT=E(2,(t,n)=>{const r=fg(t);return n(r),dg(r)}),$l=E(2,(t,n)=>t._keyMap._editable?(Ll(n,!0)(t._keyMap),t):cd(Ll(n,!0)(t._keyMap))),$T=E(2,(t,n)=>t._keyMap._editable?(PS(n)(t._keyMap),t):cd(PS(n)(t._keyMap))),eN=E(2,(t,n)=>LT(t,r=>{for(const i of n)$T(r,i)})),nN=E(2,(t,n)=>LT(ud(),r=>{aN(t,i=>$l(r,i));for(const i of n)$l(r,i)})),aN=E(2,(t,n)=>NT(t._keyMap,(r,i)=>n(i))),rN=E(3,(t,n,r)=>ld(t._keyMap,n,(i,l,u)=>r(i,u))),us=ud,sN=Zk,hg=Jk,iN=Wk,UT=tN,Al=$l,FT=$T,VS=eN,Ul=nN,Fl=rN,GS=Symbol.for("effect/MutableRef"),oN={[GS]:GS,toString(){return _e(this.toJSON())},toJSON(){return{_id:"MutableRef",current:pe(this.current)}},[re](){return this.toJSON()},pipe(){return Et(this,arguments)}},fd=t=>{const n=Object.create(oN);return n.current=t,n},lN=E(3,(t,n,r)=>Ot(n,t.current)?(t.current=r,!0):!1),Sr=t=>t.current,dd=E(2,(t,n)=>(t.current=n,t)),hd="effect/FiberId",fs=Symbol.for(hd),Ni="None",ey="Runtime",ny="Composite",cN=ve(`${hd}-${Ni}`);let uN=class{[fs]=fs;_tag=Ni;id=-1;startTimeMillis=-1;[At](){return cN}[wt](n){return mg(n)&&n._tag===Ni}toString(){return _e(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag}}[re](){return this.toJSON()}};class fN{id;startTimeMillis;[fs]=fs;_tag=ey;constructor(n,r){this.id=n,this.startTimeMillis=r}[At](){return ue(this,ve(`${hd}-${this._tag}-${this.id}-${this.startTimeMillis}`))}[wt](n){return mg(n)&&n._tag===ey&&this.id===n.id&&this.startTimeMillis===n.startTimeMillis}toString(){return _e(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,id:this.id,startTimeMillis:this.startTimeMillis}}[re](){return this.toJSON()}}class dN{left;right;[fs]=fs;_tag=ny;constructor(n,r){this.left=n,this.right=r}_hash;[At](){return x(ve(`${hd}-${this._tag}`),Lt(mt(this.left)),Lt(mt(this.right)),ue(this))}[wt](n){return mg(n)&&n._tag===ny&&Ot(this.left,n.left)&&Ot(this.right,n.right)}toString(){return _e(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,left:pe(this.left),right:pe(this.right)}}[re](){return this.toJSON()}}const jT=new uN,mg=t=>Rt(t,fs),BT=E(2,(t,n)=>t._tag===Ni?n:n._tag===Ni?t:new dN(t,n)),hN=t=>x(t,Fl(jT,(n,r)=>BT(r)(n))),Sf=t=>{switch(t._tag){case Ni:return us();case ey:return hg(t.id);case ny:return x(Sf(t.left),Ul(Sf(t.right)))}},KS=$t(Symbol.for("effect/Fiber/Id/_fiberCounter"),()=>fd(0)),qT=t=>Array.from(Sf(t)).map(r=>`#${r}`).join(","),mN=()=>{const t=Sr(KS);return x(KS,dd(t+1)),new fN(t,Date.now())},ds=jT,pN=BT,x7=hN,yN=Sf,gN=qT,HT=mN,md=od,vN=jk,_N=qk,IT=Hk,PT=Ll,VT=DT,bN=ug,SN=Kk,EN=NT,GT=ld,jl=Symbol.for("effect/List"),ay=t=>ce(t),TN=t=>v3(tg(t),ay),ON=TN(Ot),RN={[jl]:jl,_tag:"Cons",toString(){return _e(this.toJSON())},toJSON(){return{_id:"List",_tag:"Cons",values:ay(this).map(pe)}},[re](){return this.toJSON()},[wt](t){return QT(t)&&this._tag===t._tag&&ON(this,t)},[At](){return ue(this,ec(ay(this)))},[Symbol.iterator](){let t=!1,n=this;return{next(){if(t)return this.return();if(n._tag==="Nil")return t=!0,this.return();const r=n.head;return n=n.tail,{done:t,value:r}},return(r){return t||(t=!0),{done:!0,value:r}}}},pipe(){return Et(this,arguments)}},Ef=(t,n)=>{const r=Object.create(RN);return r.head=t,r.tail=n,r},MN=ve("Nil"),wN={[jl]:jl,_tag:"Nil",toString(){return _e(this.toJSON())},toJSON(){return{_id:"List",_tag:"Nil"}},[re](){return this.toJSON()},[At](){return MN},[wt](t){return QT(t)&&this._tag===t._tag},[Symbol.iterator](){return{next(){return{done:!0,value:void 0}}}},pipe(){return Et(this,arguments)}},KT=Object.create(wN),QT=t=>Rt(t,jl),La=t=>t._tag==="Nil",AN=t=>t._tag==="Cons",CN=()=>KT,hs=(t,n)=>Ef(t,n),zi=CN,pg=t=>Ef(t,KT),xN=E(2,(t,n)=>kN(n,t)),DN=E(2,(t,n)=>hs(n,t)),kN=E(2,(t,n)=>{if(La(t))return n;if(La(n))return t;{const r=Ef(n.head,t);let i=r,l=n.tail;for(;!La(l);){const u=Ef(l.head,t);i.tail=u,i=u,l=l.tail}return r}}),NN=E(3,(t,n,r)=>{let i=n,l=t;for(;!La(l);)i=r(i,l.head),l=l.tail;return i}),zN=t=>{let n=zi(),r=t;for(;!La(r);)n=DN(n,r.head),r=r.tail;return n},yg=function(){function t(n){n&&Object.assign(this,n)}return t.prototype=Xy,t}(),LN=Symbol.for("effect/DifferContextPatch");function QS(t){return t}const ac={...yg.prototype,[LN]:{_Value:QS,_Patch:QS}},$N=Object.assign(Object.create(ac),{_tag:"Empty"}),UN=Object.create($N),YT=()=>UN,FN=Object.assign(Object.create(ac),{_tag:"AndThen"}),jN=(t,n)=>{const r=Object.create(FN);return r.first=t,r.second=n,r},BN=Object.assign(Object.create(ac),{_tag:"AddService"}),qN=(t,n)=>{const r=Object.create(BN);return r.key=t,r.service=n,r},HN=Object.assign(Object.create(ac),{_tag:"RemoveService"}),IN=t=>{const n=Object.create(HN);return n.key=t,n},PN=Object.assign(Object.create(ac),{_tag:"UpdateService"}),VN=(t,n)=>{const r=Object.create(PN);return r.key=t,r.update=n,r},GN=(t,n)=>{const r=new Map(t.unsafeMap);let i=YT();for(const[l,u]of n.unsafeMap.entries())if(r.has(l)){const f=r.get(l);r.delete(l),Ot(f,u)||(i=Yu(VN(l,()=>u))(i))}else r.delete(l),i=Yu(qN(l,u))(i);for(const[l]of r.entries())i=Yu(IN(l))(i);return i},Yu=E(2,(t,n)=>jN(t,n)),KN=E(2,(t,n)=>{if(t._tag==="Empty")return n;let r=!1,i=Je(t);const l=new Map(n.unsafeMap);for(;Ua(i);){const f=oa(i),h=ka(i);switch(f._tag){case"Empty":{i=h;break}case"AddService":{l.set(f.key,f.service),i=h;break}case"AndThen":{i=Ln(Ln(h,f.second),f.first);break}case"RemoveService":{l.delete(f.key),i=h;break}case"UpdateService":{l.set(f.key,f.update(l.get(f.key))),r=!0,i=h;break}}}if(!r)return xi(l);const u=new Map;for(const[f]of n.unsafeMap)l.has(f)&&(u.set(f,l.get(f)),l.delete(f));for(const[f,h]of l)u.set(f,h);return xi(u)}),QN=Symbol.for("effect/DifferHashSetPatch");function Rp(t){return t}const pd={...yg.prototype,[QN]:{_Value:Rp,_Key:Rp,_Patch:Rp}},YN=Object.assign(Object.create(pd),{_tag:"Empty"}),XN=Object.create(YN),XT=()=>XN,ZN=Object.assign(Object.create(pd),{_tag:"AndThen"}),JN=(t,n)=>{const r=Object.create(ZN);return r.first=t,r.second=n,r},WN=Object.assign(Object.create(pd),{_tag:"Add"}),t4=t=>{const n=Object.create(WN);return n.value=t,n},e4=Object.assign(Object.create(pd),{_tag:"Remove"}),n4=t=>{const n=Object.create(e4);return n.value=t,n},a4=(t,n)=>{const[r,i]=Fl([t,XT()],([l,u],f)=>iN(f)(l)?[FT(f)(l),u]:[l,ry(t4(f))(u)])(n);return Fl(i,(l,u)=>ry(n4(u))(l))(r)},ry=E(2,(t,n)=>JN(t,n)),r4=E(2,(t,n)=>{if(t._tag==="Empty")return n;let r=n,i=Je(t);for(;Ua(i);){const l=oa(i),u=ka(i);switch(l._tag){case"Empty":{i=u;break}case"AndThen":{i=Ln(l.first)(Ln(l.second)(u));break}case"Add":{r=Al(l.value)(r),i=u;break}case"Remove":r=FT(l.value)(r),i=u}}return r}),s4=Symbol.for("effect/DifferReadonlyArrayPatch");function YS(t){return t}const rc={...yg.prototype,[s4]:{_Value:YS,_Patch:YS}},i4=Object.assign(Object.create(rc),{_tag:"Empty"}),o4=Object.create(i4),ZT=()=>o4,l4=Object.assign(Object.create(rc),{_tag:"AndThen"}),c4=(t,n)=>{const r=Object.create(l4);return r.first=t,r.second=n,r},u4=Object.assign(Object.create(rc),{_tag:"Append"}),f4=t=>{const n=Object.create(u4);return n.values=t,n},d4=Object.assign(Object.create(rc),{_tag:"Slice"}),h4=(t,n)=>{const r=Object.create(d4);return r.from=t,r.until=n,r},m4=Object.assign(Object.create(rc),{_tag:"Update"}),p4=(t,n)=>{const r=Object.create(m4);return r.index=t,r.patch=n,r},y4=t=>{let n=0,r=ZT();for(;n<t.oldValue.length&&n<t.newValue.length;){const i=t.oldValue[n],l=t.newValue[n],u=t.differ.diff(i,l);Ot(u,t.differ.empty)||(r=Xu(r,p4(n,u))),n=n+1}return n<t.oldValue.length&&(r=Xu(r,h4(0,n))),n<t.newValue.length&&(r=Xu(r,f4(SD(n)(t.newValue)))),r},Xu=E(2,(t,n)=>c4(t,n)),g4=E(3,(t,n,r)=>{if(t._tag==="Empty")return n;let i=n.slice(),l=Hn(t);for(;pD(l);){const u=Ze(l),f=is(l);switch(u._tag){case"Empty":{l=f;break}case"AndThen":{f.unshift(u.first,u.second),l=f;break}case"Append":{for(const h of u.values)i.push(h);l=f;break}case"Slice":{i=i.slice(u.from,u.until),l=f;break}case"Update":{i[u.index]=r.patch(u.patch,i[u.index]),l=f;break}}}return i}),v4=Symbol.for("effect/Differ"),_4={[v4]:{_P:Vt,_V:Vt},pipe(){return Et(this,arguments)}},Wi=t=>{const n=Object.create(_4);return n.empty=t.empty,n.diff=t.diff,n.combine=t.combine,n.patch=t.patch,n},b4=()=>Wi({empty:YT(),combine:(t,n)=>Yu(n)(t),diff:(t,n)=>GN(t,n),patch:(t,n)=>KN(n)(t)}),S4=()=>Wi({empty:XT(),combine:(t,n)=>ry(n)(t),diff:(t,n)=>a4(t,n),patch:(t,n)=>r4(n)(t)}),E4=t=>Wi({empty:ZT(),combine:(n,r)=>Xu(n,r),diff:(n,r)=>y4({oldValue:n,newValue:r,differ:t}),patch:(n,r)=>g4(n,r,t)}),JT=()=>T4((t,n)=>n),T4=t=>Wi({empty:Vt,combine:(n,r)=>n===Vt?r:r===Vt?n:i=>r(n(i)),diff:(n,r)=>Ot(n,r)?Vt:Yf(r),patch:(n,r)=>t(r,n(r))}),Bl=255,WT=8,sy=t=>t&Bl,iy=t=>t>>WT&Bl,sc=(t,n)=>(t&Bl)+((n&t&Bl)<<WT),O4=sc(0,0),R4=t=>sc(t,t),M4=t=>sc(t,0),w4=E(2,(t,n)=>sc(sy(t)&~n,iy(t))),A4=E(2,(t,n)=>t|n),C4=t=>~t>>>0&Bl,x4=0,to=1,D4=2,t2=4,oy=16,e2=32,k4=t=>yd(t,e2),N4=E(2,(t,n)=>t|n),hr=t=>n2(t)&&!L4(t),n2=t=>yd(t,to),yd=E(2,(t,n)=>(t&n)!==0),a2=(...t)=>t.reduce((n,r)=>n|r,0),z4=a2(x4),XS=t=>yd(t,t2),L4=t=>yd(t,oy),pr=E(2,(t,n)=>sc(t^n,n)),Oi=E(2,(t,n)=>t&(C4(sy(n))|iy(n))|sy(n)&iy(n)),ZS=Wi({empty:O4,diff:(t,n)=>pr(t,n),combine:(t,n)=>A4(n)(t),patch:(t,n)=>Oi(n,t)}),$4=R4,r2=M4,JS=w4,s2=(t,n)=>({_tag:"Par",left:t,right:n}),ju=(t,n)=>({_tag:"Seq",left:t,right:n}),U4=t=>{let n=pg(t),r=zi();for(;;){const[i,l]=NN(n,[i2(),zi()],([u,f],h)=>{const[m,p]=F4(h);return[I4(u,m),xN(f,p)]});if(r=j4(r,i),La(l))return zN(r);n=l}throw new Error("BUG: BlockedRequests.flatten - please report an issue at https://github.com/Effect-TS/effect/issues")},F4=t=>{let n=t,r=i2(),i=zi(),l=zi();for(;;)switch(n._tag){case"Empty":{if(La(i))return[r,l];n=i.head,i=i.tail;break}case"Par":{i=hs(n.right,i),n=n.left;break}case"Seq":{const u=n.left,f=n.right;switch(u._tag){case"Empty":{n=f;break}case"Par":{const h=u.left,m=u.right;n=s2(ju(h,f),ju(m,f));break}case"Seq":{const h=u.left,m=u.right;n=ju(h,ju(m,f));break}case"Single":{n=u,l=hs(f,l);break}}break}case"Single":{if(r=H4(r,n),La(i))return[r,l];n=i.head,i=i.tail;break}}throw new Error("BUG: BlockedRequests.step - please report an issue at https://github.com/Effect-TS/effect/issues")},j4=(t,n)=>{if(La(t))return pg(Mp(n));if(P4(n))return t;const r=X4(t.head),i=V4(n);return r.length===1&&i.length===1&&Ot(r[0],i[0])?hs(Y4(t.head,Mp(n)),t.tail):hs(Mp(n),t)},B4=Symbol.for("effect/RequestBlock/RequestBlockParallel"),q4={_R:t=>t};class gg{map;[B4]=q4;constructor(n){this.map=n}}const i2=()=>new gg(md()),H4=(t,n)=>new gg(bN(t.map,n.dataSource,r=>sD(Ml(r,sk(n.blockedRequest)),()=>Je(n.blockedRequest)))),I4=(t,n)=>new gg(GT(t.map,n.map,(r,i,l)=>PT(r,l,Fa(IT(r,l),{onNone:()=>i,onSome:u=>Gn(i,u)})))),P4=t=>_N(t.map),V4=t=>Array.from(VT(t.map)),Mp=t=>Q4(SN(t.map,n=>Je(n))),G4=Symbol.for("effect/RequestBlock/RequestBlockSequential"),K4={_R:t=>t};class o2{map;[G4]=K4;constructor(n){this.map=n}}const Q4=t=>new o2(t),Y4=(t,n)=>new o2(GT(n.map,t.map,(r,i,l)=>PT(r,l,Fa(IT(r,l),{onNone:()=>Yn(),onSome:u=>Gn(u,i)})))),X4=t=>Array.from(VT(t.map)),Z4=t=>Array.from(t.map),eo="Die",ms="Empty",Ss="Fail",no="Interrupt",Li="Parallel",$i="Sequential",l2="effect/Cause",c2=Symbol.for(l2),J4={_E:t=>t},ao={[c2]:J4,[At](){return x(mt(l2),Lt(mt(lz(this))),ue(this))},[wt](t){return W4(t)&&oz(this,t)},pipe(){return Et(this,arguments)},toJSON(){switch(this._tag){case"Empty":return{_id:"Cause",_tag:this._tag};case"Die":return{_id:"Cause",_tag:this._tag,defect:pe(this.defect)};case"Interrupt":return{_id:"Cause",_tag:this._tag,fiberId:this.fiberId.toJSON()};case"Fail":return{_id:"Cause",_tag:this._tag,failure:pe(this.error)};case"Sequential":case"Parallel":return{_id:"Cause",_tag:this._tag,left:pe(this.left),right:pe(this.right)}}},toString(){return ic(this)},[re](){return this.toJSON()}},Ui=(()=>{const t=Object.create(ao);return t._tag=ms,t})(),ql=t=>{const n=Object.create(ao);return n._tag=Ss,n.error=t,n},Qn=t=>{const n=Object.create(ao);return n._tag=eo,n.defect=t,n},ra=t=>{const n=Object.create(ao);return n._tag=no,n.fiberId=t,n},ps=(t,n)=>{const r=Object.create(ao);return r._tag=Li,r.left=t,r.right=n,r},Ke=(t,n)=>{const r=Object.create(ao);return r._tag=$i,r.left=t,r.right=n,r},W4=t=>Rt(t,c2),tz=t=>t._tag===ms,D7=t=>t._tag===Ss,ez=t=>t._tag===eo,nz=t=>t._tag===ms?!0:Fi(t,!0,(n,r)=>{switch(r._tag){case ms:return Tt(n);case eo:case Ss:case no:return Tt(!1);default:return yt()}}),u2=t=>ca(rz(t)),vg=t=>Sg(void 0,uz)(t),az=t=>ls(Fi(t,Yn(),(n,r)=>r._tag===Ss?Tt(x(n,Ln(r.error))):yt())),f2=t=>ls(Fi(t,Yn(),(n,r)=>r._tag===eo?Tt(x(n,Ln(r.defect))):yt())),d2=t=>Fi(t,us(),(n,r)=>r._tag===no?Tt(x(n,Al(r.fiberId))):yt()),h2=t=>_g(t,n=>n._tag===Ss?Tt(n.error):yt()),m2=t=>{const n=h2(t);switch(n._tag){case"None":return Vn(t);case"Some":return _r(n.value)}},rz=t=>_g(t,n=>n._tag===no?Tt(n.fiberId):yt()),WS=t=>bg(t,{onEmpty:Ui,onFail:()=>Ui,onDie:Qn,onInterrupt:ra,onSequential:Ke,onParallel:ps}),sz=t=>bg(t,{onEmpty:Ui,onFail:Qn,onDie:Qn,onInterrupt:ra,onSequential:Ke,onParallel:ps}),k7=E(2,(t,n)=>iz(t,r=>ql(n(r)))),iz=E(2,(t,n)=>bg(t,{onEmpty:Ui,onFail:r=>n(r),onDie:r=>Qn(r),onInterrupt:r=>ra(r),onSequential:(r,i)=>Ke(r,i),onParallel:(r,i)=>ps(r,i)})),oz=(t,n)=>{let r=Je(t),i=Je(n);for(;Ua(r)&&Ua(i);){const[l,u]=x(oa(r),Fi([us(),Yn()],([m,p],g)=>{const[v,b]=ly(g);return Tt([x(m,Ul(v)),x(p,Gn(b))])})),[f,h]=x(oa(i),Fi([us(),Yn()],([m,p],g)=>{const[v,b]=ly(g);return Tt([x(m,Ul(v)),x(p,Gn(b))])}));if(!Ot(l,f))return!1;r=u,i=h}return!0},lz=t=>cz(Je(t),Yn()),cz=(t,n)=>{for(;;){const[r,i]=x(t,Wy([us(),Yn()],([u,f],h)=>{const[m,p]=ly(h);return[x(u,Ul(m)),x(f,Gn(p))]})),l=UT(r)>0?x(n,Ln(r)):n;if(ik(i))return ls(l);t=i,n=l}throw new Error(Wf("Cause.flattenCauseLoop"))},_g=E(2,(t,n)=>{const r=[t];for(;r.length>0;){const i=r.pop(),l=n(i);switch(l._tag){case"None":{switch(i._tag){case $i:case Li:{r.push(i.right),r.push(i.left);break}}break}case"Some":return l}}return yt()}),ly=t=>{let n=t;const r=[];let i=us(),l=Yn();for(;n!==void 0;)switch(n._tag){case ms:{if(r.length===0)return[i,l];n=r.pop();break}case Ss:{if(i=Al(i,Tp(n._tag,n.error)),r.length===0)return[i,l];n=r.pop();break}case eo:{if(i=Al(i,Tp(n._tag,n.defect)),r.length===0)return[i,l];n=r.pop();break}case no:{if(i=Al(i,Tp(n._tag,n.fiberId)),r.length===0)return[i,l];n=r.pop();break}case $i:{switch(n.left._tag){case ms:{n=n.right;break}case $i:{n=Ke(n.left.left,Ke(n.left.right,n.right));break}case Li:{n=ps(Ke(n.left.left,n.right),Ke(n.left.right,n.right));break}default:{l=Ln(l,n.right),n=n.left;break}}break}case Li:{r.push(n.right),n=n.left;break}}throw new Error(Wf("Cause.evaluateCauseLoop"))},uz={emptyCase:zS,failCase:uf,dieCase:uf,interruptCase:zS,sequentialCase:(t,n,r)=>n&&r,parallelCase:(t,n,r)=>n&&r},t1="SequentialCase",e1="ParallelCase",bg=E(2,(t,{onDie:n,onEmpty:r,onFail:i,onInterrupt:l,onParallel:u,onSequential:f})=>Sg(t,void 0,{emptyCase:()=>r,failCase:(h,m)=>i(m),dieCase:(h,m)=>n(m),interruptCase:(h,m)=>l(m),sequentialCase:(h,m,p)=>f(m,p),parallelCase:(h,m,p)=>u(m,p)})),Fi=E(3,(t,n,r)=>{let i=n,l=t;const u=[];for(;l!==void 0;){const f=r(i,l);switch(i=ca(f)?f.value:i,l._tag){case $i:{u.push(l.right),l=l.left;break}case Li:{u.push(l.right),l=l.left;break}default:{l=void 0;break}}l===void 0&&u.length>0&&(l=u.pop())}return i}),Sg=E(3,(t,n,r)=>{const i=[t],l=[];for(;i.length>0;){const f=i.pop();switch(f._tag){case ms:{l.push(Vn(r.emptyCase(n)));break}case Ss:{l.push(Vn(r.failCase(n,f.error)));break}case eo:{l.push(Vn(r.dieCase(n,f.defect)));break}case no:{l.push(Vn(r.interruptCase(n,f.fiberId)));break}case $i:{i.push(f.right),i.push(f.left),l.push(_r({_tag:t1}));break}case Li:{i.push(f.right),i.push(f.left),l.push(_r({_tag:e1}));break}}}const u=[];for(;l.length>0;){const f=l.pop();switch(f._tag){case"Left":{switch(f.left._tag){case t1:{const h=u.pop(),m=u.pop(),p=r.sequentialCase(n,h,m);u.push(p);break}case e1:{const h=u.pop(),m=u.pop(),p=r.parallelCase(n,h,m);u.push(p);break}}break}case"Right":{u.push(f.right);break}}}if(u.length===0)throw new Error("BUG: Cause.reduceWithContext - please report an issue at https://github.com/Effect-TS/effect/issues");return u.pop()}),ic=(t,n)=>vg(t)?"All fibers interrupted without errors.":y2(t).map(function(r){return n?.renderErrorCause!==!0||r.cause===void 0?r.stack:`${r.stack} {
${p2(r.cause,"  ")}
}`}).join(`
`),p2=(t,n)=>{const r=t.stack.split(`
`);let i=`${n}[cause]: ${r[0]}`;for(let l=1,u=r.length;l<u;l++)i+=`
${n}${r[l]}`;return t.cause&&(i+=` {
${p2(t.cause,`${n}  `)}
${n}}`),i};class Tf extends globalThis.Error{span=void 0;constructor(n){const r=typeof n=="object"&&n!==null,i=Error.stackTraceLimit;Error.stackTraceLimit=1,super(fz(n),r&&"cause"in n&&typeof n.cause<"u"?{cause:new Tf(n.cause)}:void 0),this.message===""&&(this.message="An error has occurred"),Error.stackTraceLimit=i,this.name=n instanceof Error?n.name:"Error",r&&(ji in n&&(this.span=n[ji]),Object.keys(n).forEach(l=>{l in this||(this[l]=n[l])})),this.stack=hz(`${this.name}: ${this.message}`,n instanceof Error&&n.stack?n.stack:"",this.span)}}const fz=t=>{if(typeof t=="string")return t;if(typeof t=="object"&&t!==null&&t instanceof Error)return t.message;try{if(Rt(t,"toString")&&Zf(t.toString)&&t.toString!==Object.prototype.toString&&t.toString!==globalThis.Array.prototype.toString)return t.toString()}catch{}return HE(t)},dz=/\((.*)\)/g,Of=$t("effect/Tracer/spanToTrace",()=>new WeakMap),hz=(t,n,r)=>{const i=[t],l=n.startsWith(t)?n.slice(t.length).split(`
`):n.split(`
`);for(let u=1;u<l.length;u++){if(l[u].includes(" at new BaseEffectError")||l[u].includes(" at new YieldableError")){u++;continue}if(l[u].includes("Generator.next")||l[u].includes("effect_internal_function"))break;i.push(l[u].replace(/at .*effect_instruction_i.*\((.*)\)/,"at $1").replace(/EffectPrimitive\.\w+/,"<anonymous>"))}if(r){let u=r,f=0;for(;u&&u._tag==="Span"&&f<10;){const h=Of.get(u);if(typeof h=="function"){const m=h();if(typeof m=="string"){const p=m.matchAll(dz);let g=!1;for(const[,v]of p)g=!0,i.push(`    at ${u.name} (${v})`);g||i.push(`    at ${u.name} (${m.replace(/^at /,"")})`)}else i.push(`    at ${u.name}`)}else i.push(`    at ${u.name}`);u=Pr(u.parent),f++}}return i.join(`
`)},ji=Symbol.for("effect/SpanAnnotation"),y2=t=>Sg(t,void 0,{emptyCase:()=>[],dieCase:(n,r)=>[new Tf(r)],failCase:(n,r)=>[new Tf(r)],interruptCase:()=>[],parallelCase:(n,r,i)=>[...r,...i],sequentialCase:(n,r,i)=>[...r,...i]}),oc="Pending",gd="Done",mz="effect/Deferred",pz=Symbol.for(mz),yz={_E:t=>t,_A:t=>t},gz=t=>({_tag:oc,joiners:t}),g2=t=>({_tag:gd,effect:t});class lc{self;called=!1;constructor(n){this.self=n}next(n){return this.called?{value:n,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(n){return{value:n,done:!0}}throw(n){throw n}[Symbol.iterator](){return new lc(this.self)}}const v2=(t,n)=>{const r=new Oe("Blocked");return r.effect_instruction_i0=t,r.effect_instruction_i1=n,r},vz=t=>{const n=new Oe("RunBlocked");return n.effect_instruction_i0=t,n},Bi=Symbol.for("effect/Effect");class _z{patch;op;_op=Yy;constructor(n,r){this.patch=n,this.op=r}}class Oe{_op;effect_instruction_i0=void 0;effect_instruction_i1=void 0;effect_instruction_i2=void 0;trace=void 0;[Bi]=Ci;constructor(n){this._op=n}[wt](n){return this===n}[At](){return ue(this,Vy(this))}pipe(){return Et(this,arguments)}toJSON(){return{_id:"Effect",_op:this._op,effect_instruction_i0:pe(this.effect_instruction_i0),effect_instruction_i1:pe(this.effect_instruction_i1),effect_instruction_i2:pe(this.effect_instruction_i2)}}toString(){return _e(this.toJSON())}[re](){return this.toJSON()}[Symbol.iterator](){return new lc(new tc(this))}}class _2{_op;effect_instruction_i0=void 0;effect_instruction_i1=void 0;effect_instruction_i2=void 0;trace=void 0;[Bi]=Ci;constructor(n){this._op=n,this._tag=n}[wt](n){return $g(n)&&n._op==="Failure"&&Ot(this.effect_instruction_i0,n.effect_instruction_i0)}[At](){return x(ve(this._tag),Lt(mt(this.effect_instruction_i0)),ue(this))}get cause(){return this.effect_instruction_i0}pipe(){return Et(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,cause:this.cause.toJSON()}}toString(){return _e(this.toJSON())}[re](){return this.toJSON()}[Symbol.iterator](){return new lc(new tc(this))}}class b2{_op;effect_instruction_i0=void 0;effect_instruction_i1=void 0;effect_instruction_i2=void 0;trace=void 0;[Bi]=Ci;constructor(n){this._op=n,this._tag=n}[wt](n){return $g(n)&&n._op==="Success"&&Ot(this.effect_instruction_i0,n.effect_instruction_i0)}[At](){return x(ve(this._tag),Lt(mt(this.effect_instruction_i0)),ue(this))}get value(){return this.effect_instruction_i0}pipe(){return Et(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,value:pe(this.value)}}toString(){return _e(this.toJSON())}[re](){return this.toJSON()}[Symbol.iterator](){return new lc(new tc(this))}}const ha=t=>Rt(t,Bi),Gt=t=>{const n=new Oe(VE);return n.effect_instruction_i0=t,n},S2=E(3,(t,n,r)=>Xn(i=>X(t,l=>X(yr(Wt(()=>i(n(l)))),u=>Wt(()=>r(l,u)).pipe($n({onFailure:f=>{switch(u._tag){case Ce:return be(Ke(u.effect_instruction_i0,f));case xe:return be(f)}},onSuccess:()=>u})))))),We=E(2,(t,n)=>X(t,()=>pt(n))),ja=t=>We(t,void 0),E2=function(){const t=new Oe(ed);switch(arguments.length){case 2:{t.effect_instruction_i0=arguments[0],t.commit=arguments[1];break}case 3:{t.effect_instruction_i0=arguments[0],t.effect_instruction_i1=arguments[1],t.commit=arguments[2];break}case 4:{t.effect_instruction_i0=arguments[0],t.effect_instruction_i1=arguments[1],t.effect_instruction_i2=arguments[2],t.commit=arguments[3];break}default:throw new Error(Wf("you're not supposed to end up here"))}return t},Hl=(t,n=ds)=>{const r=new Oe(Ol);let i;return r.effect_instruction_i0=l=>{i=t(l)},r.effect_instruction_i1=n,wg(r,l=>ha(i)?i:ae)},Eg=(t,n=ds)=>Wt(()=>Hl(t,n)),fa=(t,n=ds)=>E2(t,function(){let r,i;function l(m){r?r(m):i===void 0&&(i=m)}const u=new Oe(Ol);u.effect_instruction_i0=m=>{r=m,i&&m(i)},u.effect_instruction_i1=n;let f,h;return this.effect_instruction_i0.length!==1?(h=new AbortController,f=Be(()=>this.effect_instruction_i0(l,h.signal))):f=Be(()=>this.effect_instruction_i0(l)),f||h?wg(u,m=>(h&&h.abort(),f??ae)):u}),Tg=E(2,(t,n)=>{const r=new Oe(Gu);return r.effect_instruction_i0=t,r.effect_instruction_i1=n,r}),Rf=E(2,(t,n)=>Er(t,{onFailure:n,onSuccess:pt})),bz=E(3,(t,n,r)=>Tg(t,i=>{const l=m2(i);switch(l._tag){case"Left":return n(l.left)?r(l.left):be(i);case"Right":return be(l.right)}})),n1=Symbol.for("effect/OriginalAnnotation"),Og=(t,n)=>ca(n)?new Proxy(t,{has(r,i){return i===ji||i===n1||i in r},get(r,i){return i===ji?n.value:i===n1?t:r[i]}}):t,Mf=t=>Jf(t)&&!(ji in t)?Gt(n=>be(Qn(Og(t,Bg(n))))):be(Qn(t)),cy=t=>T2(()=>Qn(new eL(t))),qi=t=>Er(t,{onFailure:n=>pt(_r(n)),onSuccess:n=>pt(Vn(n))}),yr=t=>O2(t,{onFailure:Xt,onSuccess:ne}),le=t=>Jf(t)&&!(ji in t)?Gt(n=>be(ql(Og(t,Bg(n))))):be(ql(t)),vd=t=>X(ot(t),le),be=t=>{const n=new _2(Ce);return n.effect_instruction_i0=t,n},T2=t=>X(ot(t),be),Rg=Gt(t=>pt(t.id())),cc=t=>Gt(n=>t(n.id())),X=E(2,(t,n)=>{const r=new Oe(hf);return r.effect_instruction_i0=t,r.effect_instruction_i1=n,r}),vi=E(2,(t,n)=>X(t,r=>{const i=typeof n=="function"?n(r):n;return ha(i)?i:UE(i)?Hl(l=>{i.then(u=>l(pt(u)),u=>l(le(new Md(u,"An unknown error occurred in Effect.andThen"))))}):pt(i)})),Sz=t=>{const n=new Oe("OnStep");return n.effect_instruction_i0=t,n},_d=t=>X(t,Vt),O2=E(2,(t,n)=>$n(t,{onFailure:r=>pt(n.onFailure(r)),onSuccess:r=>pt(n.onSuccess(r))})),$n=E(2,(t,n)=>{const r=new Oe(mf);return r.effect_instruction_i0=t,r.effect_instruction_i1=n.onFailure,r.effect_instruction_i2=n.onSuccess,r}),Er=E(2,(t,n)=>$n(t,{onFailure:r=>{if(f2(r).length>0)return be(sz(r));const l=az(r);return l.length>0?n.onFailure(ST(l)):be(r)},onSuccess:n.onSuccess})),Na=E(2,(t,n)=>Wt(()=>{const r=ce(t),i=Jy(r.length);let l=0;return We(Cg({while:()=>l<r.length,body:()=>n(r[l],l),step:u=>{i[l++]=u}}),i)})),bd=E(2,(t,n)=>Wt(()=>{const r=ce(t);let i=0;return Cg({while:()=>i<r.length,body:()=>n(r[i],i),step:()=>{i++}})})),Ez=X(Rg,t=>R2(t)),R2=t=>be(ra(t)),Mg=t=>{const n=new Oe(Ki);return n.effect_instruction_i0=$4(to),n.effect_instruction_i1=()=>t,n},M2=E(2,(t,n)=>Xn(r=>X(yr(r(t)),i=>mL(n,i)))),Ft=E(2,(t,n)=>X(t,r=>ot(()=>n(r)))),w2=E(2,(t,n)=>Er(t,{onFailure:r=>vd(()=>n.onFailure(r)),onSuccess:r=>ot(()=>n.onSuccess(r))})),Sd=E(2,(t,n)=>$n(t,{onFailure:r=>{const i=m2(r);switch(i._tag){case"Left":return vd(()=>n(i.left));case"Right":return be(i.right)}},onSuccess:pt})),ys=E(2,(t,n)=>Xn(r=>$n(r(t),{onFailure:i=>{const l=Xt(i);return $n(n(l),{onFailure:u=>Xt(Ke(i,u)),onSuccess:()=>l})},onSuccess:i=>{const l=ne(i);return Ve(n(l),l)}}))),wg=E(2,(t,n)=>ys(t,wd({onFailure:r=>vg(r)?ja(n(d2(r))):ae,onSuccess:()=>ae}))),Tz=t=>Oz(t,Vt),Oz=E(2,(t,n)=>Er(t,{onFailure:r=>Mf(n(r)),onSuccess:pt})),Rz=Gt((t,n)=>pt(n.runtimeFlags)),pt=t=>{const n=new b2(xe);return n.effect_instruction_i0=t,n},Wt=t=>{const n=new Oe(ed);return n.commit=t,n},ot=t=>{const n=new Oe(PE);return n.effect_instruction_i0=t,n},Ag=E(t=>t.length===3||t.length===2&&!(Jf(t[1])&&"onlyEffect"in t[1]),(t,n)=>X(t,r=>{const i=typeof n=="function"?n(r):n;return ha(i)?We(i,r):UE(i)?Hl(l=>{i.then(u=>l(pt(r)),u=>l(le(new Md(u,"An unknown error occurred in Effect.tap"))))}):pt(r)})),Mz=t=>Gt(n=>{const r=n.getFiberRef(fy),i=x(r,zn(()=>n.scope()));return t(uc(fy,Tt(i)))}),Ed=t=>{const n=new Oe(Ki);return n.effect_instruction_i0=r2(to),n.effect_instruction_i1=()=>t,n},Xn=t=>E2(t,function(){const n=new Oe(Ki);return n.effect_instruction_i0=r2(to),n.effect_instruction_i1=r=>n2(r)?Be(()=>this.effect_instruction_i0(Mg)):Be(()=>this.effect_instruction_i0(Ed)),n}),ae=pt(void 0),A2=t=>{const n=new Oe(Ki);return n.effect_instruction_i0=t,n.effect_instruction_i1=void 0,n},C2=E(2,(t,n)=>X(n,r=>r?x(t,Ft(Tt)):pt(yt()))),Cg=t=>{const n=new Oe(pf);return n.effect_instruction_i0=t.while,n.effect_instruction_i1=t.body,n.effect_instruction_i2=t.step,n},wz=t=>Wt(()=>{const n=new Oe(Rl);return n.effect_instruction_i0=t(),n}),Az=function(){const t=arguments.length===1?arguments[0]:arguments[1].bind(arguments[0]);return wz(()=>t(x))},Cz=E(2,(t,n)=>{const r=new Oe(Ki);return r.effect_instruction_i0=n,r.effect_instruction_i1=()=>t,r}),xg=t=>{const n=new Oe(Ku);return typeof t?.priority<"u"?Kz(n,t.priority):n},Td=E(2,(t,n)=>X(t,r=>Ft(n,i=>[r,i]))),Dg=E(2,(t,n)=>X(t,r=>We(n,r))),Ve=E(2,(t,n)=>X(t,()=>n)),x2=E(3,(t,n,r)=>X(t,i=>Ft(n,l=>r(i,l)))),kg=t=>X(Rg,n=>x(t,Il(n))),Il=E(2,(t,n)=>X(t.interruptAsFork(n),()=>t.await)),xz={_tag:"All",syslog:0,label:"ALL",ordinal:Number.MIN_SAFE_INTEGER,pipe(){return Et(this,arguments)}},Dz={_tag:"Fatal",syslog:2,label:"FATAL",ordinal:5e4,pipe(){return Et(this,arguments)}},kz={_tag:"Error",syslog:3,label:"ERROR",ordinal:4e4,pipe(){return Et(this,arguments)}},Nz={_tag:"Warning",syslog:4,label:"WARN",ordinal:3e4,pipe(){return Et(this,arguments)}},D2={_tag:"Info",syslog:6,label:"INFO",ordinal:2e4,pipe(){return Et(this,arguments)}},k2={_tag:"Debug",syslog:7,label:"DEBUG",ordinal:1e4,pipe(){return Et(this,arguments)}},zz={_tag:"Trace",syslog:7,label:"TRACE",ordinal:0,pipe(){return Et(this,arguments)}},Lz={_tag:"None",syslog:7,label:"OFF",ordinal:Number.MAX_SAFE_INTEGER,pipe(){return Et(this,arguments)}},$z="effect/FiberRef",Uz=Symbol.for($z),Fz={_A:t=>t},Ng=t=>Gt(n=>ne(n.getFiberRef(t))),Od=E(2,(t,n)=>X(Ng(t),n)),a1=E(2,(t,n)=>jz(t,()=>[void 0,n])),jz=E(2,(t,n)=>Gt(r=>{const[i,l]=n(r.getFiberRef(t));return r.setFiberRef(t,l),pt(i)})),uc=E(3,(t,n,r)=>S2(Dg(Ng(n),a1(n,r)),()=>t,i=>a1(n,i))),Bz=E(3,(t,n,r)=>Od(n,i=>uc(t,n,r(i)))),qe=(t,n)=>ro(t,{differ:JT(),fork:n?.fork??Vt,join:n?.join}),qz=t=>{const n=S4();return ro(t,{differ:n,fork:n.empty})},Hz=t=>{const n=E4(JT());return ro(t,{differ:n,fork:n.empty})},N2=t=>{const n=b4();return ro(t,{differ:n,fork:n.empty})},ro=(t,n)=>({...Qi,[Uz]:Fz,initial:t,commit(){return Ng(this)},diff:(i,l)=>n.differ.diff(i,l),combine:(i,l)=>n.differ.combine(i,l),patch:i=>l=>n.differ.patch(i,l),fork:n.fork,join:n.join??((i,l)=>l)}),Iz=t=>ro(t,{differ:ZS,fork:ZS.empty}),Ba=$t(Symbol.for("effect/FiberRef/currentContext"),()=>N2(zl())),so=$t(Symbol.for("effect/FiberRef/currentSchedulingPriority"),()=>qe(0)),z2=$t(Symbol.for("effect/FiberRef/currentMaxOpsBeforeYield"),()=>qe(2048)),Pz=$t(Symbol.for("effect/FiberRef/currentLogAnnotation"),()=>qe(md())),Vz=$t(Symbol.for("effect/FiberRef/currentLogLevel"),()=>qe(D2)),Gz=$t(Symbol.for("effect/FiberRef/currentLogSpan"),()=>qe(zi())),Kz=E(2,(t,n)=>uc(t,so,n)),Qz=$t(Symbol.for("effect/FiberRef/currentConcurrency"),()=>qe("unbounded")),Yz=$t(Symbol.for("effect/FiberRef/currentRequestBatching"),()=>qe(!0)),Xz=$t(Symbol.for("effect/FiberRef/currentUnhandledErrorLogLevel"),()=>qe(Tt(k2))),uy=$t(Symbol.for("effect/FiberRef/currentMetricLabels"),()=>Hz(os())),fy=$t(Symbol.for("effect/FiberRef/currentForkScopeOverride"),()=>qe(yt(),{fork:()=>yt(),join:(t,n)=>t})),Bu=$t(Symbol.for("effect/FiberRef/currentInterruptedCause"),()=>qe(Ui,{fork:()=>Ui,join:(t,n)=>t})),Zz=$t(Symbol.for("effect/FiberRef/currentTracerEnabled"),()=>qe(!0)),L2=$t(Symbol.for("effect/FiberRef/currentTracerTiming"),()=>qe(!0)),Jz=$t(Symbol.for("effect/FiberRef/currentTracerSpanAnnotations"),()=>qe(md())),Wz=$t(Symbol.for("effect/FiberRef/currentTracerSpanLinks"),()=>qe(Yn())),r1=Symbol.for("effect/Scope"),s1=Symbol.for("effect/CloseableScope"),$2=(t,n)=>t.addFinalizer(()=>ja(n)),wf=(t,n)=>t.addFinalizer(n),dy=(t,n)=>t.close(n),Rd=(t,n)=>t.fork(n),N7=t=>tL(Vt)(t),tL=E(2,(t,n)=>{const r=x(t,h2,Ml(n));switch(r._tag){case"None":return x(f2(t),ag,Fa({onNone:()=>{const i=ce(d2(t)).flatMap(l=>ce(yN(l)).map(u=>`#${u}`));return new nL(i?`Interrupted by fibers: ${i.join(", ")}`:void 0)},onSome:Vt}));case"Some":return r.value}}),U2=function(){class t extends globalThis.Error{commit(){return le(this)}toJSON(){const r={...this};return this.message&&(r.message=this.message),this.cause&&(r.cause=this.cause),r}[re](){return this.toString!==globalThis.Error.prototype.toString?this.stack?`${this.toString()}
${this.stack.split(`
`).slice(1).join(`
`)}`:this.toString():"Bun"in globalThis?ic(ql(this),{renderErrorCause:!0}):this}}return Object.assign(t.prototype,P3),t}(),zg=(t,n)=>{class r extends U2{_tag=n}return Object.assign(r.prototype,t),r.prototype.name=n,r},i1=Symbol.for("effect/Cause/errors/RuntimeException"),eL=zg({[i1]:i1},"RuntimeException"),hy=Symbol.for("effect/Cause/errors/InterruptedException"),nL=zg({[hy]:hy},"InterruptedException"),aL=t=>Rt(t,hy),o1=Symbol.for("effect/Cause/errors/NoSuchElement"),Lg=zg({[o1]:o1},"NoSuchElementException"),l1=Symbol.for("effect/Cause/errors/UnknownException"),Md=function(){class t extends U2{_tag="UnknownException";error;constructor(r,i){super(i??"An unknown error occurred",{cause:r}),this.error=r}}return Object.assign(t.prototype,{[l1]:l1,name:"UnknownException"}),t}(),$g=t=>ha(t)&&"_tag"in t&&(t._tag==="Success"||t._tag==="Failure"),F2=t=>t._tag==="Failure",rL=t=>t._tag==="Success",sL=t=>{switch(t._tag){case Ce:return u2(t.effect_instruction_i0);case xe:return!1}},iL=E(2,(t,n)=>{switch(t._tag){case Ce:return Xt(t.effect_instruction_i0);case xe:return ne(n)}}),wp=t=>iL(t,void 0),Ri=(t,n)=>dL(t,n?.parallel?ps:Ke),_i=t=>Xt(Qn(t)),my=t=>Xt(ql(t)),Xt=t=>{const n=new _2(Ce);return n.effect_instruction_i0=t,n},oL=E(2,(t,n)=>{switch(t._tag){case Ce:return Xt(t.effect_instruction_i0);case xe:return n(t.effect_instruction_i0)}}),lL=t=>x(t,oL(Vt)),cL=t=>Xt(ra(t)),Zu=E(2,(t,n)=>{switch(t._tag){case Ce:return Xt(t.effect_instruction_i0);case xe:return ne(n(t.effect_instruction_i0))}}),wd=E(2,(t,{onFailure:n,onSuccess:r})=>{switch(t._tag){case Ce:return n(t.effect_instruction_i0);case xe:return r(t.effect_instruction_i0)}}),py=E(2,(t,{onFailure:n,onSuccess:r})=>{switch(t._tag){case Ce:return n(t.effect_instruction_i0);case xe:return r(t.effect_instruction_i0)}}),ne=t=>{const n=new b2(xe);return n.effect_instruction_i0=t,n},vn=ne(void 0),uL=E(2,(t,n)=>Ug(t,n,{onSuccess:(r,i)=>[r,i],onFailure:Ke})),fL=E(2,(t,n)=>Ug(t,n,{onSuccess:(r,i)=>i,onFailure:Ke})),Ug=E(3,(t,n,{onFailure:r,onSuccess:i})=>{switch(t._tag){case Ce:switch(n._tag){case xe:return Xt(t.effect_instruction_i0);case Ce:return Xt(r(t.effect_instruction_i0,n.effect_instruction_i0))}case xe:switch(n._tag){case xe:return ne(i(t.effect_instruction_i0,n.effect_instruction_i0));case Ce:return Xt(n.effect_instruction_i0)}}}),dL=(t,n)=>{const r=bT(t);return Ua(r)?x(ka(r),Wy(x(oa(r),Zu(Je)),(i,l)=>x(i,Ug(l,{onSuccess:(u,f)=>x(u,Ln(f)),onFailure:n}))),Zu(ls),Zu(i=>la(i)),Tt):yt()},j2=t=>({...Qi,[pz]:yz,state:fd(gz([])),commit(){return Ad(this)},blockingOn:t}),B2=()=>X(Rg,t=>hL(t)),hL=t=>ot(()=>j2(t)),Ad=t=>Eg(n=>{const r=Sr(t.state);switch(r._tag){case gd:return n(r.effect);case oc:return r.joiners.push(n),gL(t,n)}},t.blockingOn),Cd=E(2,(t,n)=>ot(()=>{const r=Sr(t.state);switch(r._tag){case gd:return!1;case oc:{dd(t.state,g2(n));for(let i=0,l=r.joiners.length;i<l;i++)r.joiners[i](n);return!0}}})),mL=E(2,(t,n)=>Cd(t,n)),pL=E(2,(t,n)=>Cd(t,be(n))),z7=E(2,(t,n)=>Cd(t,R2(n))),L7=t=>ot(()=>Sr(t.state)._tag===gd),yL=E(2,(t,n)=>Cd(t,pt(n))),q2=(t,n)=>{const r=Sr(t.state);if(r._tag===oc){dd(t.state,g2(n));for(let i=0,l=r.joiners.length;i<l;i++)r.joiners[i](n)}},gL=(t,n)=>ot(()=>{const r=Sr(t.state);if(r._tag===oc){const i=r.joiners.indexOf(n);i>=0&&r.joiners.splice(i,1)}}),vL=Gt(t=>ne(t.currentContext)),Fg=()=>vL,io=t=>X(Fg(),t),xd=E(2,(t,n)=>uc(Ba,n)(t)),jg=E(2,(t,n)=>Bz(Ba,r=>Zi(r,n))(t)),H2=E(2,(t,n)=>io(r=>xd(t,n(r)))),Bg=t=>{const n=t.currentSpan;return n!==void 0&&n._tag==="Span"?Tt(n):yt()},_L={_tag:"Span",spanId:"noop",traceId:"noop",sampled:!1,status:{_tag:"Ended",startTime:BigInt(0),endTime:BigInt(0),exit:vn},attributes:new Map,links:[],kind:"internal",attribute(){},event(){},end(){},addLinks(){}},bL=t=>Object.assign(Object.create(_L),t),$7=F2,U7=rL,F7=sL,j7=Ri,B7=_i,q7=my,H7=Xt,SL=lL,I7=Zu,P7=wd,V7=ne,G7=vn,K7=uL,Q7=fL,c1=Symbol.for("effect/MutableHashMap"),EL={[c1]:c1,[Symbol.iterator](){return new qg(this)},toString(){return _e(this.toJSON())},toJSON(){return{_id:"MutableHashMap",values:Array.from(this).map(pe)}},[re](){return this.toJSON()},pipe(){return Et(this,arguments)}};class qg{self;referentialIterator;bucketIterator;constructor(n){this.self=n,this.referentialIterator=n.referential[Symbol.iterator]()}next(){if(this.bucketIterator!==void 0)return this.bucketIterator.next();const n=this.referentialIterator.next();return n.done?(this.bucketIterator=new TL(this.self.buckets.values()),this.next()):n}[Symbol.iterator](){return new qg(this.self)}}class TL{backing;constructor(n){this.backing=n}currentBucket;next(){if(this.currentBucket===void 0){const r=this.backing.next();if(r.done)return r;this.currentBucket=r.value[Symbol.iterator]()}const n=this.currentBucket.next();return n.done?(this.currentBucket=void 0,this.next()):n}}const OL=()=>{const t=Object.create(EL);return t.referential=new Map,t.buckets=new Map,t.bucketsSize=0,t},Vr=E(2,(t,n)=>{if(df(n)===!1)return t.referential.has(n)?Tt(t.referential.get(n)):yt();const r=n[At](),i=t.buckets.get(r);return i===void 0?yt():RL(t,i,n)}),RL=(t,n,r,i=!1)=>{for(let l=0,u=n.length;l<u;l++)if(r[wt](n[l][0])){const f=n[l][1];return i&&(n.splice(l,1),t.bucketsSize--),Tt(f)}return yt()},hl=E(2,(t,n)=>ca(Vr(t,n))),ml=E(3,(t,n,r)=>{if(df(n)===!1)return t.referential.set(n,r),t;const i=n[At](),l=t.buckets.get(i);return l===void 0?(t.buckets.set(i,[[n,r]]),t.bucketsSize++,t):(ML(t,l,n),l.push([n,r]),t.bucketsSize++,t)}),ML=(t,n,r)=>{for(let i=0,l=n.length;i<l;i++)if(r[wt](n[i][0])){n.splice(i,1),t.bucketsSize--;return}},wL="effect/Clock",u1=Symbol.for(wL),oo=bs("effect/Clock"),AL=2**31-1,f1={unsafeSchedule(t,n){const r=Zp(n);if(r>AL)return uf;let i=!1;const l=setTimeout(()=>{i=!0,t()},r);return()=>(clearTimeout(l),!i)}},d1=function(){const t=BigInt(1e6);if(typeof performance>"u")return()=>BigInt(Date.now())*t;let n;return()=>(n===void 0&&(n=BigInt(Date.now())*t-BigInt(Math.round(performance.now()*1e6))),n+BigInt(Math.round(performance.now()*1e6)))}(),CL=function(){const t=typeof process=="object"&&"hrtime"in process&&typeof process.hrtime.bigint=="function"?process.hrtime:void 0;if(!t)return d1;const n=d1()-t.bigint();return()=>n+t.bigint()}();class xL{[u1]=u1;unsafeCurrentTimeMillis(){return Date.now()}unsafeCurrentTimeNanos(){return CL()}currentTimeMillis=ot(()=>this.unsafeCurrentTimeMillis());currentTimeNanos=ot(()=>this.unsafeCurrentTimeNanos());scheduler(){return pt(f1)}sleep(n){return fa(r=>{const i=f1.unsafeSchedule(()=>r(ae),n);return ja(ot(i))})}}const DL=()=>new xL,I2="And",P2="Or",V2="InvalidData",G2="MissingData",K2="SourceUnavailable",Q2="Unsupported",kL="effect/ConfigError",h1=Symbol.for(kL),lo={_tag:"ConfigError",[h1]:h1},Y2=(t,n)=>{const r=Object.create(lo);return r._op=I2,r.left=t,r.right=n,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} and ${this.right}`}}),Object.defineProperty(r,"message",{enumerable:!1,get(){return this.toString()}}),r},X2=(t,n)=>{const r=Object.create(lo);return r._op=P2,r.left=t,r.right=n,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} or ${this.right}`}}),Object.defineProperty(r,"message",{enumerable:!1,get(){return this.toString()}}),r},NL=(t,n,r={pathDelim:"."})=>{const i=Object.create(lo);return i._op=V2,i.path=t,i.message=n,Object.defineProperty(i,"toString",{enumerable:!1,value(){return`(Invalid data at ${x(this.path,Yi(r.pathDelim))}: "${this.message}")`}}),i},gs=(t,n,r={pathDelim:"."})=>{const i=Object.create(lo);return i._op=G2,i.path=t,i.message=n,Object.defineProperty(i,"toString",{enumerable:!1,value(){return`(Missing data at ${x(this.path,Yi(r.pathDelim))}: "${this.message}")`}}),i},zL=(t,n,r,i={pathDelim:"."})=>{const l=Object.create(lo);return l._op=K2,l.path=t,l.message=n,l.cause=r,Object.defineProperty(l,"toString",{enumerable:!1,value(){return`(Source unavailable at ${x(this.path,Yi(i.pathDelim))}: "${this.message}")`}}),l},LL=(t,n,r={pathDelim:"."})=>{const i=Object.create(lo);return i._op=Q2,i.path=t,i.message=n,Object.defineProperty(i,"toString",{enumerable:!1,value(){return`(Unsupported operation at ${x(this.path,Yi(r.pathDelim))}: "${this.message}")`}}),i},Yr=E(2,(t,n)=>{switch(t._op){case I2:return Y2(Yr(t.left,n),Yr(t.right,n));case P2:return X2(Yr(t.left,n),Yr(t.right,n));case V2:return NL([...n,...t.path],t.message);case G2:return gs([...n,...t.path],t.message);case K2:return zL([...n,...t.path],t.message,t.cause);case Q2:return LL([...n,...t.path],t.message)}}),$L={_tag:"Empty"},Ap=E(2,(t,n)=>{let r=pg(n),i=t;for(;AN(r);){const l=r.head;switch(l._tag){case"Empty":{r=r.tail;break}case"AndThen":{r=hs(l.first,hs(l.second,r.tail));break}case"MapName":{i=Wr(i,l.f),r=r.tail;break}case"Nested":{i=yf(i,l.name),r=r.tail;break}case"Unnested":{if(x(wl(i),lD(l.name)))i=is(i),r=r.tail;else return _r(gs(i,`Expected ${l.name} to be in path in ConfigProvider#unnested`));break}}}return Vn(i)}),UL="Constant",FL="Fail",jL="Fallback",BL="Described",qL="Lazy",HL="MapOrFail",IL="Nested",PL="Primitive",VL="Sequence",GL="HashMap",KL="ZipWith";var m1={};const Af=(t,n)=>[...t,...n],QL="effect/ConfigProvider",p1=Symbol.for(QL),YL=bs("effect/ConfigProvider"),XL="effect/ConfigProviderFlat",y1=Symbol.for(XL),ZL=t=>({[p1]:p1,pipe(){return Et(this,arguments)},...t}),JL=t=>({[y1]:y1,patch:t.patch,load:(n,r,i=!0)=>t.load(n,r,i),enumerateChildren:t.enumerateChildren}),WL=t=>ZL({load:n=>X(xn(t,os(),n,!1),r=>Fa(wl(r),{onNone:()=>le(gs(os(),`Expected a single value having structure: ${n}`)),onSome:pt})),flattened:t}),t6=t=>{const{pathDelim:n,seqDelim:r}=Object.assign({},{pathDelim:"_",seqDelim:","},t),i=m=>x(m,Yi(n)),l=m=>m.split(n),u=()=>typeof process<"u"&&"env"in process&&typeof m1=="object"?m1:{};return WL(JL({load:(m,p,g=!0)=>{const v=i(m),b=u(),O=v in b?Tt(b[v]):yt();return x(O,Sd(()=>gs(m,`Expected ${v} to exist in the process context`)),X(R=>s6(R,m,p,r,g)))},enumerateChildren:m=>ot(()=>{const p=u(),b=Object.keys(p).map(O=>l(O.toUpperCase())).filter(O=>{for(let R=0;R<m.length;R++){const A=x(m,oT(R)),j=O[R];if(j===void 0||A!==j)return!1}return!0}).flatMap(O=>O.slice(m.length,m.length+1));return sN(b)}),patch:$L}))},e6=(t,n,r,i)=>{const l=jS(r.length,m=>m>=i.length?yt():Tt([t(m),m+1])),u=jS(i.length,m=>m>=r.length?yt():Tt([n(m),m+1])),f=Af(r,l),h=Af(i,u);return[f,h]},n6=(t,n)=>{let r=n;if(r._tag==="Nested"){const i=t.slice();for(;r._tag==="Nested";)i.push(r.name),r=r.config;return i}return t},xn=(t,n,r,i)=>{const l=r;switch(l._tag){case UL:return pt(Hn(l.value));case BL:return Wt(()=>xn(t,n,l.config,i));case FL:return le(gs(n,l.message));case jL:return x(Wt(()=>xn(t,n,l.first,i)),Rf(u=>l.condition(u)?x(xn(t,n,l.second,i),Rf(f=>le(X2(u,f)))):le(u)));case qL:return Wt(()=>xn(t,n,l.config(),i));case HL:return Wt(()=>x(xn(t,n,l.original,i),X(Na(u=>x(l.mapOrFail(u),Sd(Yr(n6(n,l.original))))))));case IL:return Wt(()=>xn(t,Af(n,Hn(l.name)),l.config,i));case PL:return x(Ap(n,t.patch),X(u=>x(t.load(u,l,i),X(f=>{if(f.length===0){const h=x(vD(u),zn(()=>"<n/a>"));return le(gs([],`Expected ${l.description} with name ${h}`))}return pt(f)}))));case VL:return x(Ap(n,t.patch),X(u=>x(t.enumerateChildren(u),X(o6),X(f=>f.length===0?Wt(()=>Ft(xn(t,n,l.config,!0),Hn)):x(Na(f,h=>xn(t,dD(n,`[${h}]`),l.config,!0)),Ft(h=>{const m=CD(h);return m.length===0?Hn(os()):Hn(m)}))))));case GL:return Wt(()=>x(Ap(n,t.patch),X(u=>x(t.enumerateChildren(u),X(f=>x(f,Na(h=>xn(t,Af(u,Hn(h)),l.valueConfig,i)),Ft(h=>h.length===0?Hn(md()):x(i6(h),Wr(m=>vN(FS(ce(f),m)))))))))));case KL:return Wt(()=>x(xn(t,n,l.left,i),qi,X(u=>x(xn(t,n,l.right,i),qi,X(f=>{if(pi(u)&&pi(f))return le(Y2(u.left,f.left));if(pi(u)&&yi(f))return le(u.left);if(yi(u)&&pi(f))return le(f.left);if(yi(u)&&yi(f)){const h=x(n,Yi(".")),m=a6(n,h),[p,g]=e6(m,m,x(u.right,Wr(Vn)),x(f.right,Wr(Vn)));return x(p,FS(g),Na(([v,b])=>x(Td(v,b),Ft(([O,R])=>l.zip(O,R)))))}throw new Error("BUG: ConfigProvider.fromFlatLoop - please report an issue at https://github.com/Effect-TS/effect/issues")})))))}},a6=(t,n)=>r=>_r(gs(t,`The element at index ${r} in a sequence at path "${n}" was missing`)),r6=(t,n)=>t.split(new RegExp(`\\s*${kD(n)}\\s*`)),s6=(t,n,r,i,l)=>l?x(r6(t,i),Na(u=>r.parse(u.trim())),Sd(Yr(n))):x(r.parse(t),w2({onFailure:Yr(n),onSuccess:Hn})),i6=t=>Object.keys(t[0]).map(n=>t.map(r=>r[n])),o6=t=>x(Na(t,c6),w2({onFailure:()=>os(),onSuccess:gf(Nl)}),qi,Ft(tD)),l6=/^(\[(\d+)\])$/,c6=t=>{const n=t.match(l6);if(n!==null){const r=n[2];return x(r!==void 0&&r.length>0?Tt(r):yt(),rT(u6))}return yt()},u6=t=>{const n=Number.parseInt(t);return Number.isNaN(n)?yt():Tt(n)},g1=Symbol.for("effect/Console"),Z2=bs("effect/Console"),f6={[g1]:g1,assert(t,...n){return ot(()=>{console.assert(t,...n)})},clear:ot(()=>{console.clear()}),count(t){return ot(()=>{console.count(t)})},countReset(t){return ot(()=>{console.countReset(t)})},debug(...t){return ot(()=>{console.debug(...t)})},dir(t,n){return ot(()=>{console.dir(t,n)})},dirxml(...t){return ot(()=>{console.dirxml(...t)})},error(...t){return ot(()=>{console.error(...t)})},group(t){return t?.collapsed?ot(()=>console.groupCollapsed(t?.label)):ot(()=>console.group(t?.label))},groupEnd:ot(()=>{console.groupEnd()}),info(...t){return ot(()=>{console.info(...t)})},log(...t){return ot(()=>{console.log(...t)})},table(t,n){return ot(()=>{console.table(t,n)})},time(t){return ot(()=>console.time(t))},timeEnd(t){return ot(()=>console.timeEnd(t))},timeLog(t,...n){return ot(()=>{console.timeLog(t,...n)})},trace(...t){return ot(()=>{console.trace(...t)})},warn(...t){return ot(()=>{console.warn(...t)})},unsafe:console},d6="effect/Random",v1=Symbol.for(d6),h6=bs("effect/Random");class m6{seed;[v1]=v1;PRNG;constructor(n){this.seed=n,this.PRNG=new A3(n)}get next(){return ot(()=>this.PRNG.number())}get nextBoolean(){return Ft(this.next,n=>n>.5)}get nextInt(){return ot(()=>this.PRNG.integer(Number.MAX_SAFE_INTEGER))}nextRange(n,r){return Ft(this.next,i=>(r-n)*i+n)}nextIntBetween(n,r){return ot(()=>this.PRNG.integer(r-n)+n)}shuffle(n){return p6(n,r=>this.nextIntBetween(0,r))}}const p6=(t,n)=>Wt(()=>x(ot(()=>Array.from(t)),X(r=>{const i=[];for(let l=r.length;l>=2;l=l-1)i.push(l);return x(i,bd(l=>x(n(l),Ft(u=>y6(r,l-1,u)))),We(bT(r)))}))),y6=(t,n,r)=>{const i=t[n];return t[n]=t[r],t[r]=i,t},g6=t=>new m6(mt(t)),_1=Symbol.for("effect/Tracer"),v6=t=>({[_1]:_1,...t}),Hg=bs("effect/Tracer"),Dd=bs("effect/ParentSpan"),b1=function(){const t="abcdef0123456789",n=t.length;return function(r){let i="";for(let l=0;l<r;l++)i+=t.charAt(Math.floor(Math.random()*n));return i}}();class _6{name;parent;context;startTime;kind;_tag="Span";spanId;traceId="native";sampled=!0;status;attributes;events=[];links;constructor(n,r,i,l,u,f){this.name=n,this.parent=r,this.context=i,this.startTime=u,this.kind=f,this.status={_tag:"Started",startTime:u},this.attributes=new Map,this.traceId=r._tag==="Some"?r.value.traceId:b1(32),this.spanId=b1(16),this.links=Array.from(l)}end(n,r){this.status={_tag:"Ended",endTime:n,exit:r,startTime:this.status.startTime}}attribute(n,r){this.attributes.set(n,r)}event(n,r,i){this.events.push([n,r,i??{}])}addLinks(n){this.links.push(...n)}}const b6=v6({span:(t,n,r,i,l,u)=>new _6(t,n,r,i,l,u),context:t=>t()}),S6=t=>{if(t?.captureStackTrace===!1)return t;if(t?.captureStackTrace!==void 0&&typeof t.captureStackTrace!="boolean")return t;const n=Error.stackTraceLimit;Error.stackTraceLimit=3;const r=new Error;Error.stackTraceLimit=n;let i=!1;return{...t,captureStackTrace:()=>{if(i!==!1)return i;if(r.stack!==void 0){const l=r.stack.split(`
`);if(l[3]!==void 0)return i=l[3].trim(),i}}}},yy=rd()("effect/Tracer/DisablePropagation",{defaultValue:uf}),E6=x(zl(),Gr(oo,DL()),Gr(Z2,f6),Gr(h6,g6(Math.random())),Gr(YL,t6()),Gr(Hg,b6)),Hi=$t(Symbol.for("effect/DefaultServices/currentServices"),()=>N2(E6)),T6=t=>{const n=ua(t);return J2(r=>r.sleep(n))},O6=t=>Gt(n=>t(n.currentDefaultServices)),J2=t=>O6(n=>t(n.unsafeMap.get(oo.key))),R6=J2(t=>t.currentTimeMillis),M6=T6,w6=R6,A6=oo;function C6(t){return new Tr(t)}function x6(){return C6(new Map)}const S1=Symbol.for("effect/FiberRefs");class Tr{locals;[S1]=S1;constructor(n){this.locals=n}pipe(){return Et(this,arguments)}}const D6=(t,n,r,i=!1)=>{const l=t;let u=n,f=r,h=i,m;for(;m===void 0;)if(on(u)&&on(f)){const p=Ze(u)[0],g=is(u),v=Ze(f)[0],b=Ze(f)[1],O=is(f);p.startTimeMillis<v.startTimeMillis?(f=O,h=!0):p.startTimeMillis>v.startTimeMillis?u=g:p.id<v.id?(f=O,h=!0):p.id>v.id?u=g:m=[b,h]}else m=[l.initial,!0];return m},k6=E(3,(t,n,r)=>{const i=new Map(t.locals);return r.locals.forEach((l,u)=>{const f=l[0][1];if(!l[0][0][wt](n)){if(!i.has(u)){if(Ot(f,u.initial))return;i.set(u,[[n,u.join(u.initial,f)]]);return}const h=i.get(u),[m,p]=D6(u,h,l);if(p){const g=u.diff(m,f),v=h[0][1],b=u.join(v,u.patch(g)(v));if(!Ot(v,b)){let O;const R=h[0][0];R[wt](n)?O=[[R,b],...h.slice(1)]:O=[[n,b],...h],i.set(u,O)}}}}),new Tr(i)}),N6=E(2,(t,n)=>{const r=new Map;return W2(t,r,n),new Tr(r)}),W2=(t,n,r)=>{t.locals.forEach((i,l)=>{const u=i[0][1],f=l.patch(l.fork)(u);Ot(u,f)?n.set(l,i):n.set(l,[[r,f],...i])})},tO=E(2,(t,n)=>{const r=new Map(t.locals);return r.delete(n),new Tr(r)}),eO=E(2,(t,n)=>t.locals.has(n)?Tt(Ze(t.locals.get(n))[1]):yt()),Pl=E(2,(t,n)=>x(eO(t,n),zn(()=>n.initial))),gy=E(2,(t,{fiberId:n,fiberRef:r,value:i})=>{if(t.locals.size===0)return new Tr(new Map([[r,[[n,i]]]]));const l=new Map(t.locals);return vy(l,n,r,i),new Tr(l)}),vy=(t,n,r,i)=>{const l=t.get(r)??[];let u;if(on(l)){const[f,h]=Ze(l);if(f[wt](n)){if(Ot(h,i))return;u=[[n,i],...l.slice(1)]}else u=[[n,i],...l]}else u=[[n,i]];t.set(r,u)},z6=E(2,(t,{entries:n,forkAs:r})=>{if(t.locals.size===0)return new Tr(new Map(n));const i=new Map(t.locals);return r!==void 0&&W2(t,i,r),n.forEach(([l,u])=>{u.length===1?vy(i,u[0][0],l,u[0][1]):u.forEach(([f,h])=>{vy(i,f,l,h)})}),new Tr(i)}),E1=eO,L6=Pl,$6=z6,U6=x6,F6=xz,j6=Dz,B6=kz,q6=Nz,H6=D2,I6=k2,P6=zz,V6=Lz,G6=x(Nl,aD(t=>t.ordinal)),K6=rD(G6),Q6=t=>{switch(t){case"All":return F6;case"Debug":return I6;case"Error":return B6;case"Fatal":return j6;case"Info":return H6;case"Trace":return P6;case"None":return V6;case"Warning":return q6}},nO=t=>t.replace(/[\s="]/g,"_"),Y6=t=>n=>`${nO(n.label)}=${t-n.startTime}ms`,X6=nc,Y7=Qi,Z6=V3;class kd extends Z6{}const Cf=Symbol.for("effect/Readable"),aO=Symbol.for("effect/Ref"),rO={_A:t=>t};class J6 extends kd{ref;commit(){return this.get}[aO]=rO;[Cf]=Cf;constructor(n){super(),this.ref=n,this.get=ot(()=>Sr(this.ref))}get;modify(n){return ot(()=>{const r=Sr(this.ref),[i,l]=n(r);return r!==l&&dd(l)(this.ref),i})}}const sO=t=>new J6(fd(t)),xf=t=>ot(()=>sO(t)),gr=t=>t.get,Df=E(2,(t,n)=>t.modify(()=>[void 0,n])),W6=E(2,(t,n)=>t.modify(r=>[r,n])),iO=E(2,(t,n)=>t.modify(n)),_y=E(2,(t,n)=>t.modify(r=>[void 0,n(r)])),t5=xf,X7=gr,e5=W6,Z7=iO,J7=_y,oO="Empty",lO="Add",cO="Remove",uO="Update",fO="AndThen",n5={_tag:oO},Ig=(t,n)=>{const r=new Map(t.locals);let i=n5;for(const[l,u]of n.locals.entries()){const f=Ze(u)[1],h=r.get(l);if(h!==void 0){const m=Ze(h)[1];Ot(m,f)||(i=Cp({_tag:uO,fiberRef:l,patch:l.diff(m,f)})(i))}else i=Cp({_tag:lO,fiberRef:l,value:f})(i);r.delete(l)}for(const[l]of r.entries())i=Cp({_tag:cO,fiberRef:l})(i);return i},Cp=E(2,(t,n)=>({_tag:fO,first:t,second:n})),dO=E(3,(t,n,r)=>{let i=r,l=Hn(t);for(;on(l);){const u=Ze(l),f=is(l);switch(u._tag){case oO:{l=f;break}case lO:{i=gy(i,{fiberId:n,fiberRef:u.fiberRef,value:u.value}),l=f;break}case cO:{i=tO(i,u.fiberRef),l=f;break}case uO:{const h=Pl(i,u.fiberRef);i=gy(i,{fiberId:n,fiberRef:u.fiberRef,value:u.fiberRef.patch(u.patch)(h)}),l=f;break}case fO:{l=yf(u.first)(yf(u.second)(f));break}}}return i}),hO="effect/MetricLabel",by=Symbol.for(hO);class a5{key;value;[by]=by;_hash;constructor(n,r){this.key=n,this.value=r,this._hash=ve(hO+this.key+this.value)}[At](){return this._hash}[wt](n){return s5(n)&&this.key===n.key&&this.value===n.value}pipe(){return Et(this,arguments)}}const r5=(t,n)=>new a5(t,n),s5=t=>Rt(t,by),i5=t=>Ft(t,Tt),mO=t=>{let n,r;return typeof t=="function"?n=t:(n=t.try,r=t.catch),Wt(()=>{try{return pt(Be(n))}catch(i){return le(r?Be(()=>r(i)):new Md(i,"An unknown error occurred in Effect.try"))}})},o5=E(2,(t,n)=>{let r;return bz(t,i=>(r??=Object.keys(n),Rt(i,"_tag")&&NE(i._tag)&&r.includes(i._tag)),i=>n[i._tag](i))}),l5=t=>_O(t,yO,Ig),c5=t=>_O(t,Td(yO,Rz),([n,r],[i,l])=>[Ig(n,i),pr(r,l)]),pO=E(2,(t,n)=>Er(t,{onFailure:r=>pt(n.onFailure(r)),onSuccess:r=>pt(n.onSuccess(r))})),yO=Gt(t=>pt(t.getFiberRefs())),u5=t=>pO(t,{onFailure:Ip,onSuccess:Ip}),T1=E(2,(t,n)=>$n(t,{onFailure:r=>T2(()=>n(r)),onSuccess:pt})),f5=t=>x(B2(),X(n=>x(c5(t),M2(n),h5,Ft(r=>Ve(r,x(Ad(n),X(([i,l])=>We(Td(gO(i[0]),A2(i[1])),l)))))))),d5=t=>Ft(t,n=>!n),h5=t=>Ft(t5(!0),n=>ja(C2(t,e5(n,!1)))),gO=t=>b5((n,r)=>x(t,dO(n,r))),m5=t=>t.length>=1?fa((n,r)=>{try{t(r).then(i=>n(ne(i)),i=>n(_i(i)))}catch(i){n(_i(i))}}):fa(n=>{try{t().then(r=>n(ne(r)),r=>n(_i(r)))}catch(r){n(_i(r))}}),vO=E(3,(t,n,r)=>io(i=>xd(t,Gr(i,n,r)))),p5=M6,y5=pt(yt()),_O=E(3,(t,n,r)=>X(n,i=>X(t,l=>Ft(n,u=>[r(i,u),l])))),g5=E(2,(t,n)=>$n(t,{onFailure:r=>Ve(n(r),be(r)),onSuccess:pt})),v5=t=>{let n,r;typeof t=="function"?n=t:(n=t.try,r=t.catch);const i=l=>r?vd(()=>r(l)):le(new Md(l,"An unknown error occurred in Effect.tryPromise"));return n.length>=1?fa((l,u)=>{try{n(u).then(f=>l(ne(f)),f=>l(i(f)))}catch(f){l(i(f))}}):fa(l=>{try{n().then(u=>l(ne(u)),u=>l(i(u)))}catch(u){l(i(u))}})},_5=E(2,(t,n)=>X(t,r=>mO({try:()=>n.try(r),catch:n.catch}))),b5=t=>Gt(n=>(n.setFiberRefs(t(n.id(),n.getFiberRefs())),ae)),S5=E(2,(t,n)=>Wt(()=>n()?Ft(t,Tt):pt(yt()))),E5=t=>new Proxy({},{get(n,r,i){return(...l)=>X(t,u=>u[r](...l))}}),bO=BigInt(0),SO=rT(t=>ts(t.context,yy)?t._tag==="Span"?SO(t.parent):yt():Tt(t)),T5=(t,n,r)=>{const i=!t.getFiberRef(Zz)||r.context&&ts(r.context,yy),l=t.getFiberRef(Ba),u=r.parent?Tt(r.parent):r.root?yt():SO(Xi(l,Dd));let f;if(i)f=bL({name:n,parent:u,context:Gr(r.context??zl(),yy,!0)});else{const h=t.getFiberRef(Hi),m=ts(h,Hg),p=ts(h,A6),g=t.getFiberRef(L2),v=t.getFiberRefs(),b=E1(v,Jz),O=E1(v,Wz),R=O._tag==="Some"?r.links!==void 0?[...la(O.value),...r.links??[]]:la(O.value):r.links??os();f=m.span(n,u,r.context??zl(),R,g?p.unsafeCurrentTimeNanos():bO,r.kind??"internal"),b._tag==="Some"&&EN(b.value,(A,j)=>f.attribute(j,A)),r.attributes!==void 0&&Object.entries(r.attributes).forEach(([A,j])=>f.attribute(A,j))}return typeof r.captureStackTrace=="function"&&Of.set(f,r.captureStackTrace),f},O5=(t,n,r,i)=>ot(()=>{t.status._tag!=="Ended"&&(F2(n)&&Of.has(t)&&t.attribute("code.stacktrace",Of.get(t)()),t.end(i?r.unsafeCurrentTimeNanos():bO,n))}),R5=(t,...n)=>{const r=S6(n.length===1?void 0:n[0]),i=n[n.length-1];return Gt(l=>{const u=T5(l,t,r),f=l.getFiberRef(L2),h=ts(l.getFiberRef(Hi),oo);return ys(i(u),m=>O5(u,m,h,f))})},M5=E(2,(t,n)=>vO(t,Dd,n)),EO="Sequential",TO="Parallel",w5="ParallelN",Nd={_tag:EO},A5={_tag:TO},C5=t=>({_tag:w5,parallelism:t}),x5=t=>t._tag===EO,D5=t=>t._tag===TO,Sy=Nd,Ey=A5,Ty=C5,Vl=Ig,Gl=dO,zd="effect/FiberStatus",vs=Symbol.for(zd),kf="Done",O1="Running",R1="Suspended",k5=ve(`${zd}-${kf}`);class N5{[vs]=vs;_tag=kf;[At](){return k5}[wt](n){return Pg(n)&&n._tag===kf}}class z5{runtimeFlags;[vs]=vs;_tag=O1;constructor(n){this.runtimeFlags=n}[At](){return x(mt(zd),Lt(mt(this._tag)),Lt(mt(this.runtimeFlags)),ue(this))}[wt](n){return Pg(n)&&n._tag===O1&&this.runtimeFlags===n.runtimeFlags}}class L5{runtimeFlags;blockingOn;[vs]=vs;_tag=R1;constructor(n,r){this.runtimeFlags=n,this.blockingOn=r}[At](){return x(mt(zd),Lt(mt(this._tag)),Lt(mt(this.runtimeFlags)),Lt(mt(this.blockingOn)),ue(this))}[wt](n){return Pg(n)&&n._tag===R1&&this.runtimeFlags===n.runtimeFlags&&Ot(this.blockingOn,n.blockingOn)}}const $5=new N5,U5=t=>new z5(t),F5=(t,n)=>new L5(t,n),Pg=t=>Rt(t,vs),j5=t=>t._tag===kf,B5=$5,OO=U5,q5=F5,H5=j5,I5=Symbol.for("effect/Micro"),Nf=Symbol.for("effect/Micro/MicroExit"),M1=Symbol.for("effect/Micro/MicroCause"),P5={_E:Vt};class RO extends globalThis.Error{_tag;traces;[M1];constructor(n,r,i){const l=`MicroCause.${n}`;let u,f,h;if(r instanceof globalThis.Error){u=`(${l}) ${r.name}`,f=r.message;const m=f.split(`
`).length;h=r.stack?`(${l}) ${r.stack.split(`
`).slice(0,m+3).join(`
`)}`:`${u}: ${f}`}else u=l,f=Ai(r,0),h=`${u}: ${f}`;i.length>0&&(h+=`
    ${i.join(`
    `)}`),super(f),this._tag=n,this.traces=i,this[M1]=P5,this.name=u,this.stack=h}pipe(){return Et(this,arguments)}toString(){return this.stack}[re](){return this.stack}}class V5 extends RO{defect;constructor(n,r=[]){super("Die",n,r),this.defect=n}}const G5=(t,n=[])=>new V5(t,n);class K5 extends RO{constructor(n=[]){super("Interrupt","interrupted",n)}}const Q5=(t=[])=>new K5(t),Y5=t=>t._tag==="Interrupt",w1=Symbol.for("effect/Micro/MicroFiber"),X5={_A:Vt,_E:Vt};class Z5{context;interruptible;[w1];_stack=[];_observers=[];_exit;_children;currentOpCount=0;constructor(n,r=!0){this.context=n,this.interruptible=r,this[w1]=X5}getRef(n){return GD(this.context,n)}addObserver(n){return this._exit?(n(this._exit),Ip):(this._observers.push(n),()=>{const r=this._observers.indexOf(n);r>=0&&this._observers.splice(r,1)})}_interrupted=!1;unsafeInterrupt(){this._exit||(this._interrupted=!0,this.interruptible&&this.evaluate(Yg))}unsafePoll(){return this._exit}evaluate(n){if(this._exit)return;if(this._yielded!==void 0){const l=this._yielded;this._yielded=void 0,l()}const r=this.runLoop(n);if(r===qu)return;const i=A1.interruptChildren&&A1.interruptChildren(this);if(i!==void 0)return this.evaluate(Lf(i,()=>r));this._exit=r;for(let l=0;l<this._observers.length;l++)this._observers[l](r);this._observers.length=0}runLoop(n){let r=!1,i=n;this.currentOpCount=0;try{for(;;){if(this.currentOpCount++,!r&&this.getRef(Xg).shouldYield(this)){r=!0;const l=i;i=Lf(n$,()=>l)}if(i=i[Oy](this),i===qu){const l=this._yielded;return Nf in l?(this._yielded=void 0,l):qu}}}catch(l){return Rt(i,Oy)?Ry(l):Ry(`MicroFiber.runLoop: Not a valid effect: ${String(i)}`)}}getCont(n){for(;;){const r=this._stack.pop();if(!r)return;const i=r[zf]&&r[zf](this);if(i)return{[n]:i};if(r[n])return r}}_yielded=void 0;yieldWith(n){return this._yielded=n,qu}children(){return this._children??=new Set}}const A1=$t("effect/Micro/fiberMiddleware",()=>({interruptChildren:void 0})),MO=Symbol.for("effect/Micro/identifier"),Ae=Symbol.for("effect/Micro/args"),Oy=Symbol.for("effect/Micro/evaluate"),Ii=Symbol.for("effect/Micro/successCont"),Mi=Symbol.for("effect/Micro/failureCont"),zf=Symbol.for("effect/Micro/ensureCont"),qu=Symbol.for("effect/Micro/Yield"),J5={_A:Vt,_E:Vt,_R:Vt},W5={...X6,_op:"Micro",[I5]:J5,pipe(){return Et(this,arguments)},[Symbol.iterator](){return new FE(new tc(this))},toJSON(){return{_id:"Micro",op:this[MO],...Ae in this?{args:this[Ae]}:void 0}},toString(){return _e(this)},[re](){return _e(this)}};function t$(t){return Ry("Micro.evaluate: Not implemented")}const Ld=t=>({...W5,[MO]:t.op,[Oy]:t.eval??t$,[Ii]:t.contA,[Mi]:t.contE,[zf]:t.ensure}),Vg=t=>{const n=Ld(t);return function(){const r=Object.create(n);return r[Ae]=t.single===!1?arguments:arguments[0],r}},wO=t=>{const n={...Ld(t),[Nf]:Nf,_tag:t.op,get[t.prop](){return this[Ae]},toJSON(){return{_id:"MicroExit",_tag:t.op,[t.prop]:this[Ae]}},[wt](r){return s$(r)&&r._tag===t.op&&Ot(this[Ae],r[Ae])},[At](){return ue(this,Lt(ve(t.op))(mt(this[Ae])))}};return function(r){const i=Object.create(n);return i[Ae]=r,i[Ii]=void 0,i[Mi]=void 0,i[zf]=void 0,i}},Gg=wO({op:"Success",prop:"value",eval(t){const n=t.getCont(Ii);return n?n[Ii](this[Ae],t):t.yieldWith(this)}}),AO=wO({op:"Failure",prop:"cause",eval(t){let n=t.getCont(Mi);for(;Y5(this[Ae])&&n&&t.interruptible;)n=t.getCont(Mi);return n?n[Mi](this[Ae],t):t.yieldWith(this)}}),e$=Vg({op:"Yield",eval(t){let n=!1;return t.getRef(Xg).scheduleTask(()=>{n||t.evaluate(i$)},this[Ae]??0),t.yieldWith(()=>{n=!0})}}),n$=e$(0),a$=Gg(void 0),Kg=Vg({op:"WithMicroFiber",eval(t){return this[Ae](t)}}),Lf=E(2,(t,n)=>{const r=Object.create(r$);return r[Ae]=t,r[Ii]=n,r}),r$=Ld({op:"OnSuccess",eval(t){return t._stack.push(this),this[Ae]}}),s$=t=>Rt(t,Nf),CO=Gg,Qg=AO,Yg=Qg(Q5()),Ry=t=>Qg(G5(t)),i$=CO(void 0),o$="setImmediate"in globalThis?globalThis.setImmediate:t=>setTimeout(t,0);class xO{tasks=[];running=!1;scheduleTask(n,r){this.tasks.push(n),this.running||(this.running=!0,o$(this.afterScheduled))}afterScheduled=()=>{this.running=!1,this.runTasks()};runTasks(){const n=this.tasks;this.tasks=[];for(let r=0,i=n.length;r<i;r++)n[r]()}shouldYield(n){return n.currentOpCount>=n.getRef(u$)}flush(){for(;this.tasks.length>0;)this.runTasks()}}const l$=E(2,(t,n)=>Kg(r=>{const i=r.context;return r.context=n(i),h$(t,()=>(r.context=i,a$))})),c$=E(2,(t,n)=>l$(t,Zi(n)));class u$ extends rd()("effect/Micro/currentMaxOpsBeforeYield",{defaultValue:()=>2048}){}class Xg extends rd()("effect/Micro/currentScheduler",{defaultValue:()=>new xO}){}const f$=E(2,(t,n)=>{const r=Object.create(d$);return r[Ae]=t,r[Ii]=n.onSuccess,r[Mi]=n.onFailure,r}),d$=Ld({op:"OnSuccessAndFailure",eval(t){return t._stack.push(this),this[Ae]}}),h$=E(2,(t,n)=>p$(r=>f$(r(t),{onFailure:i=>Lf(n(Qg(i)),()=>AO(i)),onSuccess:i=>Lf(n(CO(i)),()=>Gg(i))}))),DO=Vg({op:"SetInterruptible",ensure(t){if(t.interruptible=this[Ae],t._interrupted&&t.interruptible)return()=>Yg}}),m$=t=>Kg(n=>n.interruptible?t:(n.interruptible=!0,n._stack.push(DO(!1)),n._interrupted?Yg:t)),p$=t=>Kg(n=>n.interruptible?(n.interruptible=!1,n._stack.push(DO(!0)),t(m$)):t(Vt)),y$=(t,n)=>{const r=new Z5(Xg.context(new xO));return r.evaluate(t),r};class kO{buckets=[];scheduleTask(n,r){const i=this.buckets.length;let l,u=0;for(;u<i&&this.buckets[u][0]<=r;u++)l=this.buckets[u];l&&l[0]===r?l[1].push(n):u===i?this.buckets.push([r,[n]]):this.buckets.splice(u,0,[r,[n]])}}class g${maxNextTickBeforeTimer;running=!1;tasks=new kO;constructor(n){this.maxNextTickBeforeTimer=n}starveInternal(n){const r=this.tasks.buckets;this.tasks.buckets=[];for(const[i,l]of r)for(let u=0;u<l.length;u++)l[u]();this.tasks.buckets.length===0?this.running=!1:this.starve(n)}starve(n=0){n>=this.maxNextTickBeforeTimer?setTimeout(()=>this.starveInternal(0),0):Promise.resolve(void 0).then(()=>this.starveInternal(n+1))}shouldYield(n){return n.currentOpCount>n.getFiberRef(z2)?n.getFiberRef(so):!1}scheduleTask(n,r){this.tasks.scheduleTask(n,r),this.running||(this.running=!0,this.starve())}}const NO=$t(Symbol.for("effect/Scheduler/defaultScheduler"),()=>new g$(2048));class v${tasks=new kO;deferred=!1;scheduleTask(n,r){this.deferred?NO.scheduleTask(n,r):this.tasks.scheduleTask(n,r)}shouldYield(n){return n.currentOpCount>n.getFiberRef(z2)?n.getFiberRef(so):!1}flush(){for(;this.tasks.buckets.length>0;){const n=this.tasks.buckets;this.tasks.buckets=[];for(const[r,i]of n)for(let l=0;l<i.length;l++)i[l]()}this.deferred=!0}}const Zg=$t(Symbol.for("effect/FiberRef/currentScheduler"),()=>qe(NO)),zO=$t(Symbol.for("effect/FiberRef/currentRequestMap"),()=>qe(new Map)),C1=(t,n,r,i)=>{switch(t){case void 0:return n();case"unbounded":return r();case"inherit":return Od(Qz,l=>l==="unbounded"?r():l>1?i(l):n());default:return t>1?i(t):n()}},Jg="InterruptSignal",Wg="Stateful",t0="Resume",e0="YieldNow",xp=t=>({_tag:Jg,cause:t}),Ju=t=>({_tag:Wg,onFiber:t}),fi=t=>({_tag:t0,effect:t}),_$=()=>({_tag:e0}),b$="effect/FiberScope",$f=Symbol.for(b$);class S${[$f]=$f;fiberId=ds;roots=new Set;add(n,r){this.roots.add(r),r.addObserver(()=>{this.roots.delete(r)})}}class E${fiberId;parent;[$f]=$f;constructor(n,r){this.fiberId=n,this.parent=r}add(n,r){this.parent.tell(Ju(i=>{i.addChild(r),r.addObserver(()=>{i.removeChild(r)})}))}}const T$=t=>new E$(t.id(),t),$d=$t(Symbol.for("effect/FiberScope/Global"),()=>new S$),O$="effect/Fiber",R$=Symbol.for(O$),M$={_E:t=>t,_A:t=>t},w$="effect/Fiber",A$=Symbol.for(w$),C$=t=>t.await,x$=t=>t.inheritAll,Kl=t=>Dg(_d(t.await),t.inheritAll);({...Qi});const Ir="effect/FiberCurrent",D$="effect/Logger",k$=Symbol.for(D$),N$={_Message:t=>t,_Output:t=>t},n0=t=>({[k$]:N$,log:t,pipe(){return Et(this,arguments)}}),z$=/^[^\s"=]*$/,L$=(t,n)=>({annotations:r,cause:i,date:l,fiberId:u,logLevel:f,message:h,spans:m})=>{const p=R=>R.match(z$)?R:t(R),g=(R,A)=>`${nO(R)}=${p(A)}`,v=(R,A)=>" "+g(R,A);let b=g("timestamp",l.toISOString());b+=v("level",f.label),b+=v("fiber",qT(u));const O=fD(h);for(let R=0;R<O.length;R++)b+=v("message",Ai(O[R],n));tz(i)||(b+=v("cause",ic(i,{renderErrorCause:!0})));for(const R of m)b+=" "+Y6(l.getTime())(R);for(const[R,A]of r)b+=v(R,Ai(A,n));return b},$$=t=>`"${t.replace(/\\([\s\S])|(")/g,"\\$1$2")}"`,U$=n0(L$($$)),F$=typeof process=="object"&&process!==null&&typeof process.stdout=="object"&&process.stdout!==null;F$&&process.stdout.isTTY;const LO="effect/MetricBoundaries",My=Symbol.for(LO);class j${values;[My]=My;constructor(n){this.values=n,this._hash=x(ve(LO),Lt(ec(this.values)))}_hash;[At](){return this._hash}[wt](n){return B$(n)&&Ot(this.values,n.values)}pipe(){return Et(this,arguments)}}const B$=t=>Rt(t,My),q$=t=>{const n=x(t,sT(Je(Number.POSITIVE_INFINITY)),DD);return new j$(n)},H$=t=>x(uD(t.count-1,n=>t.start*Math.pow(t.factor,n)),Ji,q$),I$="effect/MetricKeyType",$O=Symbol.for(I$),UO="effect/MetricKeyType/Counter",wy=Symbol.for(UO),P$="effect/MetricKeyType/Frequency",V$=Symbol.for(P$),G$="effect/MetricKeyType/Gauge",K$=Symbol.for(G$),FO="effect/MetricKeyType/Histogram",Ay=Symbol.for(FO),Q$="effect/MetricKeyType/Summary",Y$=Symbol.for(Q$),jO={_In:t=>t,_Out:t=>t};class X${incremental;bigint;[$O]=jO;[wy]=wy;constructor(n,r){this.incremental=n,this.bigint=r,this._hash=ve(UO)}_hash;[At](){return this._hash}[wt](n){return BO(n)}pipe(){return Et(this,arguments)}}class Z${boundaries;[$O]=jO;[Ay]=Ay;constructor(n){this.boundaries=n,this._hash=x(ve(FO),Lt(mt(this.boundaries)))}_hash;[At](){return this._hash}[wt](n){return qO(n)&&Ot(this.boundaries,n.boundaries)}pipe(){return Et(this,arguments)}}const J$=t=>new X$(t?.incremental??!1,t?.bigint??!1),W$=t=>new Z$(t),BO=t=>Rt(t,wy),tU=t=>Rt(t,V$),eU=t=>Rt(t,K$),qO=t=>Rt(t,Ay),nU=t=>Rt(t,Y$),aU="effect/MetricKey",HO=Symbol.for(aU),rU={_Type:t=>t},sU=tg(Ot);class a0{name;keyType;description;tags;[HO]=rU;constructor(n,r,i,l=[]){this.name=n,this.keyType=r,this.description=i,this.tags=l,this._hash=x(ve(this.name+this.description),Lt(mt(this.keyType)),Lt(ec(this.tags)))}_hash;[At](){return this._hash}[wt](n){return iU(n)&&this.name===n.name&&Ot(this.keyType,n.keyType)&&Ot(this.description,n.description)&&sU(this.tags,n.tags)}pipe(){return Et(this,arguments)}}const iU=t=>Rt(t,HO),oU=(t,n)=>new a0(t,J$(n),nd(n?.description)),lU=(t,n,r)=>new a0(t,W$(n),nd(r)),cU=E(2,(t,n)=>n.length===0?t:new a0(t.name,t.keyType,t.description,Qu(t.tags,n))),uU="effect/MetricState",fc=Symbol.for(uU),IO="effect/MetricState/Counter",Cy=Symbol.for(IO),PO="effect/MetricState/Frequency",xy=Symbol.for(PO),VO="effect/MetricState/Gauge",Dy=Symbol.for(VO),GO="effect/MetricState/Histogram",ky=Symbol.for(GO),KO="effect/MetricState/Summary",Ny=Symbol.for(KO),dc={_A:t=>t};class fU{count;[fc]=dc;[Cy]=Cy;constructor(n){this.count=n}[At](){return x(mt(IO),Lt(mt(this.count)),ue(this))}[wt](n){return EU(n)&&this.count===n.count}pipe(){return Et(this,arguments)}}const dU=tg(Ot);class hU{occurrences;[fc]=dc;[xy]=xy;constructor(n){this.occurrences=n}_hash;[At](){return x(ve(PO),Lt(ec(ce(this.occurrences.entries()))),ue(this))}[wt](n){return TU(n)&&dU(ce(this.occurrences.entries()),ce(n.occurrences.entries()))}pipe(){return Et(this,arguments)}}class mU{value;[fc]=dc;[Dy]=Dy;constructor(n){this.value=n}[At](){return x(mt(VO),Lt(mt(this.value)),ue(this))}[wt](n){return OU(n)&&this.value===n.value}pipe(){return Et(this,arguments)}}class pU{buckets;count;min;max;sum;[fc]=dc;[ky]=ky;constructor(n,r,i,l,u){this.buckets=n,this.count=r,this.min=i,this.max=l,this.sum=u}[At](){return x(mt(GO),Lt(mt(this.buckets)),Lt(mt(this.count)),Lt(mt(this.min)),Lt(mt(this.max)),Lt(mt(this.sum)),ue(this))}[wt](n){return RU(n)&&Ot(this.buckets,n.buckets)&&this.count===n.count&&this.min===n.min&&this.max===n.max&&this.sum===n.sum}pipe(){return Et(this,arguments)}}class yU{error;quantiles;count;min;max;sum;[fc]=dc;[Ny]=Ny;constructor(n,r,i,l,u,f){this.error=n,this.quantiles=r,this.count=i,this.min=l,this.max=u,this.sum=f}[At](){return x(mt(KO),Lt(mt(this.error)),Lt(mt(this.quantiles)),Lt(mt(this.count)),Lt(mt(this.min)),Lt(mt(this.max)),Lt(mt(this.sum)),ue(this))}[wt](n){return MU(n)&&this.error===n.error&&Ot(this.quantiles,n.quantiles)&&this.count===n.count&&this.min===n.min&&this.max===n.max&&this.sum===n.sum}pipe(){return Et(this,arguments)}}const gU=t=>new fU(t),vU=t=>new hU(t),_U=t=>new mU(t),bU=t=>new pU(t.buckets,t.count,t.min,t.max,t.sum),SU=t=>new yU(t.error,t.quantiles,t.count,t.min,t.max,t.sum),EU=t=>Rt(t,Cy),TU=t=>Rt(t,xy),OU=t=>Rt(t,Dy),RU=t=>Rt(t,ky),MU=t=>Rt(t,Ny),wU="effect/MetricHook",AU=Symbol.for(wU),CU={_In:t=>t,_Out:t=>t},hc=t=>({[AU]:CU,pipe(){return Et(this,arguments)},...t}),x1=BigInt(0),xU=t=>{let n=t.keyType.bigint?x1:0;const r=t.keyType.incremental?t.keyType.bigint?l=>l>=x1:l=>l>=0:l=>!0,i=l=>{r(l)&&(n=n+l)};return hc({get:()=>gU(n),update:i,modify:i})},DU=t=>{const n=new Map;for(const i of t.keyType.preregisteredWords)n.set(i,0);const r=i=>{const l=n.get(i)??0;n.set(i,l+1)};return hc({get:()=>vU(n),update:r,modify:r})},kU=(t,n)=>{let r=n;return hc({get:()=>_U(r),update:i=>{r=i},modify:i=>{r=r+i}})},NU=t=>{const n=t.keyType.boundaries.values,r=n.length,i=new Uint32Array(r+1),l=new Float32Array(r);let u=0,f=0,h=Number.MAX_VALUE,m=Number.MIN_VALUE;x(n,gf(Nl),Wr((v,b)=>{l[b]=v}));const p=v=>{let b=0,O=r;for(;b!==O;){const R=Math.floor(b+(O-b)/2),A=l[R];v<=A?O=R:b=R,O===b+1&&(v<=l[b]?O=b:b=O)}i[b]=i[b]+1,u=u+1,f=f+v,v<h&&(h=v),v>m&&(m=v)},g=()=>{const v=Jy(r);let b=0;for(let O=0;O<r;O++){const R=l[O],A=i[O];b=b+A,v[O]=[R,b]}return v};return hc({get:()=>bU({buckets:g(),count:u,min:h,max:m,sum:f}),update:p,modify:p})},zU=t=>{const{error:n,maxAge:r,maxSize:i,quantiles:l}=t.keyType,u=x(l,gf(Nl)),f=Jy(i);let h=0,m=0,p=0,g=Number.MAX_VALUE,v=Number.MIN_VALUE;const b=R=>{const A=[];let j=0;for(;j!==i-1;){const U=f[j];if(U!=null){const[B,q]=U,G=_f(R-B);Tk(G,OT)&&Ek(G,r)&&A.push(q)}j=j+1}return LU(n,u,gf(A,Nl))},O=(R,A)=>{if(i>0){h=h+1;const j=h%i;f[j]=[A,R]}m=m+1,p=p+R,R<g&&(g=R),R>v&&(v=R)};return hc({get:()=>SU({error:n,quantiles:b(Date.now()),count:m,min:g,max:v,sum:p}),update:([R,A])=>O(R,A),modify:([R,A])=>O(R,A)})},LU=(t,n,r)=>{const i=r.length;if(!on(n))return os();const l=n[0],u=n.slice(1),f=D1(t,i,yt(),0,l,r),h=Hn(f);return u.forEach(m=>{h.push(D1(t,i,f.value,f.consumed,m,f.rest))}),Wr(h,m=>[m.quantile,m.value])},D1=(t,n,r,i,l,u)=>{let f=t,h=n,m=r,p=i,g=l,v=u,b=t,O=n,R=r,A=i,j=l,U=u;for(;;){if(!on(v))return{quantile:g,value:yt(),consumed:p,rest:[]};if(g===1)return{quantile:g,value:Tt(lT(v)),consumed:p+v.length,rest:[]};const B=Ze(v),q=bD(v,N=>N===B),G=g*h,Z=f/2*G,K=p+q[0].length,Q=Math.abs(K-G);if(K<G-Z){b=f,O=h,R=wl(v),A=K,j=g,U=q[1],f=b,h=O,m=R,p=A,g=j,v=U;continue}if(K>G+Z){const N=$e(m)?Tt(B):m;return{quantile:g,value:N,consumed:p,rest:v}}switch(m._tag){case"None":{b=f,O=h,R=wl(v),A=K,j=g,U=q[1],f=b,h=O,m=R,p=A,g=j,v=U;continue}case"Some":{const N=Math.abs(G-m.value);if(Q<N){b=f,O=h,R=wl(v),A=K,j=g,U=q[1],f=b,h=O,m=R,p=A,g=j,v=U;continue}return{quantile:g,value:Tt(m.value),consumed:p,rest:v}}}}throw new Error("BUG: MetricHook.resolveQuantiles - please report an issue at https://github.com/Effect-TS/effect/issues")},$U="effect/MetricPair",UU=Symbol.for($U),FU={_Type:t=>t},jU=(t,n)=>({[UU]:FU,metricKey:t,metricState:n,pipe(){return Et(this,arguments)}}),BU="effect/MetricRegistry",k1=Symbol.for(BU);class qU{[k1]=k1;map=OL();snapshot(){const n=[];for(const[r,i]of this.map)n.push(jU(r,i.get()));return n}get(n){const r=x(this.map,Vr(n),Pr);if(r==null){if(BO(n.keyType))return this.getCounter(n);if(eU(n.keyType))return this.getGauge(n);if(tU(n.keyType))return this.getFrequency(n);if(qO(n.keyType))return this.getHistogram(n);if(nU(n.keyType))return this.getSummary(n);throw new Error("BUG: MetricRegistry.get - unknown MetricKeyType - please report an issue at https://github.com/Effect-TS/effect/issues")}else return r}getCounter(n){let r=x(this.map,Vr(n),Pr);if(r==null){const i=xU(n);x(this.map,hl(n))||x(this.map,ml(n,i)),r=i}return r}getFrequency(n){let r=x(this.map,Vr(n),Pr);if(r==null){const i=DU(n);x(this.map,hl(n))||x(this.map,ml(n,i)),r=i}return r}getGauge(n){let r=x(this.map,Vr(n),Pr);if(r==null){const i=kU(n,n.keyType.bigint?BigInt(0):0);x(this.map,hl(n))||x(this.map,ml(n,i)),r=i}return r}getHistogram(n){let r=x(this.map,Vr(n),Pr);if(r==null){const i=NU(n);x(this.map,hl(n))||x(this.map,ml(n,i)),r=i}return r}getSummary(n){let r=x(this.map,Vr(n),Pr);if(r==null){const i=zU(n);x(this.map,hl(n))||x(this.map,ml(n,i)),r=i}return r}}const HU=()=>new qU,IU="effect/Metric",PU=Symbol.for(IU),VU={_Type:t=>t,_In:t=>t,_Out:t=>t},N1=$t(Symbol.for("effect/Metric/globalMetricRegistry"),()=>HU()),QO=function(t,n,r,i){const l=Object.assign(u=>Ag(u,f=>YU(l,f)),{[PU]:VU,keyType:t,unsafeUpdate:n,unsafeValue:r,unsafeModify:i,register(){return this.unsafeValue([]),this},pipe(){return Et(this,arguments)}});return l},Ud=(t,n)=>YO(oU(t,n)),YO=t=>{let n;const r=new WeakMap,i=l=>{if(l.length===0)return n!==void 0||(n=N1.get(t)),n;let u=r.get(l);return u!==void 0||(u=N1.get(cU(t,l)),r.set(l,u)),u};return QO(t.keyType,(l,u)=>i(u).update(l),l=>i(l).get(),(l,u)=>i(u).modify(l))},GU=(t,n,r)=>YO(lU(t,n,r)),KU=E(3,(t,n,r)=>QU(t,[r5(n,r)])),QU=E(2,(t,n)=>QO(t.keyType,(r,i)=>t.unsafeUpdate(r,Qu(n,i)),r=>t.unsafeValue(Qu(n,r)),(r,i)=>t.unsafeModify(r,Qu(n,i)))),YU=E(2,(t,n)=>Od(uy,r=>ot(()=>t.unsafeUpdate(n,r))));({...Xy});const XU=E(2,(t,n)=>Od(zO,r=>ot(()=>{if(r.has(t)){const i=r.get(t);i.state.completed||(i.state.completed=!0,q2(i.result,n))}}))),ZU="effect/Supervisor",Fd=Symbol.for(ZU),r0={_T:t=>t};class jd{underlying;value0;[Fd]=r0;constructor(n,r){this.underlying=n,this.value0=r}get value(){return this.value0}onStart(n,r,i,l){this.underlying.onStart(n,r,i,l)}onEnd(n,r){this.underlying.onEnd(n,r)}onEffect(n,r){this.underlying.onEffect(n,r)}onSuspend(n){this.underlying.onSuspend(n)}onResume(n){this.underlying.onResume(n)}map(n){return new jd(this,x(this.value,Ft(n)))}zip(n){return new Bd(this,n)}}class Bd{left;right;_tag="Zip";[Fd]=r0;constructor(n,r){this.left=n,this.right=r}get value(){return Td(this.left.value,this.right.value)}onStart(n,r,i,l){this.left.onStart(n,r,i,l),this.right.onStart(n,r,i,l)}onEnd(n,r){this.left.onEnd(n,r),this.right.onEnd(n,r)}onEffect(n,r){this.left.onEffect(n,r),this.right.onEffect(n,r)}onSuspend(n){this.left.onSuspend(n),this.right.onSuspend(n)}onResume(n){this.left.onResume(n),this.right.onResume(n)}map(n){return new jd(this,x(this.value,Ft(n)))}zip(n){return new Bd(this,n)}}const XO=t=>Rt(t,Fd)&&LE(t,"Zip");class JU{effect;[Fd]=r0;constructor(n){this.effect=n}get value(){return this.effect}onStart(n,r,i,l){}onEnd(n,r){}onEffect(n,r){}onSuspend(n){}onResume(n){}map(n){return new jd(this,x(this.value,Ft(n)))}zip(n){return new Bd(this,n)}onRun(n,r){return n()}}const WU=t=>new JU(t),qd=$t("effect/Supervisor/none",()=>WU(ae)),tF=Wi,ZO="Empty",JO="AddSupervisor",WO="RemoveSupervisor",tR="AndThen",Cl={_tag:ZO},Wu=(t,n)=>({_tag:tR,first:t,second:n}),eF=(t,n)=>nF(n,Je(t)),nF=(t,n)=>{let r=t,i=n;for(;Ua(i);){const l=oa(i);switch(l._tag){case ZO:{i=ka(i);break}case JO:{r=r.zip(l.supervisor),i=ka(i);break}case WO:{r=zy(r,l.supervisor),i=ka(i);break}case tR:{i=Ln(l.first)(Ln(l.second)(ka(i)));break}}}return r},zy=(t,n)=>Ot(t,n)?qd:XO(t)?zy(t.left,n).zip(zy(t.right,n)):t,Uf=t=>Ot(t,qd)?us():XO(t)?x(Uf(t.left),Ul(Uf(t.right))):hg(t),aF=(t,n)=>{if(Ot(t,n))return Cl;const r=Uf(t),i=Uf(n),l=x(i,VS(r),Fl(Cl,(f,h)=>Wu(f,{_tag:JO,supervisor:h}))),u=x(r,VS(i),Fl(Cl,(f,h)=>Wu(f,{_tag:WO,supervisor:h})));return Wu(l,u)},rF=tF({empty:Cl,patch:eF,combine:Wu,diff:aF}),sF=Ud("effect_fiber_started",{incremental:!0}),z1=Ud("effect_fiber_active"),iF=Ud("effect_fiber_successes",{incremental:!0}),oF=Ud("effect_fiber_failures",{incremental:!0}),lF=KU(GU("effect_fiber_lifetimes",H$({start:.5,factor:2,count:35})),"time_unit","milliseconds"),pl="Continue",cF="Done",L1="Yield",uF={_E:t=>t,_A:t=>t},Hu=t=>{throw new Error(`BUG: FiberRuntime - ${Ai(t)} - please report an issue at https://github.com/Effect-TS/effect/issues`)},xa=Symbol.for("effect/internal/fiberRuntime/YieldedOp"),Da=$t("effect/internal/fiberRuntime/yieldedOpChannel",()=>({currentOp:null})),yl={[hf]:(t,n,r)=>Be(()=>n.effect_instruction_i1(r)),OnStep:(t,n,r)=>ne(ne(r)),[mf]:(t,n,r)=>Be(()=>n.effect_instruction_i2(r)),[Yy]:(t,n,r)=>(t.patchRuntimeFlags(t.currentRuntimeFlags,n.patch),hr(t.currentRuntimeFlags)&&t.isInterrupted()?Xt(t.getInterruptedCause()):ne(r)),[pf]:(t,n,r)=>(Be(()=>n.effect_instruction_i2(r)),Be(()=>n.effect_instruction_i0())?(t.pushStack(n),Be(()=>n.effect_instruction_i1())):ae),[Rl]:(t,n,r)=>{const i=Be(()=>n.effect_instruction_i0.next(r));return i.done?ne(i.value):(t.pushStack(n),x3(i.value))}},fF={[Jg]:(t,n,r,i)=>(t.processNewInterruptSignal(i.cause),hr(n)?Xt(i.cause):r),[t0]:(t,n,r,i)=>{throw new Error("It is illegal to have multiple concurrent run loops in a single fiber")},[Wg]:(t,n,r,i)=>(i.onFiber(t,OO(n)),r),[e0]:(t,n,r,i)=>X(xg(),()=>r)},dF=t=>bd(U4(t),n=>as(Z4(n),([r,i])=>{const l=new Map,u=[];for(const h of i){u.push(la(h));for(const m of h)l.set(m.request,m)}const f=u.flat();return uc(LF(r.runAll(u),f,()=>f.forEach(h=>{h.listeners.interrupted=!0})),zO,l)},!1,!1)),hF=Xf();class eR extends kd{[R$]=M$;[A$]=uF;_fiberRefs;_fiberId;_queue=new Array;_children=null;_observers=new Array;_running=!1;_stack=[];_asyncInterruptor=null;_asyncBlockingOn=null;_exitValue=null;_steps=[];_isYielding=!1;currentRuntimeFlags;currentOpCount=0;currentSupervisor;currentScheduler;currentTracer;currentSpan;currentContext;currentDefaultServices;constructor(n,r,i){if(super(),this.currentRuntimeFlags=i,this._fiberId=n,this._fiberRefs=r,XS(i)){const l=this.getFiberRef(uy);sF.unsafeUpdate(1,l),z1.unsafeUpdate(1,l)}this.refreshRefCache()}commit(){return Kl(this)}id(){return this._fiberId}resume(n){this.tell(fi(n))}get status(){return this.ask((n,r)=>r)}get runtimeFlags(){return this.ask((n,r)=>H5(r)?n.currentRuntimeFlags:r.runtimeFlags)}scope(){return T$(this)}get children(){return this.ask(n=>Array.from(n.getChildren()))}getChildren(){return this._children===null&&(this._children=new Set),this._children}getInterruptedCause(){return this.getFiberRef(Bu)}fiberRefs(){return this.ask(n=>n.getFiberRefs())}ask(n){return Wt(()=>{const r=j2(this._fiberId);return this.tell(Ju((i,l)=>{q2(r,ot(()=>n(i,l)))})),Ad(r)})}tell(n){this._queue.push(n),this._running||(this._running=!0,this.drainQueueLaterOnExecutor())}get await(){return fa(n=>{const r=i=>n(pt(i));return this.tell(Ju((i,l)=>{i._exitValue!==null?r(this._exitValue):i.addObserver(r)})),ot(()=>this.tell(Ju((i,l)=>{i.removeObserver(r)})))},this.id())}get inheritAll(){return Gt((n,r)=>{const i=n.id(),l=n.getFiberRefs(),u=r.runtimeFlags,f=this.getFiberRefs(),h=k6(l,i,f);n.setFiberRefs(h);const m=n.getFiberRef(j1),p=x(pr(u,m),JS(to),JS(oy));return A2(p)})}get poll(){return ot(()=>nd(this._exitValue))}unsafePoll(){return this._exitValue}interruptAsFork(n){return ot(()=>this.tell(xp(ra(n))))}unsafeInterruptAsFork(n){this.tell(xp(ra(n)))}addObserver(n){this._exitValue!==null?n(this._exitValue):this._observers.push(n)}removeObserver(n){this._observers=this._observers.filter(r=>r!==n)}getFiberRefs(){return this.setFiberRef(j1,this.currentRuntimeFlags),this._fiberRefs}unsafeDeleteFiberRef(n){this._fiberRefs=tO(this._fiberRefs,n)}getFiberRef(n){return this._fiberRefs.locals.has(n)?this._fiberRefs.locals.get(n)[0][1]:n.initial}setFiberRef(n,r){this._fiberRefs=gy(this._fiberRefs,{fiberId:this._fiberId,fiberRef:n,value:r}),this.refreshRefCache()}refreshRefCache(){this.currentDefaultServices=this.getFiberRef(Hi),this.currentTracer=this.currentDefaultServices.unsafeMap.get(Hg.key),this.currentSupervisor=this.getFiberRef(kF),this.currentScheduler=this.getFiberRef(Zg),this.currentContext=this.getFiberRef(Ba),this.currentSpan=this.currentContext.unsafeMap.get(Dd.key)}setFiberRefs(n){this._fiberRefs=n,this.refreshRefCache()}addChild(n){this.getChildren().add(n)}removeChild(n){this.getChildren().delete(n)}transferChildren(n){const r=this._children;if(this._children=null,r!==null&&r.size>0)for(const i of r)i._exitValue===null&&n.add(this.currentRuntimeFlags,i)}drainQueueOnCurrentThread(){let n=!0;for(;n;){let r=pl;const i=globalThis[Ir];globalThis[Ir]=this;try{for(;r===pl;)r=this._queue.length===0?cF:this.evaluateMessageWhileSuspended(this._queue.splice(0,1)[0])}finally{this._running=!1,globalThis[Ir]=i}this._queue.length>0&&!this._running?(this._running=!0,r===L1?(this.drainQueueLaterOnExecutor(),n=!1):n=!0):n=!1}}drainQueueLaterOnExecutor(){this.currentScheduler.scheduleTask(this.run,this.getFiberRef(so))}drainQueueWhileRunning(n,r){let i=r;for(;this._queue.length>0;){const l=this._queue.splice(0,1)[0];i=fF[l._tag](this,n,i,l)}return i}isInterrupted(){return!nz(this.getFiberRef(Bu))}addInterruptedCause(n){const r=this.getFiberRef(Bu);this.setFiberRef(Bu,Ke(r,n))}processNewInterruptSignal(n){this.addInterruptedCause(n),this.sendInterruptSignalToAllChildren()}sendInterruptSignalToAllChildren(){if(this._children===null||this._children.size===0)return!1;let n=!1;for(const r of this._children)r.tell(xp(ra(this.id()))),n=!0;return n}interruptAllChildren(){if(this.sendInterruptSignalToAllChildren()){const n=this._children.values();this._children=null;let r=!1;return Cg({while:()=>!r,body:()=>{const l=n.next();return l.done?ot(()=>{r=!0}):ja(l.value.await)},step:()=>{}})}return null}reportExitValue(n){if(XS(this.currentRuntimeFlags)){const r=this.getFiberRef(uy),i=this.id().startTimeMillis,l=Date.now();switch(lF.unsafeUpdate(l-i,r),z1.unsafeUpdate(-1,r),n._tag){case xe:{iF.unsafeUpdate(1,r);break}case Ce:{oF.unsafeUpdate(1,r);break}}}if(n._tag==="Failure"){const r=this.getFiberRef(Xz);!vg(n.cause)&&r._tag==="Some"&&this.log("Fiber terminated with an unhandled error",n.cause,r)}}setExitValue(n){this._exitValue=n,this.reportExitValue(n);for(let r=this._observers.length-1;r>=0;r--)this._observers[r](n);this._observers=[]}getLoggers(){return this.getFiberRef(vF)}log(n,r,i){const l=ca(i)?i.value:this.getFiberRef(Vz),u=this.getFiberRef(mF);if(K6(u,l))return;const f=this.getFiberRef(Gz),h=this.getFiberRef(Pz),m=this.getLoggers(),p=this.getFiberRefs();if(UT(m)>0){const g=ts(this.getFiberRef(Hi),oo),v=new Date(g.unsafeCurrentTimeMillis());L3(p,()=>{for(const b of m)b.log({fiberId:this.id(),logLevel:l,message:n,cause:r,context:p,spans:f,annotations:h,date:v})})}}evaluateMessageWhileSuspended(n){switch(n._tag){case e0:return L1;case Jg:return this.processNewInterruptSignal(n.cause),this._asyncInterruptor!==null&&(this._asyncInterruptor(Xt(n.cause)),this._asyncInterruptor=null),pl;case t0:return this._asyncInterruptor=null,this._asyncBlockingOn=null,this.evaluateEffect(n.effect),pl;case Wg:return n.onFiber(this,this._exitValue!==null?B5:q5(this.currentRuntimeFlags,this._asyncBlockingOn)),pl;default:return Hu(n)}}evaluateEffect(n){this.currentSupervisor.onResume(this);try{let r=hr(this.currentRuntimeFlags)&&this.isInterrupted()?Xt(this.getInterruptedCause()):n;for(;r!==null;){const i=r,l=this.runLoop(i);if(l===xa){const u=Da.currentOp;Da.currentOp=null,u._op===Ku?k4(this.currentRuntimeFlags)?(this.tell(_$()),this.tell(fi(vn)),r=null):r=vn:u._op===Ol&&(r=null)}else{this.currentRuntimeFlags=x(this.currentRuntimeFlags,N4(oy));const u=this.interruptAllChildren();u!==null?r=X(u,()=>l):(this._queue.length===0?this.setExitValue(l):this.tell(fi(l)),r=null)}}}finally{this.currentSupervisor.onSuspend(this)}}start(n){if(this._running)this.tell(fi(n));else{this._running=!0;const r=globalThis[Ir];globalThis[Ir]=this;try{this.evaluateEffect(n)}finally{this._running=!1,globalThis[Ir]=r,this._queue.length>0&&this.drainQueueLaterOnExecutor()}}}startFork(n){this.tell(fi(n))}patchRuntimeFlags(n,r){const i=Oi(n,r);return globalThis[Ir]=this,this.currentRuntimeFlags=i,i}initiateAsync(n,r){let i=!1;const l=u=>{i||(i=!0,this.tell(fi(u)))};hr(n)&&(this._asyncInterruptor=l);try{r(l)}catch(u){l(be(Qn(u)))}}pushStack(n){this._stack.push(n),n._op==="OnStep"&&this._steps.push({refs:this.getFiberRefs(),flags:this.currentRuntimeFlags})}popStack(){const n=this._stack.pop();if(n)return n._op==="OnStep"&&this._steps.pop(),n}getNextSuccessCont(){let n=this.popStack();for(;n;){if(n._op!==Gu)return n;n=this.popStack()}}getNextFailCont(){let n=this.popStack();for(;n;){if(n._op!==hf&&n._op!==pf&&n._op!==Rl)return n;n=this.popStack()}}[U3](n){return ot(()=>yT(this.currentContext,n))}Left(n){return le(n.left)}None(n){return le(new Lg)}Right(n){return ne(n.right)}Some(n){return ne(n.value)}Micro(n){return Hl(r=>{let i=r;const l=y$(c$(n,this.currentContext));return l.addObserver(u=>{if(u._tag==="Success")return i(ne(u.value));switch(u.cause._tag){case"Interrupt":return i(Xt(ra(ds)));case"Fail":return i(le(u.cause.error));case"Die":return i(Mf(u.cause.defect))}}),Hl(u=>{i=f=>{u(ae)},l.unsafeInterrupt()})})}[PE](n){const r=Be(()=>n.effect_instruction_i0()),i=this.getNextSuccessCont();return i!==void 0?(i._op in yl||Hu(i),yl[i._op](this,i,r)):(Da.currentOp=ne(r),xa)}[xe](n){const r=n,i=this.getNextSuccessCont();return i!==void 0?(i._op in yl||Hu(i),yl[i._op](this,i,r.effect_instruction_i0)):(Da.currentOp=r,xa)}[Ce](n){const r=n.effect_instruction_i0,i=this.getNextFailCont();if(i!==void 0)switch(i._op){case Gu:case mf:return hr(this.currentRuntimeFlags)&&this.isInterrupted()?Xt(WS(r)):Be(()=>i.effect_instruction_i1(r));case"OnStep":return hr(this.currentRuntimeFlags)&&this.isInterrupted()?Xt(WS(r)):ne(Xt(r));case Yy:return this.patchRuntimeFlags(this.currentRuntimeFlags,i.patch),hr(this.currentRuntimeFlags)&&this.isInterrupted()?Xt(Ke(r,this.getInterruptedCause())):Xt(r);default:Hu(i)}else return Da.currentOp=Xt(r),xa}[VE](n){return Be(()=>n.effect_instruction_i0(this,OO(this.currentRuntimeFlags)))}Blocked(n){const r=this.getFiberRefs(),i=this.currentRuntimeFlags;if(this._steps.length>0){const l=[],u=this._steps[this._steps.length-1];let f=this.popStack();for(;f&&f._op!=="OnStep";)l.push(f),f=this.popStack();this.setFiberRefs(u.refs),this.currentRuntimeFlags=u.flags;const h=Vl(u.refs,r),m=pr(u.flags,i);return ne(v2(n.effect_instruction_i0,Gt(p=>{for(;l.length>0;)p.pushStack(l.pop());return p.setFiberRefs(Gl(p.id(),p.getFiberRefs())(h)),p.currentRuntimeFlags=Oi(m)(p.currentRuntimeFlags),n.effect_instruction_i1})))}return Xn(l=>X(s0(vz(n.effect_instruction_i0)),()=>l(n.effect_instruction_i1)))}RunBlocked(n){return dF(n.effect_instruction_i0)}[Ki](n){const r=n.effect_instruction_i0,i=this.currentRuntimeFlags,l=Oi(i,r);if(hr(l)&&this.isInterrupted())return Xt(this.getInterruptedCause());if(this.patchRuntimeFlags(this.currentRuntimeFlags,r),n.effect_instruction_i1){const u=pr(l,i);return this.pushStack(new _z(u,n)),Be(()=>n.effect_instruction_i1(i))}else return vn}[hf](n){return this.pushStack(n),n.effect_instruction_i0}OnStep(n){return this.pushStack(n),n.effect_instruction_i0}[Gu](n){return this.pushStack(n),n.effect_instruction_i0}[mf](n){return this.pushStack(n),n.effect_instruction_i0}[Ol](n){return this._asyncBlockingOn=n.effect_instruction_i1,this.initiateAsync(this.currentRuntimeFlags,n.effect_instruction_i0),Da.currentOp=n,xa}[Ku](n){return this._isYielding=!1,Da.currentOp=n,xa}[pf](n){const r=n.effect_instruction_i0,i=n.effect_instruction_i1;return r()?(this.pushStack(n),i()):vn}[Rl](n){return yl[Rl](this,n,void 0)}[ed](n){return Be(()=>n.commit())}runLoop(n){let r=n;for(this.currentOpCount=0;;){if((this.currentRuntimeFlags&D4)!==0&&this.currentSupervisor.onEffect(this,r),this._queue.length>0&&(r=this.drainQueueWhileRunning(this.currentRuntimeFlags,r)),!this._isYielding){this.currentOpCount+=1;const i=this.currentScheduler.shouldYield(this);if(i!==!1){this._isYielding=!0,this.currentOpCount=0;const l=r;r=X(xg({priority:i}),()=>l)}}try{if(r=this.currentTracer.context(()=>hF!==r[Bi]._V?cy(`Cannot execute an Effect versioned ${r[Bi]._V} with a Runtime of version ${Xf()}`):this[r._op](r),this),r===xa){const i=Da.currentOp;return i._op===Ku||i._op===Ol?xa:(Da.currentOp=null,i._op===xe||i._op===Ce?i:Xt(Qn(i)))}}catch(i){r!==xa&&!Rt(r,"_op")||!(r._op in this)?r=cy(`Not a valid effect: ${Ai(r)}`):aL(i)?r=Xt(Ke(Qn(i),ra(ds))):r=Mf(i)}}}run=()=>{this.drainQueueOnCurrentThread()}}const mF=$t("effect/FiberRef/currentMinimumLogLevel",()=>qe(Q6("Info"))),pF=t=>n0(n=>{const r=L6(n.context,Hi);ts(r,Z2).unsafe.log(t.log(n))}),yF=$t(Symbol.for("effect/Logger/defaultLogger"),()=>pF(U$)),gF=$t(Symbol.for("effect/Logger/tracerLogger"),()=>n0(({annotations:t,cause:n,context:r,fiberId:i,logLevel:l,message:u})=>{const f=Xi(Pl(r,Ba),Dd);if(f._tag==="None"||f.value._tag==="ExternalSpan")return;const h=yT(Pl(r,Hi),oo),m={};for(const[p,g]of t)m[p]=g;m["effect.fiberId"]=gN(i),m["effect.logLevel"]=l.label,n!==null&&n._tag!=="Empty"&&(m["effect.cause"]=ic(n,{renderErrorCause:!0})),f.value.event(Ai(Array.isArray(u)?u[0]:u),h.unsafeCurrentTimeNanos(),m)})),vF=$t(Symbol.for("effect/FiberRef/currentLoggers"),()=>qz(hg(yF,gF))),_F=E(t=>ha(t[0]),(t,n)=>Ed(Ag(t,r=>bF(i=>n(r,i))))),bF=t=>Gt(n=>{const r=n.getFiberRefs(),i=n.currentRuntimeFlags;return X(oR,l=>wf(l,u=>Gt(f=>{const h=f.getFiberRefs(),m=f.currentRuntimeFlags,p=Vl(h,r),g=pr(m,i),v=Vl(r,h);return f.setFiberRefs(Gl(p,f.id(),r)),Ql(Cz(t(u),g),ot(()=>{f.setFiberRefs(Gl(v,f.id(),f.getFiberRefs()))}))})))}),SF=t=>{if(Array.isArray(t)||$E(t))return[t,yt()];const n=Object.keys(t),r=n.length;return[n.map(i=>t[i]),Tt(i=>{const l={};for(let u=0;u<r;u++)l[n[u]]=i[u];return l})]},EF=(t,n,r)=>{const i=[];for(const l of t)i.push(qi(l));return X(Pi(i,Vt,{concurrency:r?.concurrency,batching:r?.batching,concurrentFinalizers:r?.concurrentFinalizers}),l=>{const u=yt(),f=l.length,h=new Array(f),m=new Array(f);let p=!1;for(let g=0;g<f;g++){const v=l[g];v._tag==="Left"?(h[g]=Tt(v.left),p=!0):(m[g]=v.right,h[g]=u)}return p?n._tag==="Some"?le(n.value(h)):le(h):r?.discard?ae:n._tag==="Some"?pt(n.value(m)):pt(m)})},TF=(t,n,r)=>{const i=[];for(const l of t)i.push(qi(l));return r?.discard?Pi(i,Vt,{concurrency:r?.concurrency,batching:r?.batching,discard:!0,concurrentFinalizers:r?.concurrentFinalizers}):Ft(Pi(i,Vt,{concurrency:r?.concurrency,batching:r?.batching,concurrentFinalizers:r?.concurrentFinalizers}),l=>n._tag==="Some"?n.value(l):l)},nR=(t,n)=>{const[r,i]=SF(t);return n?.mode==="validate"?EF(r,i,n):n?.mode==="either"?TF(r,i,n):n?.discard!==!0&&i._tag==="Some"?Ft(Pi(r,Vt,n),i.value):Pi(r,Vt,n)},Pi=E(t=>$E(t[0]),(t,n,r)=>Gt(i=>{const l=r?.batching===!0||r?.batching==="inherit"&&i.getFiberRef(Yz);return r?.discard?C1(r.concurrency,()=>di(Sy,r?.concurrentFinalizers)(u=>l?as(t,(f,h)=>u(n(f,h)),!0,!1,1):bd(t,(f,h)=>u(n(f,h)))),()=>di(Ey,r?.concurrentFinalizers)(u=>as(t,(f,h)=>u(n(f,h)),l,!1)),u=>di(Ty(u),r?.concurrentFinalizers)(f=>as(t,(h,m)=>f(n(h,m)),l,!1,u))):C1(r?.concurrency,()=>di(Sy,r?.concurrentFinalizers)(u=>l?Ly(t,1,(f,h)=>u(n(f,h)),!0):Na(t,(f,h)=>u(n(f,h)))),()=>di(Ey,r?.concurrentFinalizers)(u=>aR(t,(f,h)=>u(n(f,h)),l)),u=>di(Ty(u),r?.concurrentFinalizers)(f=>Ly(t,u,(h,m)=>f(n(h,m)),l)))})),aR=(t,n,r)=>Wt(()=>{const i=ce(t),l=new Array(i.length);return Ve(as(i,(f,h)=>X(n(f,h),m=>ot(()=>l[h]=m)),r,!1),pt(l))}),as=(t,n,r,i,l)=>Xn(u=>Mz(f=>Gt(h=>{let m=Array.from(t).reverse(),p=m.length;if(p===0)return ae;let g=0,v=!1;const b=l?Math.min(m.length,l):m.length,O=new Set,R=new Array,A=()=>O.forEach(N=>{N.currentScheduler.scheduleTask(()=>{N.unsafeInterruptAsFork(h.id())},0)}),j=new Array,U=new Array,B=new Array,q=()=>{const N=R.filter(({exit:L})=>L._tag==="Failure").sort((L,rt)=>L.index<rt.index?-1:L.index===rt.index?0:1).map(({exit:L})=>L);return N.length===0&&N.push(vn),N},G=(N,L=!1)=>{const rt=Ed(f(N)),tt=OF(rt,h,h.currentRuntimeFlags,$d);return h.currentScheduler.scheduleTask(()=>{L&&tt.unsafeInterruptAsFork(h.id()),tt.resume(rt)},0),tt},Z=()=>{i||(p-=m.length,m=[]),v=!0,A()},K=r?Sz:yr,Q=G(fa(N=>{const L=(tt,Y)=>{tt._op==="Blocked"?B.push(tt):(R.push({index:Y,exit:tt}),tt._op==="Failure"&&!v&&Z())},rt=()=>{if(m.length>0){const tt=m.pop();let Y=g++;const it=()=>{const I=m.pop();return Y=g++,X(xg(),()=>X(K(u(n(I,Y))),St))},St=I=>m.length>0&&(L(I,Y),m.length>0)?it():pt(I),ut=X(K(u(n(tt,Y))),St),C=G(ut);j.push(C),O.add(C),v&&C.currentScheduler.scheduleTask(()=>{C.unsafeInterruptAsFork(h.id())},0),C.addObserver(I=>{let st;if(I._op==="Failure"?st=I:st=I.effect_instruction_i0,U.push(C),O.delete(C),L(st,Y),R.length===p)N(pt(zn(Ri(q(),{parallel:!0}),()=>vn)));else if(B.length+R.length===p){const bt=q(),T=B.map(F=>F.effect_instruction_i0).reduce(s2);N(pt(v2(T,as([zn(Ri(bt,{parallel:!0}),()=>vn),...B.map(F=>F.effect_instruction_i1)],F=>F,r,!0,l))))}else rt()})}};for(let tt=0;tt<b;tt++)rt()}));return ja(ys(_d(u(Kl(Q))),wd({onFailure:N=>{Z();const L=B.length+1,rt=Math.min(typeof l=="number"?l:B.length,B.length),tt=Array.from(B);return fa(Y=>{let it=0,St=0;const ut=(I,st)=>bt=>{it++,it===L&&Y(ne(Xt(N))),tt.length>0&&st&&C()},C=()=>{G(tt.pop(),!0).addObserver(ut(St,!0)),St++};Q.addObserver(ut(St,!1)),St++;for(let I=0;I<rt;I++)C()})},onSuccess:()=>Na(U,N=>N.inheritAll)})))}))),Ly=(t,n,r,i)=>Wt(()=>{const l=ce(t),u=new Array(l.length);return Ve(as(l,(h,m)=>Ft(r(h,m),p=>u[m]=p),i,!1,n),pt(u))}),s0=t=>RF(t,$d),rR=(t,n,r,i=null)=>{const l=Ff(t,n,r,i);return l.resume(t),l},OF=(t,n,r,i=null)=>Ff(t,n,r,i),Ff=(t,n,r,i=null)=>{const l=HT(),u=n.getFiberRefs(),f=N6(u,l),h=new eR(l,f,r),m=Pl(f,Ba),p=h.currentSupervisor;return p.onStart(m,t,Tt(n),h),h.addObserver(v=>p.onEnd(v,h)),(i!==null?i:x(n.getFiberRef(fy),zn(()=>n.scope()))).add(r,h),h},RF=(t,n)=>Gt((r,i)=>pt(rR(t,r,i.runtimeFlags,n))),$1=t=>io(n=>Fa(Xi(n,Es),{onNone:()=>t,onSome:r=>{switch(r.strategy._tag){case"Parallel":return t;case"Sequential":case"ParallelN":return X(Rd(r,Ey),i=>pc(t,i))}}})),U1=t=>n=>io(r=>Fa(Xi(r,Es),{onNone:()=>n,onSome:i=>i.strategy._tag==="ParallelN"&&i.strategy.parallelism===t?n:X(Rd(i,Ty(t)),l=>pc(n,l))})),di=(t,n)=>r=>io(i=>Fa(Xi(i,Es),{onNone:()=>r(Vt),onSome:l=>{if(n===!0){const u=t._tag==="Parallel"?$1:t._tag==="Sequential"?F1:U1(t.parallelism);switch(l.strategy._tag){case"Parallel":return u(r($1));case"Sequential":return u(r(F1));case"ParallelN":return u(r(U1(l.strategy.parallelism)))}}else return r(Vt)}})),sR=t=>X(Es,t),iR=t=>X(i0(),n=>ys(t(n),r=>n.close(r))),F1=t=>io(n=>Fa(Xi(n,Es),{onNone:()=>t,onSome:r=>{switch(r.strategy._tag){case"Sequential":return t;case"Parallel":case"ParallelN":return X(Rd(r,Sy),i=>pc(t,i))}}})),MF=E(t=>ha(t[1]),(t,n,r)=>mc(t,n,(i,l)=>[i,l],r)),wF=E(t=>ha(t[1]),(t,n,r)=>r?.concurrent!==!0&&(r?.batching===void 0||r.batching===!1)?Dg(t,n):mc(t,n,(i,l)=>i,r)),AF=E(t=>ha(t[1]),(t,n,r)=>r?.concurrent!==!0&&(r?.batching===void 0||r.batching===!1)?Ve(t,n):mc(t,n,(i,l)=>l,r)),mc=E(t=>ha(t[1]),(t,n,r,i)=>Ft(nR([t,n],{concurrency:i?.concurrent?2:1,batching:i?.batching,concurrentFinalizers:i?.concurrentFinalizers}),([l,u])=>r(l,u))),Es=bs("effect/Scope"),oR=Es,CF=(t,n)=>{t.state._tag==="Open"&&t.state.finalizers.set({},n)},xF={[r1]:r1,[s1]:s1,pipe(){return Et(this,arguments)},fork(t){return ot(()=>{const n=lR(t);if(this.state._tag==="Closed")return n.state=this.state,n;const r={},i=l=>n.close(l);return this.state.finalizers.set(r,i),CF(n,l=>ot(()=>{this.state._tag==="Open"&&this.state.finalizers.delete(r)})),n})},close(t){return Wt(()=>{if(this.state._tag==="Closed")return ae;const n=Array.from(this.state.finalizers.values()).reverse();return this.state={_tag:"Closed",exit:t},n.length===0?ae:x5(this.strategy)?x(Na(n,r=>yr(r(t))),X(r=>x(Ri(r),Ml(wp),zn(()=>vn)))):D5(this.strategy)?x(aR(n,r=>yr(r(t)),!1),X(r=>x(Ri(r,{parallel:!0}),Ml(wp),zn(()=>vn)))):x(Ly(n,this.strategy.parallelism,r=>yr(r(t)),!1),X(r=>x(Ri(r,{parallel:!0}),Ml(wp),zn(()=>vn))))})},addFinalizer(t){return Wt(()=>this.state._tag==="Closed"?t(this.state.exit):(this.state.finalizers.set({},t),ae))}},lR=(t=Nd)=>{const n=Object.create(xF);return n.strategy=t,n.state={_tag:"Open",finalizers:new Map},n},i0=(t=Nd)=>ot(()=>lR(t)),pc=E(2,(t,n)=>H2(t,Zi(ng(Es,n)))),DF=t=>ro(t,{differ:rF,fork:Cl}),j1=Iz(z4),kF=DF(qd),cR=E(3,(t,n,r)=>zF(t,n,{onSelfWin:(i,l)=>X(i.await,u=>{switch(u._tag){case xe:return X(i.inheritAll,()=>r.onSelfDone(u,l));case Ce:return r.onSelfDone(u,l)}}),onOtherWin:(i,l)=>X(i.await,u=>{switch(u._tag){case xe:return X(i.inheritAll,()=>r.onOtherDone(u,l));case Ce:return r.onOtherDone(u,l)}})})),NF=E(2,(t,n)=>cc(r=>cR(t,n,{onSelfDone:(i,l)=>py(i,{onFailure:u=>x(Kl(l),T1(f=>ps(u,f))),onSuccess:u=>x(l,Il(r),We(u))}),onOtherDone:(i,l)=>py(i,{onFailure:u=>x(Kl(l),T1(f=>ps(f,u))),onSuccess:u=>x(l,Il(r),We(u))})}))),zF=E(3,(t,n,r)=>Gt((i,l)=>{const u=l.runtimeFlags,f=fd(!0),h=Ff(t,i,u,r.selfScope),m=Ff(n,i,u,r.otherScope);return fa(p=>{h.addObserver(()=>B1(h,m,r.onSelfWin,f,p)),m.addObserver(()=>B1(m,h,r.onOtherWin,f,p)),h.startFork(t),m.startFork(n)},pN(h.id(),m.id()))})),B1=(t,n,r,i,l)=>{lN(!0,!1)(i)&&l(r(t,n))},Ql=E(2,(t,n)=>Xn(r=>$n(r(t),{onFailure:i=>$n(n,{onFailure:l=>be(Ke(i,l)),onSuccess:()=>be(i)}),onSuccess:i=>We(n,i)}))),LF=(t,n,r)=>cc(i=>X(X(s0(Mg(t)),l=>fa(u=>{const f=n.map(p=>p.listeners.count),h=()=>{f.every(p=>p===0)&&n.every(p=>p.result.state.current._tag==="Pending"?!0:!!(p.result.state.current._tag==="Done"&&$g(p.result.state.current.effect)&&p.result.state.current.effect._tag==="Failure"&&u2(p.result.state.current.effect.cause)))&&(m.forEach(p=>p()),r?.(),u(kg(l)))};l.addObserver(p=>{m.forEach(g=>g()),u(p)});const m=n.map((p,g)=>{const v=b=>{f[g]=b,h()};return p.listeners.addObserver(v),()=>p.listeners.removeObserver(v)});return h(),ot(()=>{m.forEach(p=>p())})})),()=>Wt(()=>{const l=n.flatMap(u=>u.state.completed?[]:[u]);return bd(l,u=>XU(u.request,cL(i)))}))),$F="effect/ScheduleInterval",jf=Symbol.for($F),uR={[jf]:jf,startMillis:0,endMillis:0},fR=(t,n)=>t>n?uR:{[jf]:jf,startMillis:t,endMillis:n},UF=E(2,(t,n)=>FF(t,n)===t),FF=E(2,(t,n)=>t.endMillis<=n.startMillis?t:n.endMillis<=t.startMillis?n:t.startMillis<n.startMillis?t:n.startMillis<t.startMillis?n:t.endMillis<=n.endMillis?t:n),jF=t=>t.startMillis>=t.endMillis,BF=E(2,(t,n)=>{const r=Math.max(t.startMillis,n.startMillis),i=Math.min(t.endMillis,n.endMillis);return fR(r,i)}),qF=t=>fR(t,Number.POSITIVE_INFINITY),dR=uR,HF=UF,IF=jF,PF=BF,VF=qF,GF="effect/ScheduleIntervals",q1=Symbol.for(GF),hR=t=>({[q1]:q1,intervals:t}),KF=E(2,(t,n)=>QF(t.intervals,n.intervals,Yn())),QF=(t,n,r)=>{let i=t,l=n,u=r;for(;Ua(i)&&Ua(l);){const f=x(oa(i),PF(oa(l))),h=IF(f)?u:x(u,Ln(f));x(oa(i),HF(oa(l)))?i=ka(i):l=ka(l),u=h}return hR(ls(u))},$y=t=>x(t.intervals,ag,zn(()=>dR)).startMillis,YF=t=>x(t.intervals,ag,zn(()=>dR)).endMillis,XF=E(2,(t,n)=>$y(t)<$y(n)),ZF=t=>Ua(t.intervals),JF=hR,WF=KF,tj=$y,H1=YF,ej=XF,nj=ZF,o0="Continue",mR="Done",aj=t=>({_tag:o0,intervals:t}),rj=t=>({_tag:o0,intervals:JF(Je(t))}),sj={_tag:mR},ij=t=>t._tag===o0,oj=t=>t._tag===mR,lj=aj,cj=rj,Yl=sj,I1=ij,Bf=oj,W7=$2,tB=wf,uj=dy,eB=pc,fj=Rd,nB=i0,dj="effect/Schedule",pR=Symbol.for(dj),hj=t=>Rt(t,pR),mj="effect/ScheduleDriver",pj=Symbol.for(mj),yj={_Out:t=>t,_In:t=>t,_R:t=>t},gj={_Out:t=>t,_In:t=>t,_R:t=>t};class vj{initial;step;[pR]=yj;constructor(n,r){this.initial=n,this.step=r}pipe(){return Et(this,arguments)}}class _j{schedule;ref;[pj]=gj;constructor(n,r){this.schedule=n,this.ref=r}get state(){return Ft(gr(this.ref),n=>n[1])}get last(){return X(gr(this.ref),([n,r])=>{switch(n._tag){case"None":return vd(()=>new Lg);case"Some":return pt(n.value)}})}get reset(){return Df(this.ref,[yt(),this.schedule.initial])}next(n){return x(Ft(gr(this.ref),r=>r[1]),X(r=>x(w6,X(i=>x(Wt(()=>this.schedule.step(i,n,r)),X(([l,u,f])=>{const h=Df(this.ref,[Tt(u),l]);if(Bf(f))return Ve(h,le(yt()));const m=tj(f.intervals)-i;return m<=0?We(h,u):x(h,Ve(p5(_f(m))),We(u))}))))))}}const yc=(t,n)=>new vj(t,n),bj=E(2,(t,n)=>l0(t,(r,i)=>ot(()=>n(r,i)))),l0=E(2,(t,n)=>yc(t.initial,(r,i,l)=>X(t.step(r,i,l),([u,f,h])=>Bf(h)?pt([u,f,Yl]):Ft(n(i,f),m=>m?[u,f,h]:[u,f,Yl])))),Sj=t=>x(xf([yt(),t.initial]),Ft(n=>new _j(t,n))),Ej=E(2,(t,n)=>Tj(t,n,WF)),Tj=E(3,(t,n,r)=>yc([t.initial,n.initial],(i,l,u)=>x(x2(t.step(i,l,u[0]),n.step(i,l,u[1]),(f,h)=>[f,h]),X(([[f,h,m],[p,g,v]])=>I1(m)&&I1(v)?Uy(t,n,l,f,h,m.intervals,p,g,v.intervals,r):pt([[f,p],[h,g],Yl]))))),Uy=(t,n,r,i,l,u,f,h,m,p)=>{const g=p(u,m);return nj(g)?pt([[i,f],[l,h],lj(g)]):x(u,ej(m))?X(t.step(H1(u),r,i),([v,b,O])=>Bf(O)?pt([[v,f],[b,h],Yl]):Uy(t,n,r,v,b,O.intervals,f,h,m,p)):X(n.step(H1(m),r,f),([v,b,O])=>Bf(O)?pt([[i,v],[l,b],Yl]):Uy(t,n,r,i,l,u,v,b,O.intervals,p))},Oj=E(2,(t,n)=>Rj(t,r=>ot(()=>n(r)))),Rj=E(2,(t,n)=>yc(t.initial,(r,i,l)=>X(t.step(r,i,l),([u,f,h])=>Ft(n(f),m=>[u,m,h])))),Mj=t=>yc(t.initial,(n,r,i)=>x(t.step(n,r,i),Ft(([l,u,f])=>[l,r,f]))),wj=t=>Dj(gR,n=>n<t),Aj=(t,n)=>yc(t,(r,i,l)=>ot(()=>[n(l),l,cj(VF(r))])),Cj=E(2,(t,n)=>l0(t,(r,i)=>d5(n(r)))),xj=E(2,(t,n)=>l0(t,(r,i)=>n(r))),Dj=E(2,(t,n)=>bj(t,(r,i)=>n(i))),tf=Symbol.for("effect/Schedule/ScheduleDefect");class kj{error;[tf];constructor(n){this.error=n,this[tf]=tf}}const Nj=t=>Rt(t,tf),P1=t=>Rf(t,n=>Mf(new kj(n))),zj=t=>Tg(t,n=>Fa(_g(n,r=>ez(r)&&Nj(r.defect)?Tt(r.defect):yt()),{onNone:()=>be(n),onSome:r=>le(r.error)})),V1=E(2,(t,n)=>$j(t,n,(r,i)=>le(r))),Lj=E(2,(t,n)=>{if(hj(n))return V1(t,n);const r=n.schedule??Mj(gR),i=n.while?xj(r,f=>{const h=n.while(f);return typeof h=="boolean"?pt(h):P1(h)}):r,l=n.until?Cj(i,f=>{const h=n.until(f);return typeof h=="boolean"?pt(h):P1(h)}):i,u=n.times?Ej(l,wj(n.times)).pipe(Oj(f=>f[0])):l;return zj(V1(t,u))}),$j=E(3,(t,n,r)=>X(Sj(n),i=>Er(t,{onFailure:l=>r(l,yt()),onSuccess:l=>yR(t,i,r,l)}))),yR=(t,n,r,i)=>Er(n.next(i),{onFailure:()=>Tz(n.last),onSuccess:l=>Er(t,{onFailure:u=>r(u,Tt(l)),onSuccess:u=>yR(t,n,r,u)})}),gR=Aj(0,t=>t+1);class Uj{permits;waiters=new Set;taken=0;constructor(n){this.permits=n}get free(){return this.permits-this.taken}take=n=>Eg(r=>{if(this.free<n){const i=()=>{this.free<n||(this.waiters.delete(i),this.taken+=n,r(pt(n)))};return this.waiters.add(i),ot(()=>{this.waiters.delete(i)})}return this.taken+=n,r(pt(n))});updateTaken=n=>Gt(r=>(this.taken=n(this.taken),this.waiters.size>0&&r.getFiberRef(Zg).scheduleTask(()=>{const i=this.waiters.values();let l=i.next();for(;l.done===!1&&this.free>0;)l.value(),l=i.next()},r.getFiberRef(so)),pt(this.free)));release=n=>this.updateTaken(r=>r-n);releaseAll=this.updateTaken(n=>0);withPermits=n=>r=>Xn(i=>X(i(this.take(n)),l=>Ql(i(r),this.release(l))));withPermitsIfAvailable=n=>r=>Xn(i=>Wt(()=>this.free<n?y5:(this.taken+=n,Ql(i(i5(r)),this.release(n)))))}const vR=t=>new Uj(t),Fj=t=>ot(()=>vR(t));class jj extends kd{isOpen;waiters=[];scheduled=!1;constructor(n){super(),this.isOpen=n}commit(){return this.await}unsafeSchedule(n){return this.scheduled||this.waiters.length===0||(this.scheduled=!0,n.currentScheduler.scheduleTask(this.flushWaiters,n.getFiberRef(so))),ae}flushWaiters=()=>{this.scheduled=!1;const n=this.waiters;this.waiters=[];for(let r=0;r<n.length;r++)n[r](vn)};open=Gt(n=>this.isOpen?ae:(this.isOpen=!0,this.unsafeSchedule(n)));unsafeOpen(){this.isOpen||(this.isOpen=!0,this.flushWaiters())}release=Gt(n=>this.isOpen?ae:this.unsafeSchedule(n));await=Eg(n=>this.isOpen?n(ae):(this.waiters.push(n),ot(()=>{const r=this.waiters.indexOf(n);r!==-1&&this.waiters.splice(r,1)})));unsafeClose(){this.isOpen=!1}close=ot(()=>{this.isOpen=!1});whenOpen=n=>Ve(this.await,n)}const Bj=t=>new jj(t??!1),qj=E(2,(t,n)=>Gt((r,i)=>{const l=n,u=rR(t,r,i.runtimeFlags,$d);if(l.state._tag==="Open"){const f=()=>cc(m=>Ot(m,u.id())?ae:ja(kg(u))),h={};l.state.finalizers.set(h,f),u.addObserver(()=>{l.state._tag!=="Closed"&&l.state.finalizers.delete(h)})}else u.unsafeInterruptAsFork(r.id());return pt(u)})),Hj="effect/Ref/SynchronizedRef",Ij=Symbol.for(Hj),Pj={_A:t=>t};class Vj extends kd{ref;withLock;[Ij]=Pj;[aO]=rO;[Cf]=Cf;constructor(n,r){super(),this.ref=n,this.withLock=r,this.get=gr(this.ref)}get;commit(){return this.get}modify(n){return this.modifyEffect(r=>pt(n(r)))}modifyEffect(n){return this.withLock(x(X(gr(this.ref),n),X(([r,i])=>We(Df(this.ref,i),r))))}}const Gj=t=>ot(()=>_R(t)),_R=t=>{const n=sO(t),r=vR(1);return new Vj(n,r.withPermits(1))},Kj=Symbol.for("effect/ManagedRuntime"),Qj="Fresh",Yj="FromEffect",Xj="Suspend",Zj="Provide",Jj="ProvideMerge",Wj="ZipWith",aB=C$,rB=x$,sB=kg,t8=Il,iB=Kl,co=t=>function(){if(arguments.length===1){const n=arguments[0];return(r,...i)=>t(n,r,...i)}return t.apply(this,arguments)},Hd=co((t,n,r)=>{const i=HT(),l=[[Ba,[[i,t.context]]]];r?.scheduler&&l.push([Zg,[[i,r.scheduler]]]);let u=$6(t.fiberRefs,{entries:l,forkAs:i});r?.updateRefs&&(u=r.updateRefs(u,i));const f=new eR(i,u,t.runtimeFlags);let h=n;r?.scope&&(h=X(fj(r.scope,Nd),p=>Ve($2(p,cc(g=>Ot(g,f.id())?ae:Il(f,g))),ys(n,g=>uj(p,g)))));const m=f.currentSupervisor;return m!==qd&&(m.onStart(t.context,h,yt(),f),f.addObserver(p=>m.onEnd(p,f))),$d.add(t.runtimeFlags,f),r?.immediate===!1?f.resume(h):f.start(h),f}),e8=co((t,n,r={})=>{const i=Hd(t,n,r);return r.onExit&&i.addObserver(l=>{r.onExit(l)}),(l,u)=>e8(t)(x(i,t8(l??ds)),{...u,onExit:u?.onExit?f=>u.onExit(SL(f)):void 0})}),n8=co((t,n)=>{const r=ER(t)(n);if(r._tag==="Failure")throw bR(r.effect_instruction_i0);return r.effect_instruction_i0});class a8 extends Error{fiber;_tag="AsyncFiberException";constructor(n){super(`Fiber #${n.id().id} cannot be resolved synchronously. This is caused by using runSync on an effect that performs async work`),this.fiber=n,this.name=this._tag,this.stack=this.message}}const r8=t=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=0;const r=new a8(t);return Error.stackTraceLimit=n,r},ef=Symbol.for("effect/Runtime/FiberFailure"),Iu=Symbol.for("effect/Runtime/FiberFailure/Cause");class s8 extends Error{[ef];[Iu];constructor(n){const r=y2(n)[0];super(r?.message||"An error has occurred"),this[ef]=ef,this[Iu]=n,this.name=r?`(FiberFailure) ${r.name}`:"FiberFailure",r?.stack&&(this.stack=r.stack)}toJSON(){return{_id:"FiberFailure",cause:this[Iu].toJSON()}}toString(){return"(FiberFailure) "+ic(this[Iu],{renderErrorCause:!0})}[re](){return this.toString()}}const bR=t=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=0;const r=new s8(t);return Error.stackTraceLimit=n,r},oB=t=>Rt(t,ef),SR=t=>{const n=t;switch(n._op){case"Failure":case"Success":return n;case"Left":return my(n.left);case"Right":return ne(n.right);case"Some":return ne(n.value);case"None":return my(Lg())}},ER=co((t,n)=>{const r=SR(n);if(r)return r;const i=new v$,l=Hd(t)(n,{scheduler:i});i.flush();const u=l.unsafePoll();return u||_i(Og(r8(l),Bg(l)))}),i8=co((t,n,r)=>TR(t,n,r).then(i=>{switch(i._tag){case xe:return i.effect_instruction_i0;case Ce:throw bR(i.effect_instruction_i0)}})),TR=co((t,n,r)=>new Promise(i=>{const l=SR(n);l&&i(l);const u=Hd(t)(n);u.addObserver(f=>{i(f)}),r?.signal!==void 0&&(r.signal.aborted?u.unsafeInterruptAsFork(u.id()):r.signal.addEventListener("abort",()=>{u.unsafeInterruptAsFork(u.id())},{once:!0}))}));class OR{context;runtimeFlags;fiberRefs;constructor(n,r,i){this.context=n,this.runtimeFlags=r,this.fiberRefs=i}pipe(){return Et(this,arguments)}}const o8=t=>new OR(t.context,t.runtimeFlags,t.fiberRefs),RR=()=>Gt((t,n)=>pt(new OR(t.getFiberRef(Ba),n.runtimeFlags,t.getFiberRefs()))),l8=a2(to,e2,t2),_s=o8({context:zl(),runtimeFlags:l8,fiberRefs:U6()}),c8=Hd(_s),u8=i8(_s),lB=TR(_s),f8=n8(_s),cB=ER(_s),d8=E(2,(t,n)=>t.modifyEffect(n)),h8="effect/Layer",MR=Symbol.for(h8),m8={_RIn:t=>t,_E:t=>t,_ROut:t=>t},Xl={[MR]:m8,pipe(){return Et(this,arguments)}},p8="effect/Layer/MemoMap",Dp=Symbol.for(p8),y8=rd()("effect/Layer/CurrentMemoMap",{defaultValue:()=>b8()}),g8=t=>Rt(t,MR),v8=t=>t._op_layer===Qj;class wR{ref;[Dp];constructor(n){this.ref=n,this[Dp]=Dp}getOrElseMemoize(n,r){return x(d8(this.ref,i=>{const l=i.get(n);if(l!==void 0){const[u,f]=l,h=x(u,X(([m,p])=>x(gO(m),We(p))),ys(wd({onFailure:()=>ae,onSuccess:()=>wf(r,f)})));return pt([h,i])}return x(xf(0),X(u=>x(B2(),X(f=>x(xf(()=>ae),Ft(h=>{const m=Xn(g=>x(i0(),X(v=>x(g(X(xR(n,v,!0),b=>l5(b(this)))),yr,X(b=>{switch(b._tag){case Ce:return x(pL(f,b.effect_instruction_i0),Ve(dy(v,b)),Ve(be(b.effect_instruction_i0)));case xe:return x(Df(h,O=>x(dy(v,O),C2(iO(u,R=>[R===1,R-1])),ja)),Ve(_y(u,O=>O+1)),Ve(wf(r,O=>x(ot(()=>i.delete(n)),Ve(gr(h)),X(R=>R(O))))),Ve(yL(f,b.effect_instruction_i0)),We(b.effect_instruction_i0[1]))}}))))),p=[x(Ad(f),ys(py({onFailure:()=>ae,onSuccess:()=>_y(u,g=>g+1)}))),g=>x(gr(h),X(v=>v(g)))];return[m,v8(n)?i:i.set(n,p)]}))))))}),_d)}}const _8=Wt(()=>Ft(Gj(new Map),t=>new wR(t))),b8=()=>new wR(_R(new Map)),AR=E(2,(t,n)=>X(_8,r=>CR(t,r,n))),CR=E(3,(t,n,r)=>X(xR(t,r),i=>vO(i(n),y8,n))),xR=(t,n,r=!1)=>{const i=t;switch(i._op_layer){case"Locally":return ot(()=>l=>i.f(l.getOrElseMemoize(i.self,n)));case"ExtendScope":return ot(()=>l=>sR(u=>l.getOrElseMemoize(i.layer,u)));case"Fold":return ot(()=>l=>x(l.getOrElseMemoize(i.layer,n),$n({onFailure:u=>l.getOrElseMemoize(i.failureK(u),n),onSuccess:u=>l.getOrElseMemoize(i.successK(u),n)})));case"Fresh":return ot(()=>l=>x(i.layer,AR(n)));case"FromEffect":return ot(r?()=>l=>i.effect:()=>l=>l.getOrElseMemoize(t,n));case"Provide":return ot(()=>l=>x(l.getOrElseMemoize(i.first,n),X(u=>x(l.getOrElseMemoize(i.second,n),xd(u)))));case"Scoped":return ot(r?()=>l=>pc(i.effect,n):()=>l=>l.getOrElseMemoize(t,n));case"Suspend":return ot(()=>l=>l.getOrElseMemoize(i.evaluate(),n));case"ProvideMerge":return ot(()=>l=>x(l.getOrElseMemoize(i.first,n),x2(l.getOrElseMemoize(i.second,n),i.zipK)));case"ZipWith":return ot(()=>l=>x(l.getOrElseMemoize(i.first,n),mc(l.getOrElseMemoize(i.second,n),i.zipK,{concurrent:!0})))}},S8=()=>c0(Fg()),uB=E(2,(t,n)=>{const r=pT(t),i=r?t:n;return c0(Ft(r?n:t,u=>ng(i,u)))});function c0(t){const n=Object.create(Xl);return n._op_layer=Yj,n.effect=t,n}const E8=E(2,(t,n)=>T8(t,n,(r,i)=>Zi(r,i))),DR=(...t)=>{let n=t[0];for(let r=1;r<t.length;r++)n=E8(n,t[r]);return n},fB=E(2,(t,n)=>{const r=pT(t);return c0(pt(ng(r?t:n,r?n:t)))}),kR=t=>{const n=Object.create(Xl);return n._op_layer=Xj,n.evaluate=t,n},dB=E(2,(t,n)=>X(sR(r=>CR(t,n,r)),r=>x(RR(),xd(r)))),hB=E(2,(t,n)=>kR(()=>{const r=Object.create(Xl);return r._op_layer=Zj,r.first=Object.create(Xl,{_op_layer:{value:Jj,enumerable:!0},first:{value:S8(),enumerable:!0},second:{value:Array.isArray(n)?DR(...n):n},zipK:{value:(i,l)=>x(i,Zi(l))}}),r.second=t,r})),T8=E(3,(t,n,r)=>kR(()=>{const i=Object.create(Xl);return i._op_layer=Wj,i.first=t,i.second=n,i.zipK=r,i})),G1=E(2,(t,n)=>iR(r=>X(AR(n,r),i=>jg(t,i)))),K1=E(2,(t,n)=>{const r=Vl(_s.fiberRefs,n.fiberRefs),i=pr(_s.runtimeFlags,n.runtimeFlags);return Xn(l=>Gt(u=>{const f=u.getFiberRef(Ba),h=u.getFiberRefs(),m=Gl(u.id(),h)(r),p=u.currentRuntimeFlags,g=Oi(i)(p),v=Vl(m,h),b=pr(g,p);return u.setFiberRefs(m),u.currentRuntimeFlags=g,Ql(jg(l(t),Zi(f,n.context)),Gt(O=>(O.setFiberRefs(Gl(O.id(),O.getFiberRefs())(v)),O.currentRuntimeFlags=Oi(b)(O.currentRuntimeFlags),ae)))}))}),O8=E(2,(t,n)=>Array.isArray(n)?G1(t,DR(...n)):g8(n)?G1(t,n):XD(n)?jg(t,n):Kj in n?X(n.runtimeEffect,r=>K1(t,r)):K1(t,n)),mB=ha,pB=f5,yB=nR,gB=Pi,vB=Gt,_B=le,bB=be,SB=cy,EB=Az,TB=m5,OB=pt,RB=Wt,MB=ot,wB=ae,AB=Rf,CB=Tg,xB=o5,DB=u5,kB=mO,NB=_5,zB=v5,LB=Ez,$B=Mg,UB=wg,FB=Ed,jB=Xn,BB=We,qB=ja,HB=Ft,IB=Sd,PB=_F,VB=S2,GB=Ql,KB=oR,QB=iR,YB=cc,XB=s0,ZB=qj,JB=Fg,WB=H2,tq=O8,eq=E5,nq=qi,aq=yr,rq=M2,sq=S5,iq=X,oq=vi,lq=_d,cq=NF,uq=cR,fq=Ag,dq=g5,hq=Lj,mq=pO,pq=O2,yq=$n,gq=RR,vq=Fj,_q=Bj,bq=c8,Sq=u8,Eq=f8,Tq=MF,Oq=wF,Rq=AF,Mq=mc,wq=R5,Aq=M5,R8=t=>{const n=new Map;return new Proxy(t,{get(r,i,l){if(i in r)return Reflect.get(r,i,l);if(n.has(i))return n.get(i);const u=(...h)=>vi(r,m=>typeof m[i]=="function"?(n.set(i,(...p)=>vi(r,g=>g[i](...p))),m[i](...h)):(n.set(i,vi(r,p=>p[i])),m[i])),f=vi(r,h=>h[i]);return Object.assign(u,f),Object.setPrototypeOf(u,Object.getPrototypeOf(f)),n.set(i,u),u}})},Id=t=>()=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=2;const r=new Error;Error.stackTraceLimit=n;function i(){}return Object.setPrototypeOf(i,ad),i.key=t,Object.defineProperty(i,"use",{get(){return l=>vi(this,l)}}),Object.defineProperty(i,"stack",{get(){return r.stack}}),R8(i)};class kp extends Id("AuthUsecase")(){}class gl extends Id("BrandUsecase")(){}class vl extends Id("ClientUsecase")(){}class _l extends Id("WorkerUsecase")(){}const M8={login:t=>kp.login(t),logout:()=>kp.logout(),getSession:()=>kp.getSession()},w8={create:t=>gl.create(t),getAll:()=>gl.getAll(),getById:t=>gl.getById(t),update:t=>gl.update(t),delete:t=>gl.delete(t)},A8={create:t=>vl.create(t),getAll:()=>vl.getAll(),getById:t=>vl.getById(t),update:t=>vl.update(t),delete:t=>vl.delete(t)},C8={create:t=>_l.create(t),getAll:()=>_l.getAll(),getById:t=>_l.getById(t),update:t=>_l.update(t),delete:t=>_l.delete(t)},x8={auth:M8,brand:w8,client:A8,worker:C8},D8="/_build/assets/app-D8wouJFs.css",gc=zC()({head:()=>({meta:[{charSet:"utf-8"},{name:"viewport",content:"width=device-width, initial-scale=1"},{title:"TanStack Start Starter"}],links:[{rel:"stylesheet",href:D8}]}),loader:()=>d3(),component:k8});function k8(){const t=gc.useLoaderData();return W.jsx(p3,{theme:t,children:W.jsx(N8,{children:W.jsx(o3,{service:x8,children:W.jsx(qy,{})})})})}function N8({children:t}){const{theme:n}=y3();return W.jsxs("html",{lang:"es","data-theme":n,suppressHydrationWarning:!0,children:[W.jsx("head",{children:W.jsx(PC,{})}),W.jsxs("body",{children:[t,W.jsx(i3,{theme:"dark"}),W.jsx(Ox,{}),W.jsx(Rx,{}),W.jsx(GC,{})]})]})}const z8=()=>ia(()=>import("./login-BXzM-CoY.js"),__vite__mapDeps([0,1,2,3,4])),NR=Vi("/login")({component:Gi(z8,"component",()=>NR.ssr)}),L8=()=>ia(()=>import("./route-BCGlW2to.js"),__vite__mapDeps([5,6,4,3])),zR=Vi("/_authed")({component:Gi(L8,"component",()=>zR.ssr)}),$8=()=>ia(()=>import("./index-9ef86wc6.js"),[]),LR=Vi("/")({component:Gi($8,"component",()=>LR.ssr)}),U8=()=>ia(()=>import("./route-r-1Gfi_b.js"),__vite__mapDeps([7,2,4])),$R=Vi("/_authed/admin")({component:Gi(U8,"component",()=>$R.ssr)}),F8=()=>ia(()=>import("./index-BzyKgCps.js"),[]),UR=Vi("/_authed/admin/")({component:Gi(F8,"component",()=>UR.ssr)}),j8=()=>ia(()=>import("./index-DPvDv-Mn.js"),__vite__mapDeps([8,6,3,4,1,2])),FR=Vi("/_authed/admin/clients/")({component:Gi(j8,"component",()=>FR.ssr)}),B8=NR.update({id:"/login",path:"/login",getParentRoute:()=>gc}),jR=zR.update({id:"/_authed",getParentRoute:()=>gc}),q8=LR.update({id:"/",path:"/",getParentRoute:()=>gc}),u0=$R.update({id:"/admin",path:"/admin",getParentRoute:()=>jR}),H8=UR.update({id:"/",path:"/",getParentRoute:()=>u0}),I8=FR.update({id:"/clients/",path:"/clients/",getParentRoute:()=>u0}),P8={AuthedAdminIndexRoute:H8,AuthedAdminClientsIndexRoute:I8},V8=u0._addFileChildren(P8),G8={AuthedAdminRouteRoute:V8},K8=jR._addFileChildren(G8),Q8={IndexRoute:q8,AuthedRouteRoute:K8,LoginRoute:B8},Y8=gc._addFileChildren(Q8)._addFileTypes();function X8(t,n,r){const i=new Set,l=new Set,u=n.getDefaultOptions();n.setDefaultOptions({...u,queries:{...u.queries,_experimental_beforeQuery:h=>{var m,p;(p=(m=u.queries)==null?void 0:m._experimental_beforeQuery)==null||p.call(m,h);const g=h.queryKeyHashFn||rs;if(t.isServer){if(i.has(g(h.queryKey)))return;if(i.add(g(h.queryKey)),n.getQueryData(h.queryKey)!==void 0){h.__skipInjection=!0;return}}else{const v=t.clientSsr.getStreamedValue("__QueryClient__"+g(h.queryKey));v&&!v.hydrated&&(v.hydrated=!0,DS(n,v))}},_experimental_afterQuery:(h,m)=>{var p,g;const v=h.queryKeyHashFn||rs;t.isServer&&!h.__skipInjection&&n.getQueryData(h.queryKey)!==void 0&&!l.has(v(h.queryKey))&&(l.add(v(h.queryKey)),t.serverSsr.streamValue("__QueryClient__"+v(h.queryKey),xS(n,{shouldDehydrateMutation:()=>!1,shouldDehydrateQuery:b=>v(b.queryKey)===v(h.queryKey)}))),(g=(p=u.queries)==null?void 0:p._experimental_afterQuery)==null||g.call(p,h,m)}}});{const h=n.getMutationCache().config;n.getMutationCache().config={...h,onError:(p,g,v,b)=>{var O;return In(p)?t.navigate(t.resolveRedirect({...p,_fromLocation:t.state.location})):(O=h.onError)==null?void 0:O.call(h,p,g,v,b)}};const m=n.getQueryCache().config;n.getQueryCache().config={...m,onError:(p,g)=>{var v;return In(p)?t.navigate(t.resolveRedirect({...p,_fromLocation:t.state.location})):(v=m.onError)==null?void 0:v.call(m,p,g)}}}const f=t.options;return t.options={...t.options,dehydrate:()=>{var h;return{...(h=f.dehydrate)==null?void 0:h.call(f),dehydratedQueryClient:xS(n)}},hydrate:h=>{var m;(m=f.hydrate)==null||m.call(f,h),DS(n,h.dehydratedQueryClient)},context:{...f.context,queryClient:n},Wrap:({children:h})=>{const m=at.Fragment,p=f.Wrap||at.Fragment;return W.jsx(m,{children:W.jsx(Tx,{client:n,children:W.jsx(p,{children:h})})})}},t}function Z8(){const t=new gx({defaultOptions:{queries:{retry:!1}}});return X8(jC({routeTree:Y8,defaultPendingComponent:()=>W.jsx("div",{children:"Cargando..."}),context:{queryClient:t},defaultPreload:"intent",defaultPreloadStaleTime:0,scrollRestoration:!0}),t)}const J8=Z8();fA.hydrateRoot(document,W.jsx(KC,{router:J8}));const Cq=void 0;export{AD as $,mx as A,E as B,u7 as C,Si as D,Rt as E,Zf as F,NE as G,Pp as H,s7 as I,i7 as J,S3 as K,fE as L,$E as M,W8 as N,qy as O,ca as P,zn as Q,Ht as R,Np as S,_7 as T,Tt as U,yt as V,Fa as W,rT as X,b7 as Y,$t as Z,Jy as _,r7 as a,bT as a$,Ml as a0,gf as a1,T7 as a2,E7 as a3,aD as a4,Nl as a5,kD as a6,B2 as a7,L7 as a8,yL as a9,fB as aA,DR as aB,x as aC,X as aD,ot as aE,Ft as aF,fd as aG,kd as aH,Wt as aI,Rf as aJ,Ez as aK,Sr as aL,Ed as aM,Gt as aN,dd as aO,ja as aP,C2 as aQ,Ve as aR,as as aS,z7 as aT,pt as aU,ce as aV,os as aW,OD as aX,ik as aY,j2 as aZ,wg as a_,Ad as aa,pL as ab,Et as ac,re as ad,pe as ae,_e as af,Ln as ag,Yn as ah,ls as ai,D7 as aj,ic as ak,u2 as al,ql as am,N7 as an,m2 as ao,Qn as ap,ez as aq,d2 as ar,ra as as,k7 as at,U2 as au,qe as av,Zz as aw,Ba as ax,uB as ay,hB as az,lE as b,pq as b$,q2 as b0,M7 as b1,Gn as b2,sk as b3,ae as b4,Rg as b5,$7 as b6,W3 as b7,mB as b8,Vt as b9,B7 as bA,tq as bB,yq as bC,P7 as bD,XB as bE,$B as bF,GB as bG,FB as bH,HB as bI,Tq as bJ,V7 as bK,H7 as bL,gB as bM,OB as bN,j7 as bO,G7 as bP,SB as bQ,CB as bR,I7 as bS,t5 as bT,lq as bU,Z7 as bV,YB as bW,LB as bX,_r as bY,Vn as bZ,q7 as b_,wB as ba,DB as bb,Mq as bc,aq as bd,Q7 as be,jB as bf,yB as bg,fj as bh,Sy as bi,iq as bj,VB as bk,ZB as bl,Rq as bm,aB as bn,rB as bo,t8 as bp,sB as bq,UT as br,x7 as bs,MB as bt,RB as bu,rq as bv,Oq as bw,dq as bx,W7 as by,bB as bz,a7 as c,MD as c$,QB as c0,nB as c1,eB as c2,uj as c3,Ot as c4,es as c5,Ip as c6,KB as c7,K7 as c8,EB as c9,_B as cA,A7 as cB,w7 as cC,AB as cD,wt as cE,At as cF,ue as cG,Lt as cH,mt as cI,eD as cJ,v7 as cK,p7 as cL,g7 as cM,Pr as cN,zE as cO,yi as cP,f7 as cQ,pi as cR,nq as cS,O7 as cT,on as cU,bq as cV,v$ as cW,U7 as cX,pD as cY,y7 as cZ,S7 as c_,vq as ca,uq as cb,sq as cc,cq as cd,BB as ce,X7 as cf,hq as cg,J7 as ch,qB as ci,vB as cj,gq as ck,fq as cl,tB as cm,iB as cn,oB as co,Iu as cp,Hd as cq,IB as cr,mq as cs,Je as ct,_q as cu,Eq as cv,Sq as cw,PB as cx,TB as cy,zB as cz,Zr as d,c7 as d0,Jf as d1,l7 as d2,o7 as d3,b8 as d4,f8 as d5,i0 as d6,c8 as d7,Ag as d8,dB as d9,kB as dA,bs as dB,JB as dC,WB as dD,Zi as dE,eq as dF,ts as dG,rd as dH,wq as dI,uf as dJ,UB as dK,Aq as dL,F7 as dM,R7 as dN,xB as dO,oq as dP,Id as dQ,kp as dR,gl as dS,vl as dT,_l as dU,Cq as dV,_d as da,Kj as db,Y7 as dc,u8 as dd,i8 as de,e8 as df,_s as dg,lB as dh,TR as di,n8 as dj,cB as dk,ER as dl,Mf as dm,vn as dn,xd as dp,d7 as dq,hk as dr,Qy as ds,L6 as dt,sT as du,xD as dv,$3 as dw,h7 as dx,NB as dy,pB as dz,ci as e,hC as f,nA as g,Pf as h,ZC as i,W as j,TS as k,Vf as l,YC as m,kn as n,pE as o,rx as p,ux as q,at as r,e7 as s,XC as t,y3 as u,WC as v,Ye as w,n7 as x,te as y,rs as z};
