{"name": "fhyona-v2-frontend-prod", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@babel/runtime": "7.27.1", "@effect/platform": "0.82.6", "@tanstack/form-core": "1.11.3", "@tanstack/history": "1.115.0", "@tanstack/query-core": "5.76.2", "@tanstack/query-devtools": "5.76.0", "@tanstack/react-form": "1.11.3", "@tanstack/react-query": "5.76.2", "@tanstack/react-query-devtools": "5.76.2", "@tanstack/react-router": "1.120.9", "@tanstack/react-router-devtools": "1.120.9", "@tanstack/react-router-with-query": "1.120.9", "@tanstack/react-store": "0.7.0", "@tanstack/react-table": "8.21.3", "@tanstack/router-core": "1.120.9", "@tanstack/router-devtools-core": "1.120.9", "@tanstack/start-client-core": "1.120.9", "@tanstack/start-server-core": "1.120.9", "@tanstack/store": "0.7.0", "@tanstack/table-core": "8.21.3", "clsx": "2.1.1", "compute-scroll-into-view": "3.1.1", "cookie-es": "1.2.2", "defu": "6.1.4", "destr": "2.0.3", "downshift": "9.0.9", "effect": "3.15.3", "fast-check": "3.23.2", "find-my-way-ts": "0.1.5", "goober": "2.1.16", "h3": "1.13.0", "iron-webcrypto": "1.2.1", "isbot": "5.1.25", "jsesc": "3.1.0", "lucide-react": "0.511.0", "msgpackr": "1.11.4", "multipasta": "0.2.5", "mutative": "1.2.0", "object-assign": "4.1.1", "ohash": "1.1.6", "pathe": "1.1.2", "prop-types": "15.8.1", "pure-rand": "6.1.0", "radix3": "1.1.2", "react": "19.1.0", "react-dom": "19.1.0", "react-is": "18.2.0", "react-toastify": "11.0.5", "seroval": "1.2.1", "seroval-plugins": "1.2.1", "solid-js": "1.9.5", "tailwind-merge": "3.3.0", "tiny-invariant": "1.3.3", "tiny-warning": "1.0.3", "tslib": "2.8.1", "ufo": "1.5.4", "uncrypto": "0.1.3", "unctx": "2.4.1", "unenv": "1.10.0", "use-mutative": "1.3.0", "use-sync-external-store": "1.4.0", "valibot": "1.1.0", "vinxi": "0.5.6"}}