import { jsxs, jsx } from 'react/jsx-runtime';
import { Outlet, Link, useNavigate } from '@tanstack/react-router';
import { Home, Users, UserCog, User } from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { g as gt } from '../nitro/nitro.mjs';
import { _ as _t } from './runtimes-OMKR2jB5.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:async_hooks';
import 'vinxi/lib/invariant';
import 'vinxi/lib/path';
import 'node:url';
import '@tanstack/router-core';
import 'tiny-invariant';
import '@tanstack/start-server-core';
import '@tanstack/start-client-core';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'react-toastify';
import 'react';
import 'effect';
import '@tanstack/react-router-with-query';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import '@effect/platform';
import 'valibot';

function p() {
  return jsx("div", { className: "h-full w-64 bg-base-300 shadow-lg", children: jsxs("div", { className: "p-4", children: [jsx("h2", { className: "mb-6 font-bold text-2xl text-primary", children: "Schedhold" }), jsxs("ul", { className: "menu menu-lg w-full rounded-box bg-base-200", children: [jsx("li", { children: jsxs(Link, { to: "/admin", className: "flex items-center gap-3 hover:bg-base-300", children: [jsx(Home, { className: "h-5 w-5" }), "Home"] }) }), jsx("li", { children: jsxs(Link, { to: "/admin/clients", className: "flex items-center gap-3 hover:bg-base-300", children: [jsx(Users, { className: "h-5 w-5" }), "Clientes"] }) }), jsx("li", { children: jsxs(Link, { to: "/admin/workers", className: "flex items-center gap-3 hover:bg-base-300", children: [jsx(UserCog, { className: "h-5 w-5" }), "Trabajadores"] }) })] })] }) });
}
const b = () => {
  const { auth: o } = gt();
  return useMutation({ mutationKey: ["logout"], mutationFn: () => _t.runPromise(o.logout()) });
};
function f() {
  const { mutate: o } = b(), l = useNavigate();
  return jsxs("div", { className: "navbar w-full bg-neutral text-neutral-content", children: [jsx("div", { className: "flex-1", children: jsx("button", { type: "button", className: "btn btn-ghost text-xl", children: "Schedhold" }) }), jsx("div", { className: "flex-none", children: jsxs("details", { className: "dropdown dropdown-end", children: [jsx("summary", { className: "btn btn-circle avatar", children: jsx(User, { size: 26 }) }), jsxs("ul", { className: "menu menu-sm dropdown-content z-[1] mt-3 w-52 rounded-box bg-neutral p-2 shadow", children: [jsx("li", { children: jsx("span", { children: "Profile" }) }), jsx("li", { children: jsx("span", { children: "Settings" }) }), jsx("li", { children: jsx("button", { type: "button", onClick: () => {
    o(void 0, { onSettled: () => {
      l({ to: "/login" });
    } });
  }, children: "Logout" }) })] })] }) })] });
}
const q = function() {
  return jsxs("div", { className: "flex h-screen w-full ", children: [jsx(p, {}), jsxs("div", { className: "flex w-[calc(100%-16rem)] min-w-0 flex-col", children: [jsx(f, {}), jsx("div", { className: "flex-1 overflow-y-auto bg-base-200", children: jsx("div", { className: "container mx-auto px-4 py-8", children: jsx(Outlet, {}) }) })] })] });
};

export { q as component };
//# sourceMappingURL=route-DqqLROOy.mjs.map
