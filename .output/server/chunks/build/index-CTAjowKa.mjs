import { jsxs, jsx } from 'react/jsx-runtime';
import { Link } from '@tanstack/react-router';

const o = function() {
  return jsxs("div", { className: "hero min-h-screen", style: { backgroundImage: "url(/background.webp)" }, children: [jsx("div", { className: "hero-overlay" }), jsx("div", { className: "hero-content text-center text-neutral-content ", children: jsxs("div", { className: "max-w-md", children: [jsx("h1", { className: "mb-5 font-bold text-5xl", children: "FHYONA 2" }), jsx("p", { className: "mb-5 text-lg", children: "Sistema integral para ventas, almacenamiento y manufactura. Dise\xF1ado para simplificar y optimizar tus procesos, con un enfoque especializado en producci\xF3n para llevar tu negocio al siguiente nivel." }), jsx(Link, { to: "/login", className: "btn btn-primary", children: "Iniciar" })] }) })] });
};

export { o as component };
//# sourceMappingURL=index-CTAjowKa.mjs.map
