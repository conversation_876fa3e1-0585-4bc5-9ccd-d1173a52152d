import { createFormHook, createFormHookContexts } from '@tanstack/react-form';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import { useMultipleSelection, useCombobox } from 'downshift';
import { KeyRound, EyeOff, Eye, X, Eraser, ArrowUp, ArrowDown } from 'lucide-react';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { useMutative } from 'use-mutative';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

const S = (...n) => twMerge(clsx(n));
function V(n) {
  return n.isMultiple ? jsx(te, { ...n }) : jsx(le, { ...n });
}
function te({ value: n = [], isLoading: i = false, options: l, defaultSelected: t = [], onChange: a, placeholder: s = "Seleccionar...", className: b, hideReset: H = false, size: E = "md" }) {
  const [h, w] = useState(t);
  useEffect(() => {
    w(t);
  }, [t]);
  const p = useMemo(() => l.filter((o) => !h.some((r) => r.value === o.value)), [h, l]), f = useCallback((o) => {
    w(o), a(o);
  }, [a]), y = useCallback((o) => {
    const r = h.filter((m) => m.value !== o.value);
    f(r);
  }, [h, f]), j = useCallback(() => {
    f([]);
  }, [f]), { getSelectedItemProps: v, getDropdownProps: D, removeSelectedItem: O } = useMultipleSelection({ selectedItems: h, defaultSelectedItems: t, onStateChange({ selectedItems: o, type: r }) {
    switch (r) {
      case useMultipleSelection.stateChangeTypes.SelectedItemKeyDownBackspace:
      case useMultipleSelection.stateChangeTypes.SelectedItemKeyDownDelete:
      case useMultipleSelection.stateChangeTypes.DropdownKeyDownBackspace:
      case useMultipleSelection.stateChangeTypes.FunctionRemoveSelectedItem:
        o && f(o);
        break;
    }
  } }), { isOpen: x, getToggleButtonProps: k, getMenuProps: M, getInputProps: F, highlightedIndex: P, getItemProps: B, setInputValue: c } = useCombobox({ items: p, itemToString: (o) => {
    var _a;
    return (_a = o == null ? void 0 : o.label) != null ? _a : "";
  }, defaultHighlightedIndex: 0, selectedItem: null, stateReducer(o, r) {
    const { changes: m, type: L } = r;
    switch (L) {
      case useCombobox.stateChangeTypes.InputKeyDownEnter:
      case useCombobox.stateChangeTypes.ItemClick:
        return { ...m, isOpen: true, highlightedIndex: 0 };
      default:
        return m;
    }
  }, onStateChange({ type: o, selectedItem: r }) {
    switch (o) {
      case useCombobox.stateChangeTypes.InputKeyDownEnter:
      case useCombobox.stateChangeTypes.ItemClick:
      case useCombobox.stateChangeTypes.InputBlur:
        if (r) {
          const m = [...h, r];
          f(m), c("");
        }
        break;
    }
  } }), u = useCallback((o, r) => jsxs("div", { className: "badge badge-primary pr-0", children: [jsx("div", { className: "rounded-l-md", ...v({ selectedItem: o, index: r }), children: o.label }), jsx("button", { type: "button", className: "btn btn-xs btn-circle btn-error btn-ghost", onClick: (m) => {
    m.stopPropagation(), y(o);
  }, children: jsx(X, { size: 16 }) })] }, `selected-item-${o.value}-${r}`), [v, y]), g = useCallback((o, r) => jsx("li", { className: S("flex cursor-pointer flex-col px-3 py-2 shadow-sm", P === r && "bg-neutral"), ...B({ item: o, index: r }), children: jsx("span", { children: o.label }) }, `${o.value}-${r}`), [P, B]);
  return jsxs("div", { className: S("dropdown", b), children: [jsxs("div", { className: "join input h-fit w-full items-center rounded-md p-1", children: [i ? jsx("span", { className: "loading loading-dots join-item loading-sm" }) : jsxs("div", { className: "inline-flex w-full flex-wrap items-center gap-1", children: [h.map(u), jsx("input", { placeholder: s, className: "input input-ghost flex-1 focus:outline-none", ...F(D({ preventKeyAction: x })) })] }), jsx("button", { className: "btn btn-sm join-item btn-ghost btn-circle", type: "button", onClick: j, disabled: h.length === 0, children: jsx(Eraser, {}) }), jsx("button", { "aria-label": "toggle menu", className: "btn btn-sm join-item btn-ghost btn-circle", type: "button", ...k(), children: x ? jsx(ArrowUp, { className: "h-6 w-6" }) : jsx(ArrowDown, { className: "h-6 w-6" }) })] }), jsx("ul", { className: S("dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm", (!x || !p.length) && "hidden"), ...M(), children: x && p.map(g) })] });
}
function le({ value: n = null, isLoading: i = false, options: l, defaultSelected: t = null, onChange: a, placeholder: s = "Seleccionar...", className: b, size: H = "md", label: E = "Seleccionar...", hideReset: h = false }) {
  const [w, p] = useMutative(l), [f, y] = useState((t == null ? void 0 : t.label) || (n == null ? void 0 : n.label) || ""), j = useCallback((c) => {
    p((u) => {
      for (let g = 0; g < l.length; g++) u[g] = l[g];
      if (u.length = l.length, c) {
        let g = 0;
        const o = c.toLowerCase();
        for (let r = 0; r < u.length; r++) {
          const m = u[r];
          (m == null ? void 0 : m.label.toLowerCase().includes(o)) && (u[g] = m, g++);
        }
        u.length = g;
      }
    });
  }, [l, p]), { isOpen: v, getToggleButtonProps: D, getMenuProps: O, getInputProps: x, highlightedIndex: k, getItemProps: M, reset: F, getLabelProps: P } = useCombobox({ items: w, defaultSelectedItem: t || n, inputValue: f, itemToString: (c) => {
    var _a;
    return (_a = c == null ? void 0 : c.label) != null ? _a : "";
  }, onInputValueChange({ inputValue: c }) {
    y(c || ""), j(c || "");
  }, onSelectedItemChange: ({ selectedItem: c }) => {
    a(c);
  }, onIsOpenChange: ({ isOpen: c, selectedItem: u }) => {
    c || y((u == null ? void 0 : u.label) || "");
  } });
  useEffect(() => {
    p(l);
  }, [l, p]), useEffect(() => {
    n === null && F();
  }, [n, F]);
  const B = useCallback((c, u) => jsx("li", { className: S("flex cursor-pointer flex-col px-3 py-2 shadow-sm", k === u && "bg-neutral", (n == null ? void 0 : n.value) === c.value && "font-bold"), ...M({ item: c, index: u }), children: jsx("span", { children: c.label }) }, `${c.value}-${u}`), [k, n, M]);
  return jsxs("div", { className: S("dropdown", b), children: [jsx("label", { htmlFor: "combo", className: "label", ...P(), children: E }), jsxs("div", { className: "join h-fit w-full items-center bg-base-100", children: [i ? jsx("span", { className: "loading loading-dots join-item loading-sm" }) : jsx("input", { placeholder: s, className: "input input-sm input-bordered join-item w-full", ...x() }), !h && jsx("button", { className: "btn btn-sm join-item", type: "button", onClick: F, disabled: !n, children: jsx(Eraser, {}) }), jsx("button", { "aria-label": "toggle menu", className: "btn btn-sm join-item", type: "button", ...D(), children: v ? jsx(ArrowUp, { className: "h-6 w-6" }) : jsx(ArrowDown, { className: "h-6 w-6" }) })] }), jsx("ul", { className: S("dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm", (!v || !w.length) && "hidden"), ...O(), children: v && w.map(B) })] });
}
function ae({ label: n, placeholder: i, options: l, isNumber: t = false, isMultiple: a = false }) {
  return jsxs(Fragment, { children: [jsx("label", { htmlFor: "combobox", className: "label", children: n }), a ? jsx(se, { placeholder: i, options: l, isNumber: t }) : jsx(ne, { placeholder: i, options: l })] });
}
function ne({ placeholder: n, options: i, isLoading: l = false }) {
  const t = I();
  return jsx(V, { isMultiple: false, options: i, onChange: (a) => t.handleChange((a == null ? void 0 : a.value) || ""), placeholder: n, isLoading: l, defaultSelected: i.find((a) => a.value === t.state.value) || null });
}
function se({ placeholder: n, options: i, isLoading: l = false }) {
  const t = I();
  return jsx(V, { isMultiple: true, options: i, onChange: (a) => t.handleChange(a.map((s) => s.value)), placeholder: n, isLoading: l, defaultSelected: i.filter((a) => t.state.value.includes(a.value)) });
}
function oe({ label: n, placeholder: i }) {
  const l = I(), [t, a] = useState(false);
  return jsxs("fieldset", { className: "fieldset", children: [jsx("legend", { className: "fieldset-legend", children: n }), jsxs("label", { className: "input w-full", children: [jsx(KeyRound, { size: 16 }), jsx("input", { type: t ? "text" : "password", className: "grow", placeholder: i, value: l.state.value, onChange: (s) => l.handleChange(s.target.value) }), jsx("button", { type: "button", className: "btn btn-sm btn-circle", onClick: () => a(!t), children: t ? jsx(EyeOff, { size: 16 }) : jsx(Eye, { size: 16 }) })] }), l.state.meta.isTouched && l.state.meta.errors.length ? l.state.meta.errors.map((s) => jsx("p", { className: "fieldset-label text-error", children: s.message }, s.path)) : null] });
}
function ie({ label: n, placeholder: i, options: l, isNumber: t = false }) {
  var _a;
  const a = I();
  return jsxs("fieldset", { className: "fieldset", children: [jsx("legend", { className: "fieldset-legend", children: n }), jsxs("select", { className: "select w-full", value: t ? a.state.value : (_a = a.state.value) == null ? void 0 : _a.toString(), onChange: (s) => a.handleChange(t ? Number(s.target.value) : s.target.value), children: [jsx("option", { disabled: true, selected: true, children: i || "Seleccione una opci\xF3n" }), l.map((s) => jsx("option", { value: s.value, children: s.label }, s.value))] }), a.state.meta.isTouched && a.state.meta.errors.length ? a.state.meta.errors.flatMap((s) => jsx("p", { className: "fieldset-label text-error", children: s.message }, s.message)) : null] });
}
function re({ label: n, placeholder: i, type: l = "text", prefixComponent: t, suffixComponent: a }) {
  var _a;
  const s = I();
  return jsxs("fieldset", { className: "fieldset", children: [jsx("legend", { className: "fieldset-legend", children: n }), jsxs("div", { className: "input w-full", children: [t && t, jsx("input", { type: l, className: "grow", placeholder: i, value: l === "number" ? (_a = s.state.value) == null ? void 0 : _a.toString() : s.state.value, onChange: (b) => s.handleChange(l === "number" ? Number(b.target.value) : b.target.value) }), a && a] }), s.state.meta.isTouched && s.state.meta.errors.length ? s.state.meta.errors.flatMap((b) => jsx("p", { className: "fieldset-label text-error", children: b.message }, b.message)) : null] });
}
function ce({ label: n, trueLabel: i, falseLabel: l }) {
  const t = I();
  return jsxs("fieldset", { className: "fieldset", children: [jsx("legend", { className: "fieldset-legend", children: n }), jsxs("label", { className: "fieldset-label", children: [jsx("input", { type: "checkbox", className: "toggle", defaultChecked: true, checked: t.state.value, onChange: () => t.handleChange(!t.state.value) }), t.state.value ? i : l] }), t.state.meta.isTouched && t.state.meta.errors.length ? t.state.meta.errors.flatMap((a) => jsx("p", { className: "fieldset-label text-error", children: a.message }, a.message)) : null] });
}
function de({ label: n, className: i = "btn btn-neutral" }) {
  const { Subscribe: l } = he();
  return jsx(l, { selector: (t) => ({ isSubmitting: t.isSubmitting, canSubmit: t.canSubmit }), children: ({ isSubmitting: t, canSubmit: a }) => jsxs("button", { disabled: t || !a, className: i, children: [t && jsx("span", { className: "loading loading-spinner" }), n] }) });
}
const { fieldContext: ue, useFieldContext: I, formContext: me, useFormContext: he } = createFormHookContexts(), { useAppForm: xe, withForm: Se } = createFormHook({ fieldComponents: { FSTextField: re, FSPasswordField: oe, FSSelectField: ie, FSToggleField: ce, FSComboBoxField: ae }, formComponents: { SubscribeButton: de }, fieldContext: ue, formContext: me });

export { S, xe as x };
//# sourceMappingURL=form-9KniRK7M.mjs.map
