import { jsx, jsxs } from 'react/jsx-runtime';
import { F as Fe, g as gt } from '../nitro/nitro.mjs';
import { x as xe, S as S$1 } from './form-9KniRK7M.mjs';
import { useNavigate } from '@tanstack/react-router';
import { User } from 'lucide-react';
import { toast } from 'react-toastify';
import { n } from './effectErrors-BZsTgurj.mjs';
import { useMutation } from '@tanstack/react-query';
import { _ as _t } from './runtimes-OMKR2jB5.mjs';
import { Store } from '@tanstack/react-store';
import * as a from 'valibot';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:async_hooks';
import 'vinxi/lib/invariant';
import 'vinxi/lib/path';
import 'node:url';
import '@tanstack/router-core';
import 'tiny-invariant';
import '@tanstack/start-server-core';
import '@tanstack/start-client-core';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'react';
import 'effect';
import '@tanstack/react-router-with-query';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import '@tanstack/react-form';
import 'downshift';
import 'use-mutative';
import 'clsx';
import 'tailwind-merge';
import 'effect/Runtime';
import '@effect/platform';

const S = ["light", "dark", "cupcake", "bumblebee", "emerald", "corporate", "synthwave", "retro", "cyberpunk", "valentine", "halloween", "garden", "forest", "aqua", "lofi", "pastel", "fantasy", "wireframe", "black", "luxury", "dracula", "cmyk", "autumn", "business", "acid", "lemonade", "night", "coffee", "winter", "dim", "nord", "sunset", "caramellatte", "abyss", "silk"];
function y({ className: t }) {
  const { setTheme: n, theme: o } = Fe();
  return jsx("select", { defaultValue: "Elige un tema", className: S$1("select", t), value: o, onChange: (r) => n(r.target.value), children: S.map((r) => jsx("option", { value: r, children: r }, r)) });
}
function N() {
  const { auth: t } = gt();
  return useMutation({ mutationKey: ["login"], mutationFn: (n) => _t.runPromise(t.login(n)) });
}
const m = new Store(void 0), A = { setUser: (t) => m.setState(() => t), clearUser: () => m.setState(() => {
}) }, C = a.object({ username: a.pipe(a.string("Debe ingresar una cuenta"), a.minLength(4, "Debe tener al menos 4 caracteres")), password: a.pipe(a.string("Debe ingresar su contrase\xF1a"), a.minLength(4, "Debe tener al menos 4 caracteres")) });
function k() {
  const { mutateAsync: t } = N(), n$1 = useNavigate(), o = xe({ defaultValues: { username: "", password: "" }, onSubmit: async ({ value: r }) => {
    t(r, { onSuccess(i) {
      A.setUser(i.user), n$1({ to: "/admin" });
    }, onError(i) {
      const l = n(i);
      toast.error(l.error.message);
    } });
  }, validators: { onChange: C } });
  return jsx("form", { onSubmit: (r) => {
    r.preventDefault(), o.handleSubmit();
  }, children: jsxs(o.AppForm, { children: [jsx(o.AppField, { name: "username", children: ({ FSTextField: r }) => jsx(r, { label: "Usuario o Correo", placeholder: "<EMAIL>", prefixComponent: jsx(User, { size: 16 }) }) }), jsx(o.AppField, { name: "password", children: ({ FSPasswordField: r }) => jsx(r, { label: "Contrase\xF1a", placeholder: "Contrase\xF1a" }) }), jsx(o.SubscribeButton, { label: "Iniciar sesi\xF3n", className: "btn btn-neutral mt-4 w-full" })] }) });
}
const te = function() {
  return jsx("div", { className: "hero min-h-screen bg-base-200", children: jsxs("div", { className: "hero-content flex-col lg:flex-row-reverse", children: [jsxs("div", { className: "text-center lg:text-left", children: [jsx("h1", { className: "font-bold text-5xl", children: "\xA1Inicia sesi\xF3n ahora!" }), jsx("p", { className: "py-6", children: "Accede a tu cuenta para gestionar tus ventas, controlar tu inventario y optimizar tus procesos de manufactura. \xA1Todo en un solo lugar con FHYONA!" })] }), jsx("div", { className: "card w-full max-w-sm shrink-0 bg-base-100 shadow-2xl", children: jsxs("div", { className: "card-body", children: [jsx(k, {}), jsx(y, { className: "w-full" })] }) })] }) });
};

export { te as component };
//# sourceMappingURL=login-BPbgEO0C.mjs.map
