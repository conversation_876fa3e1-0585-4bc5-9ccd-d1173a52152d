import { ManagedRuntime, Schema, Layer, Effect, Context, Data, Either } from 'effect';
import { HttpBody, FetchHttpClient, HttpClient, HttpClientRequest } from '@effect/platform';
import * as a from 'valibot';
import { T as T$1, m as m$1, h as h$1, f as f$1 } from '../nitro/nitro.mjs';

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, key + "" , value);
const k = Schema.Struct({ message: Schema.String, details: Schema.optional(Schema.Unknown), code: Schema.Number }), gt = Schema.Struct({ error: k, correlationId: Schema.String }), ee = { BASE_URL: "/_build", CWD: "/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend", DEV: false, DEVTOOLS: false, MANIFEST: globalThis.MANIFEST, MODE: "production", PROD: true, ROUTERS: ["public", "client", "ssr", "server", "api"], ROUTER_HANDLER: "app/ssr.tsx", ROUTER_NAME: "ssr", ROUTER_TYPE: "http", SERVER_BASE_URL: "", SSR: true, TSS_API_BASE: "/api", TSS_CLIENT_BASE: "/_build", TSS_OUTPUT_PUBLIC_DIR: "/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend/.output/public", TSS_PUBLIC_BASE: "/", VITE_API_URL: "http://localhost:8000" }, te = a.object({ VITE_API_URL: a.string() }), re = a.parse(te, ee), u = { API_URL: re.VITE_API_URL, CORRELATION_ID_HEADER: "x-correlation-id" };
class l extends Data.TaggedError("AppError") {
}
var o = ((t) => (t[t.ClientParseError = -3] = "ClientParseError", t[t.RequestError = -2] = "RequestError", t[t.ResponseError = -1] = "ResponseError", t[t.InternalErrorCode = 0] = "InternalErrorCode", t[t.MultiErrorCode = 1] = "MultiErrorCode", t[t.ParseErrorCode = 2] = "ParseErrorCode", t[t.BadRequestCode = 3] = "BadRequestCode", t[t.UnauthorizedCode = 4] = "UnauthorizedCode", t[t.NotFoundCode = 5] = "NotFoundCode", t[t.ConflictCode = 6] = "ConflictCode", t))(o || {});
const ne = FetchHttpClient.layer.pipe(Layer.provide(Layer.succeed(FetchHttpClient.RequestInit, { credentials: "include" }))), ae = Effect.gen(function* () {
  return { httpClient: (yield* HttpClient.HttpClient).pipe(HttpClient.mapRequest(HttpClientRequest.prependUrl(`${u.API_URL}/api`)), HttpClient.filterStatusOk, HttpClient.transformResponse(Effect.catchTags({ RequestError: (n) => Effect.fail(new l({ correlationId: o.RequestError.toString(), error: { code: o.RequestError, details: n, message: "Request error" } })), ResponseError: (n) => n.response.json.pipe(Effect.flatMap((c) => Schema.decodeUnknownEither(k)(c).pipe(Either.match({ onLeft: (b) => {
    var _a;
    return Effect.fail(new l({ correlationId: (_a = n.response.headers[u.CORRELATION_ID_HEADER]) != null ? _a : o.ResponseError.toString(), error: { code: o.ResponseError, details: b, message: "Response error" } }));
  }, onRight: (b) => {
    var _a;
    return Effect.fail(new l({ correlationId: (_a = n.response.headers[u.CORRELATION_ID_HEADER]) != null ? _a : o.ResponseError.toString(), error: b }));
  } })))) })), HttpClient.transformResponse(Effect.catchTags({ ResponseError: (n) => n.response.json.pipe(Schema.decodeUnknownEither(k), Either.match({ onLeft: (c) => {
    var _a;
    return Effect.fail(new l({ correlationId: (_a = n.response.headers[u.CORRELATION_ID_HEADER]) != null ? _a : o.ResponseError.toString(), error: { code: o.ClientParseError, details: c, message: "Response error" } }));
  }, onRight: (c) => {
    var _a;
    return Effect.fail(new l({ correlationId: (_a = n.response.headers[u.CORRELATION_ID_HEADER]) != null ? _a : o.ResponseError.toString(), error: c }));
  } })) }))) };
}).pipe(Effect.provide(ne));
const _i = class _i extends Context.Tag("ApiHttpClient")() {
};
__publicField(_i, "Live", Layer.effect(_i, ae));
let i = _i;
const s = (t) => (n) => n.json.pipe(Effect.flatMap(Schema.decodeUnknown(t)), Effect.catchTags({ ParseError: (c) => Effect.fail(new l({ correlationId: o.ClientParseError.toString(), error: { code: o.ClientParseError, details: c, message: "Parse error" } })), ResponseError: (c) => {
  var _a;
  return Effect.fail(new l({ correlationId: (_a = n.headers[u.CORRELATION_ID_HEADER]) != null ? _a : o.ResponseError.toString(), error: { code: o.ResponseError, details: c, message: "Response error" } }));
} })), p = (t) => t.json.pipe(Effect.catchTags({ ResponseError: (n) => {
  var _a;
  return Effect.fail(new l({ correlationId: (_a = t.headers[u.CORRELATION_ID_HEADER]) != null ? _a : o.ResponseError.toString(), error: { code: o.ResponseError, details: n, message: "Response error" } }));
} }), Effect.andThen(Effect.succeed(Effect.void)));
class y extends Effect.Tag("AuthRepository")() {
}
const W = Schema.Struct({ id: Schema.String, name: Schema.String, email: Schema.String, createdAt: Schema.NullOr(Schema.String), updatedAt: Schema.NullOr(Schema.String), deletedAt: Schema.NullOr(Schema.String) }), oe = Schema.Struct({ id: Schema.String, name: Schema.String, email: Schema.String, created_at: Schema.NullOr(Schema.String), updated_at: Schema.NullOr(Schema.String), deleted_at: Schema.NullOr(Schema.String) }), se = Schema.transform(oe, W, { strict: true, decode: (t) => ({ ...t, createdAt: t.created_at, updatedAt: t.updated_at, deletedAt: t.deleted_at }), encode: (t) => ({ ...t, created_at: t.createdAt, updated_at: t.updatedAt, deleted_at: t.deletedAt }) }), de = Schema.Struct({ username: Schema.String, password: Schema.String });
Schema.Struct({ user: W });
const w = Schema.Struct({ user: se }), ie = de, T = "/v1/auth", ce = i.pipe(Effect.andThen(({ httpClient: t }) => t), Effect.andThen((t) => ({ login: (n) => t.post(`${T}/login`, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync(ie)(n)) }).pipe(Effect.flatMap(s(w))), logout: () => t.post(`${T}/logout`).pipe(Effect.flatMap(p)), getSession: () => t.get(`${T}/is_logged_in`).pipe(Effect.flatMap(s(w))) })), Effect.provide(i.Live)), pe = Layer.effect(y, ce), le = Layer.effect(y, y), ue = Layer.effect(T$1, y);
class N extends Effect.Tag("BrandRepository")() {
}
const F = Schema.Struct({ id: Schema.String, name: Schema.String, code: Schema.String, createdAt: Schema.NullOr(Schema.String), updatedAt: Schema.NullOr(Schema.String), deletedAt: Schema.NullOr(Schema.String) }), me = Schema.Struct({ name: Schema.String, code: Schema.String }), Se = Schema.Struct({ id: Schema.String, name: Schema.String, code: Schema.String }), ge = Schema.Struct({ id: Schema.String, name: Schema.String, code: Schema.String, created_at: Schema.NullOr(Schema.String), updated_at: Schema.NullOr(Schema.String), deleted_at: Schema.NullOr(Schema.String) }), H = Schema.transform(ge, F, { strict: true, decode: (t) => ({ ...t, createdAt: t.created_at, updatedAt: t.updated_at, deletedAt: t.deleted_at }), encode: (t) => ({ ...t, created_at: t.createdAt, updated_at: t.updatedAt, deleted_at: t.deletedAt }) }), _e = Schema.transform(Schema.mutable(Schema.NullishOr(Schema.Array(H))), Schema.mutable(Schema.Array(F)), { strict: true, decode: (t) => t || [], encode: (t) => t }), fe = Schema.Struct({ name: Schema.String, code: Schema.String }), Ae = Schema.transform(me, fe, { strict: true, decode: (t) => t, encode: (t) => t }), Re = Schema.Struct({ name: Schema.String, code: Schema.String }), he = Schema.transform(Se, Re, { strict: true, decode: (t) => ({ name: t.name, code: t.code }), encode: (t) => ({ id: "", ...t }) }), Ue = Schema.Struct({ id: Schema.String }), _ = "/v1/brands", ye = i.pipe(Effect.andThen(({ httpClient: t }) => t), Effect.andThen((t) => ({ getAll: () => t.get(_).pipe(Effect.flatMap(s(_e))), getById: (n) => t.get(`${_}/${n}`).pipe(Effect.flatMap(s(H))), create: (n) => t.post(_, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync(Ae)(n)) }).pipe(Effect.flatMap(s(Ue))), update: (n) => t.put(`${_}/${n.id}`, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync(he)(n)) }).pipe(Effect.flatMap(p)), delete: (n) => t.del(`${_}/${n}`).pipe(Effect.flatMap(p)) })), Effect.provide(i.Live)), Ne = Layer.effect(N, ye), Ee = Layer.effect(N, N), Le = Layer.effect(m$1, N);
class E extends Effect.Tag("ClientRepository")() {
}
var Oe = ((t) => (t[t.DNI = 0] = "DNI", t[t.PASAPORTE = 1] = "PASAPORTE", t[t.RUC = 2] = "RUC", t))(Oe || {});
const S = Schema.Struct({ id: Schema.String, name: Schema.String, fatherLastName: Schema.String, motherLastName: Schema.String, email: Schema.optional(Schema.String), address: Schema.optional(Schema.String), phone: Schema.optional(Schema.String), birthDate: Schema.NullOr(Schema.String), gender: Schema.Boolean, document: Schema.String, documentType: Schema.Number, createdAt: Schema.NullOr(Schema.String), updatedAt: Schema.NullOr(Schema.String), deletedAt: Schema.NullOr(Schema.String) }), I = S.omit("id", "createdAt", "updatedAt", "deletedAt"), P = S.omit("createdAt", "updatedAt", "deletedAt"), U = Schema.Struct({ id: Schema.String, name: Schema.String, father_last_name: Schema.String, mother_last_name: Schema.String, email: Schema.optional(Schema.String), address: Schema.optional(Schema.String), phone: Schema.optional(Schema.String), birth_date: Schema.NullOr(Schema.String), gender: Schema.Boolean, document: Schema.String, document_type: Schema.Number, created_at: Schema.NullOr(Schema.String), updated_at: Schema.NullOr(Schema.String), deleted_at: Schema.NullOr(Schema.String) }), m = Schema.transform(U, S, { strict: true, decode: (t) => ({ ...t, fatherLastName: t.father_last_name, motherLastName: t.mother_last_name, birthDate: t.birth_date, documentType: t.document_type, createdAt: t.created_at, updatedAt: t.updated_at, deletedAt: t.deleted_at }), encode: (t) => ({ ...t, father_last_name: t.fatherLastName, mother_last_name: t.motherLastName, birth_date: t.birthDate, document_type: t.documentType, created_at: t.createdAt, updated_at: t.updatedAt, deleted_at: t.deletedAt }) }), ve = Schema.transform(Schema.mutable(Schema.NullishOr(Schema.Array(m))), Schema.mutable(Schema.Array(S)), { strict: true, decode: (t) => t || [], encode: (t) => t }), B = U.omit("id", "created_at", "updated_at", "deleted_at"), h = Schema.transform(I, B, { strict: true, decode: (t) => ({ ...t, father_last_name: t.fatherLastName, mother_last_name: t.motherLastName, birth_date: t.birthDate, document_type: t.documentType }), encode: (t) => ({ ...t, fatherLastName: t.father_last_name, motherLastName: t.mother_last_name, birthDate: t.birth_date, documentType: t.document_type }) }), be = Schema.String, M = U.omit("created_at", "updated_at", "deleted_at"), L = Schema.transform(P, M, { strict: true, decode: (t) => ({ ...t, father_last_name: t.fatherLastName, mother_last_name: t.motherLastName, birth_date: t.birthDate, document_type: t.documentType }), encode: (t) => ({ ...t, fatherLastName: t.father_last_name, motherLastName: t.mother_last_name, birthDate: t.birth_date, documentType: t.document_type }) }), x = Schema.Struct({ id: Schema.String, person: S, createdAt: Schema.NullOr(Schema.String), updatedAt: Schema.NullOr(Schema.String), deletedAt: Schema.NullOr(Schema.String) }), Te = Schema.Struct({ person: I }), Ce = Schema.Struct({ id: Schema.String, person: P }), ke = Schema.Struct({ id: Schema.String, person: U, created_at: Schema.NullOr(Schema.String), updated_at: Schema.NullOr(Schema.String), deleted_at: Schema.NullOr(Schema.String) }), q = Schema.transform(ke, x, { strict: true, decode: (t) => ({ ...t, person: Schema.decodeUnknownSync(m)(t.person), createdAt: t.created_at, updatedAt: t.updated_at, deletedAt: t.deleted_at }), encode: (t) => ({ ...t, person: Schema.encodeUnknownSync(m)(t.person), created_at: t.createdAt, updated_at: t.updatedAt, deleted_at: t.deletedAt }) }), Ie = Schema.transform(Schema.mutable(Schema.NullishOr(Schema.Array(q))), Schema.mutable(Schema.Array(x)), { strict: true, decode: (t) => t || [], encode: (t) => t }), Pe = Schema.Struct({ person: B }), Be = Schema.transform(Te, Pe, { strict: true, encode: (t) => ({ ...t, person: Schema.encodeUnknownSync(h)(t.person) }), decode: (t) => ({ ...t, person: Schema.decodeUnknownSync(h)(t.person) }) }), Me = Schema.String, De = Schema.Struct({ id: Schema.String, person: M }), $e = Schema.transform(Ce, De, { strict: true, encode: (t) => ({ ...t, person: Schema.encodeUnknownSync(L)(t.person) }), decode: (t) => ({ ...t, person: Schema.decodeUnknownSync(L)(t.person) }) }), f = "/v1/clients", we = i.pipe(Effect.andThen(({ httpClient: t }) => t), Effect.andThen((t) => ({ getAll: () => t.get(f).pipe(Effect.flatMap(s(Ie))), getById: (n) => t.get(`${f}/${n}`).pipe(Effect.flatMap(s(q))), create: (n) => t.post(f, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync(Be)(n)) }).pipe(Effect.flatMap(s(Me))), update: (n) => t.put(`${f}/${n.id}`, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync($e)(n)) }).pipe(Effect.flatMap(p)), delete: (n) => t.del(`${f}/${n}`).pipe(Effect.flatMap(p)) })), Effect.provide(i.Live)), We = Layer.effect(E, we), Fe = Layer.effect(E, E), He = Layer.effect(h$1, E);
class O extends Effect.Tag("PersonRepository")() {
}
const A = "/v1/person", xe = i.pipe(Effect.andThen(({ httpClient: t }) => t), Effect.andThen((t) => ({ getAll: () => t.get(A).pipe(Effect.flatMap(s(ve))), getById: (n) => t.get(`${A}/${n}`).pipe(Effect.flatMap(s(m))), create: (n) => t.post(A, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync(h)(n)) }).pipe(Effect.flatMap(s(be))), update: (n) => t.post(A, { body: HttpBody.unsafeJson(Schema.encodeUnknownSync(m)(n)) }).pipe(Effect.flatMap(p)), delete: (n) => t.post(A, { body: HttpBody.unsafeJson({ id: n }) }).pipe(Effect.flatMap(p)) })), Effect.provide(i.Live)), qe = Layer.effect(O, xe), Je = Layer.effect(O, O);
class je extends Effect.Tag("PersonUsecase")() {
}
const Ve = Layer.effect(je, O);
class v extends Effect.Tag("WorkerRepository")() {
}
const J = Schema.Struct({ id: Schema.String, person: S, positions: Schema.mutable(Schema.Array(Schema.Number)), createdAt: Schema.NullOr(Schema.String), updatedAt: Schema.NullOr(Schema.String), deletedAt: Schema.NullOr(Schema.String) }), ze = Schema.Struct({ person: I, positions: Schema.mutable(Schema.Array(Schema.Number)) }), Ye = Schema.Struct({ id: Schema.String, person: P, positions: Schema.mutable(Schema.Array(Schema.Number)) }), Ge = Schema.Struct({ id: Schema.String, person: U, positions: Schema.mutable(Schema.Array(Schema.Number)), created_at: Schema.NullOr(Schema.String), updated_at: Schema.NullOr(Schema.String), deleted_at: Schema.NullOr(Schema.String) }), j = Schema.transform(Ge, J, { strict: true, decode: (t) => ({ ...t, person: Schema.decodeUnknownSync(m)(t.person), createdAt: t.created_at, updatedAt: t.updated_at, deletedAt: t.deleted_at }), encode: (t) => ({ ...t, person: Schema.encodeUnknownSync(m)(t.person), created_at: t.createdAt, updated_at: t.updatedAt, deleted_at: t.deletedAt }) }), Ke = Schema.transform(Schema.mutable(Schema.NullishOr(Schema.Array(j))), Schema.mutable(Schema.Array(J)), { strict: true, decode: (t) => t || [], encode: (t) => t }), Qe = Schema.Struct({ person: B, positions: Schema.mutable(Schema.Array(Schema.Number)) }), Xe = Schema.transform(ze, Qe, { strict: true, encode: (t) => ({ ...t, person: Schema.encodeUnknownSync(h)(t.person) }), decode: (t) => ({ ...t, person: Schema.decodeUnknownSync(h)(t.person) }) }), Ze = Schema.String, et = Schema.Struct({ id: Schema.String, person: M, positions: Schema.mutable(Schema.Array(Schema.Number)) }), tt = Schema.transform(Ye, et, { strict: true, encode: (t) => ({ ...t, person: Schema.encodeUnknownSync(L)(t.person) }), decode: (t) => ({ ...t, person: Schema.decodeUnknownSync(L)(t.person) }) }), R = "/v1/workers", rt = i.pipe(Effect.andThen(({ httpClient: t }) => t), Effect.andThen((t) => ({ getAll: () => t.get(R).pipe(Effect.flatMap(s(Ke))), getById: (n) => t.get(`${R}/${n}`).pipe(Effect.flatMap(s(j))), create: (n) => t.post(R, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync(Xe)(n)) }).pipe(Effect.flatMap(s(Ze))), update: (n) => t.put(R, { body: HttpBody.unsafeJson(Schema.decodeUnknownSync(tt)(n)) }).pipe(Effect.flatMap(p)), delete: (n) => t.del(`${R}/${n}`).pipe(Effect.flatMap(p)) })), Effect.provide(i.Live)), nt = Layer.effect(v, rt), at = Layer.effect(v, v), ot = Layer.effect(f$1, v), st = ue.pipe(Layer.provide(le), Layer.provide(pe)), dt = Le.pipe(Layer.provide(Ee), Layer.provide(Ne)), it = He.pipe(Layer.provide(Fe), Layer.provide(We)), ct = Ve.pipe(Layer.provide(Je), Layer.provide(qe)), pt = ot.pipe(Layer.provide(at), Layer.provide(nt)), lt = Layer.mergeAll(st, dt, it, ct, pt), _t = ManagedRuntime.make(lt);

export { Oe as O, _t as _, gt as g };
//# sourceMappingURL=runtimes-OMKR2jB5.mjs.map
