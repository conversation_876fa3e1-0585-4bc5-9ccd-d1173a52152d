import { isFiberFailure, FiberFailureCauseId } from 'effect/Runtime';
import { g as gt } from './runtimes-OMKR2jB5.mjs';

const n = (r) => {
  const e = gt.make({ error: { message: "Error desconocido", code: 0, details: r }, correlationId: "0" });
  if (!isFiberFailure(r)) return e;
  const o = r[FiberFailureCauseId];
  return o._tag !== "Fail" ? e : o.error;
};

export { n };
//# sourceMappingURL=effectErrors-BZsTgurj.mjs.map
