import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import { useState, useEffect } from 'react';
import { useQuery, queryOptions, useQueryClient, useMutation } from '@tanstack/react-query';
import { g as gt } from '../nitro/nitro.mjs';
import { n } from './effectErrors-BZsTgurj.mjs';
import { _ as _t, O as Oe } from './runtimes-OMKR2jB5.mjs';
import { useReactTable, getCoreRowModel, createColumnHelper, flexRender } from '@tanstack/react-table';
import { User, Mail, MapPin, Phone, Calendar, FileText, Edit, Trash } from 'lucide-react';
import { toast } from 'react-toastify';
import { S, x as xe } from './form-9KniRK7M.mjs';
import { create } from 'mutative';
import * as a from 'valibot';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:async_hooks';
import 'vinxi/lib/invariant';
import 'vinxi/lib/path';
import 'node:url';
import '@tanstack/router-core';
import 'tiny-invariant';
import '@tanstack/start-server-core';
import '@tanstack/start-client-core';
import '@tanstack/react-router';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'effect';
import '@tanstack/react-router-with-query';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'effect/Runtime';
import '@effect/platform';
import '@tanstack/react-form';
import 'downshift';
import 'use-mutative';
import 'clsx';
import 'tailwind-merge';

const D = ({ client: n }) => queryOptions({ queryKey: ["clients"], queryFn: () => _t.runPromise(n.getAll()) }), G = ({ client: n }, r) => queryOptions({ queryKey: ["clients", r], queryFn: () => _t.runPromise(n.getById(r)) });
function $({ table: n }) {
  return jsxs("table", { className: "table", children: [jsx("thead", { children: n.getHeaderGroups().map((r) => jsx("tr", { children: r.headers.map((o) => jsx("th", { children: o.isPlaceholder ? null : flexRender(o.column.columnDef.header, o.getContext()) }, o.id)) }, r.id)) }), jsx("tbody", { children: n.getRowModel().rows.map((r) => jsx("tr", { children: r.getVisibleCells().map((o) => jsx("td", { children: flexRender(o.column.columnDef.cell, o.getContext()) }, o.id)) }, r.id)) })] });
}
function A({ onClose: n }) {
  return jsx("button", { type: "button", className: "btn btn-sm btn-circle btn-ghost absolute top-2 right-2", onClick: n, children: "\u2715" });
}
function _() {
  const n = gt(), { client: r } = n, o = useQueryClient(), l = D(n).queryKey;
  return useMutation({ mutationFn: (t) => _t.runPromise(r.delete(t)), onSuccess: (t, i) => {
    o.setQueryData(l, (c) => create(c != null ? c : [], (d) => {
      const m = d.findIndex((b) => b.id === i);
      m !== -1 && d.splice(m, 1);
    }));
  } });
}
function J({ isOpen: n, setIsOpen: r, client: o }) {
  const { mutate: l } = _();
  return jsx("div", { className: S("modal", n && "modal-open"), children: jsxs("div", { className: "modal-box", children: [jsx(A, { onClose: () => r(false) }), jsx("h3", { className: "font-bold text-lg", children: "Eliminar cliente" }), jsx("p", { children: "\xBFEst\xE1s seguro de que quieres eliminar este cliente?" }), jsxs("p", { className: "mt-2 text-gray-600 text-sm", children: ["Cliente: ", o.person.name, " ", o.person.fatherLastName, " ", o.person.motherLastName] }), jsxs("div", { className: "modal-action", children: [jsx("button", { type: "button", className: "btn btn-primary", onClick: () => r(false), children: "Cancelar" }), jsx("button", { type: "button", className: "btn btn-error", onClick: () => {
    l(o.id, { onSuccess: () => {
      toast.success("Cliente eliminado"), r(false);
    }, onError: (t) => {
      console.log(t), toast.error("Error al eliminar cliente");
    } });
  }, children: "Eliminar" })] })] }) });
}
function L({ open: n = false, title: r, text: o }) {
  const [l, t] = useState(n);
  return jsx("div", { className: S("modal", n && "modal-open"), children: jsxs("div", { className: "modal-box", children: [jsx(A, { onClose: () => t(false) }), jsx("h3", { className: "font-bold text-lg", children: r }), jsx("p", { children: o })] }) });
}
function W() {
  const n = gt(), { client: r } = n, o = useQueryClient(), l = D(n).queryKey;
  return useMutation({ mutationFn: (t) => _t.runPromise(r.update(t)), onSuccess: (t, i) => {
    o.setQueryData(l, (c) => create(c != null ? c : [], (d) => {
      const m = d.findIndex((b) => b.id === i.id);
      m !== -1 && (d[m] = { ...d[m], person: i.person, updatedAt: (/* @__PURE__ */ new Date()).toISOString() });
    }));
  } });
}
const Y = a.object({ name: a.pipe(a.string("Debe ingresar un nombre"), a.minLength(1, "Debe tener al menos un caracter")), fatherLastName: a.pipe(a.string("Debe ingresar un apellido"), a.minLength(1, "Debe tener al menos un caracter")), motherLastName: a.pipe(a.string("Debe ingresar un apellido"), a.minLength(1, "Debe tener al menos un caracter")), email: a.union([a.pipe(a.string("Debe ingresar un email"), a.email("Debe ingresar un email valido")), a.literal("")]), address: a.optional(a.string()), phone: a.optional(a.string()), birthDate: a.nullable(a.string()), gender: a.boolean(), document: a.pipe(a.string("Debe ingresar un documento"), a.minLength(8, "Debe tener al menos 8 caracteres")), documentType: a.number() });
function X({ setIsOpen: n$1, client: r }) {
  const { mutate: o } = W(), { id: l, person: { id: t, ...i }, ...c } = r, d = xe({ defaultValues: { ...i }, validators: { onChange: Y }, onSubmit: ({ value: b }) => {
    o({ id: l, person: { ...b, id: t } }, { onSuccess: () => {
      toast.success("Cliente actualizado"), m();
    }, onError: (E) => {
      console.log(E);
      const { error: k } = n(E);
      toast.error(k.message);
    } });
  } });
  function m() {
    d.reset(), n$1(false);
  }
  return { form: d, handleClose: m };
}
function Z({ isOpen: n, setIsOpen: r, client: o }) {
  const { form: l, handleClose: t } = X({ setIsOpen: r, client: o });
  return jsx("div", { className: S("modal", n && "modal-open"), children: jsxs("div", { className: "modal-box", children: [jsx(A, { onClose: t }), jsx("h3", { className: "font-bold text-lg", children: "Editar Cliente" }), jsx("form", { onSubmit: (i) => {
    i.preventDefault(), l.handleSubmit();
  }, children: jsxs(l.AppForm, { children: [jsxs("fieldset", { className: "fieldset", children: [jsx(l.AppField, { name: "name", children: ({ FSTextField: i }) => jsx(i, { label: "Nombre", placeholder: "Nombre", prefixComponent: jsx(User, { size: 16 }) }) }), jsx(l.AppField, { name: "fatherLastName", children: ({ FSTextField: i }) => jsx(i, { label: "Apellido Paterno", placeholder: "Apellido Paterno", prefixComponent: jsx(User, { size: 16 }) }) }), jsx(l.AppField, { name: "motherLastName", children: ({ FSTextField: i }) => jsx(i, { label: "Apellido Materno", placeholder: "Apellido Materno", prefixComponent: jsx(User, { size: 16 }) }) }), jsx(l.AppField, { name: "email", children: ({ FSTextField: i }) => jsx(i, { label: "Correo Electr\xF3nico", placeholder: "<EMAIL>", prefixComponent: jsx(Mail, { size: 16 }) }) }), jsx(l.AppField, { name: "address", children: ({ FSTextField: i }) => jsx(i, { label: "Direcci\xF3n", placeholder: "Direcci\xF3n", prefixComponent: jsx(MapPin, { size: 16 }) }) }), jsx(l.AppField, { name: "phone", children: ({ FSTextField: i }) => jsx(i, { label: "Tel\xE9fono", placeholder: "Tel\xE9fono", prefixComponent: jsx(Phone, { size: 16 }) }) }), jsx(l.AppField, { name: "birthDate", children: ({ FSTextField: i }) => jsx(i, { label: "Fecha de Nacimiento", placeholder: "YYYY-MM-DD", type: "date", prefixComponent: jsx(Calendar, { size: 16 }) }) }), jsx(l.AppField, { name: "document", children: ({ FSTextField: i }) => jsx(i, { label: "Documento", placeholder: "N\xFAmero de Documento", prefixComponent: jsx(FileText, { size: 16 }) }) }), jsx(l.AppField, { name: "documentType", children: ({ FSSelectField: i }) => jsx(i, { label: "Tipo de Documento", placeholder: "Tipo de Documento", isNumber: true, options: [{ value: 0, label: "DNI" }, { value: 1, label: "Pasaporte" }, { value: 2, label: "RUC" }] }) }), jsx(l.AppField, { name: "gender", children: ({ FSToggleField: i }) => jsx(i, { label: "G\xE9nero", trueLabel: "Masculino", falseLabel: "Femenino" }) })] }), jsx("div", { className: "modal-action", children: jsx("button", { type: "submit", className: "btn btn-primary", children: "Actualizar" }) })] }) })] }) });
}
function ee({ isOpen: n$1, setIsOpen: r, id: o }) {
  const l = gt(), { data: t, isError: i, error: c, isPending: d } = useQuery({ ...G(l, o), enabled: n$1 });
  return useEffect(() => {
    c && console.log(c);
  }, [c]), d ? jsx(L, { text: "Cargando..." }) : i ? jsx(L, { text: "No se pudo cargar el cliente", title: n(c).error.code.toString() }) : jsx(Z, { isOpen: n$1, setIsOpen: r, client: t });
}
const p = createColumnHelper(), ne = [p.accessor("person.name", { header: "Nombre", cell: (n) => n.getValue() }), p.accessor("person.fatherLastName", { header: "Apellido Paterno", cell: (n) => n.getValue() }), p.accessor("person.motherLastName", { header: "Apellido Materno", cell: (n) => n.getValue() }), p.accessor("person.email", { header: "Email", cell: (n) => n.getValue() || "N/A" }), p.accessor("person.phone", { header: "Tel\xE9fono", cell: (n) => n.getValue() || "N/A" }), p.accessor("person.document", { header: "Documento", cell: (n) => {
  const r = n.row.original.person.documentType, o = n.getValue();
  return `${r === Oe.DNI ? "DNI" : r === Oe.PASAPORTE ? "Pasaporte" : "RUC"}: ${o}`;
} }), p.display({ id: "actions", header: "Acciones", cell: ({ row: n }) => {
  const [r, o] = useState(false), [l, t] = useState(false), i = n.original;
  return jsxs("div", { className: "flex gap-2", children: [jsx("button", { type: "button", className: "btn btn-sm btn-primary", onClick: () => o(true), children: jsx(Edit, { size: 16 }) }), jsx("button", { type: "button", className: "btn btn-sm btn-error", onClick: () => t(true), children: jsx(Trash, { size: 16 }) }), jsx(ee, { isOpen: r, setIsOpen: o, id: i.id }), jsx(J, { isOpen: l, setIsOpen: t, client: i })] });
} })];
function oe({ clients: n }) {
  const r = useReactTable({ data: n, columns: ne, getCoreRowModel: getCoreRowModel() });
  return jsx($, { table: r });
}
function te() {
  const n$1 = gt(), { data: r, isError: o, error: l, isPending: t } = useQuery(D(n$1));
  return useEffect(() => {
    l && console.log(n(l).error);
  }, [l]), o ? jsxs("div", { children: ["Error: ", n(l).error.message] }) : t ? jsx("div", { children: "Loading..." }) : jsx(oe, { clients: r });
}
function re() {
  const n = gt(), { client: r } = n, o = useQueryClient(), l = D(n).queryKey;
  return useMutation({ mutationFn: (t) => _t.runPromise(r.create(t)), onSuccess: (t, i) => {
    o.setQueryData(l, (c) => create(c != null ? c : [], (d) => {
      d.push({ id: t, person: i.person, createdAt: (/* @__PURE__ */ new Date()).toISOString(), updatedAt: null, deletedAt: null });
    }));
  } });
}
const le = { name: "", fatherLastName: "", motherLastName: "", email: "", address: "", phone: "", birthDate: "", gender: false, document: "", documentType: 0 };
function ie({ setIsOpen: n$1 }) {
  const { mutate: r } = re(), o = xe({ defaultValues: le, validators: { onChange: Y }, onSubmit: ({ value: t }) => {
    r({ person: t }, { onSuccess: () => {
      toast.success("Cliente creado"), l();
    }, onError: (i) => {
      console.log(i);
      const { error: c } = n(i);
      toast.error(c.message);
    } });
  } });
  function l() {
    o.reset(), n$1(false);
  }
  return { form: o, handleClose: l };
}
function ae({ isOpen: n, setIsOpen: r }) {
  const { form: o, handleClose: l } = ie({ setIsOpen: r });
  return jsx("div", { className: S("modal", n && "modal-open"), children: jsxs("div", { className: "modal-box", children: [jsx(A, { onClose: l }), jsx("h3", { className: "font-bold text-lg", children: "Crear Cliente" }), jsx("form", { onSubmit: (t) => {
    t.preventDefault(), o.handleSubmit();
  }, children: jsxs(o.AppForm, { children: [jsxs("fieldset", { className: "fieldset", children: [jsx(o.AppField, { name: "name", children: ({ FSTextField: t }) => jsx(t, { label: "Nombre", placeholder: "Nombre", prefixComponent: jsx(User, { size: 16 }) }) }), jsx(o.AppField, { name: "fatherLastName", children: ({ FSTextField: t }) => jsx(t, { label: "Apellido Paterno", placeholder: "Apellido Paterno", prefixComponent: jsx(User, { size: 16 }) }) }), jsx(o.AppField, { name: "motherLastName", children: ({ FSTextField: t }) => jsx(t, { label: "Apellido Materno", placeholder: "Apellido Materno", prefixComponent: jsx(User, { size: 16 }) }) }), jsx(o.AppField, { name: "email", children: ({ FSTextField: t }) => jsx(t, { label: "Correo Electr\xF3nico", placeholder: "<EMAIL>", prefixComponent: jsx(Mail, { size: 16 }) }) }), jsx(o.AppField, { name: "address", children: ({ FSTextField: t }) => jsx(t, { label: "Direcci\xF3n", placeholder: "Direcci\xF3n", prefixComponent: jsx(MapPin, { size: 16 }) }) }), jsx(o.AppField, { name: "phone", children: ({ FSTextField: t }) => jsx(t, { label: "Tel\xE9fono", placeholder: "Tel\xE9fono", prefixComponent: jsx(Phone, { size: 16 }) }) }), jsx(o.AppField, { name: "birthDate", children: ({ FSTextField: t }) => jsx(t, { label: "Fecha de Nacimiento", placeholder: "YYYY-MM-DD", type: "date", prefixComponent: jsx(Calendar, { size: 16 }) }) }), jsx(o.AppField, { name: "document", children: ({ FSTextField: t }) => jsx(t, { label: "Documento", placeholder: "N\xFAmero de Documento", prefixComponent: jsx(FileText, { size: 16 }) }) }), jsx(o.AppField, { name: "documentType", children: ({ FSSelectField: t }) => jsx(t, { label: "Tipo de Documento", placeholder: "Tipo de Documento", isNumber: true, options: [{ value: 0, label: "DNI" }, { value: 1, label: "Pasaporte" }, { value: 2, label: "RUC" }] }) }), jsx(o.AppField, { name: "gender", children: ({ FSToggleField: t }) => jsx(t, { label: "G\xE9nero", trueLabel: "Masculino", falseLabel: "Femenino" }) })] }), jsx("div", { className: "modal-action", children: jsx("button", { type: "submit", className: "btn btn-primary", children: "Crear" }) })] }) })] }) });
}
const Ke = function() {
  const [r, o] = useState(false);
  return jsxs(Fragment, { children: [jsx("div", { className: "container mx-auto", children: jsx("div", { className: "card bg-base-300", children: jsxs("div", { className: "card-body", children: [jsx("div", { children: jsx("button", { type: "button", className: "btn btn-primary", onClick: () => o(true), children: "Nuevo cliente" }) }), jsx(te, {})] }) }) }), jsx(ae, { isOpen: r, setIsOpen: o })] });
};

export { Ke as component };
//# sourceMappingURL=index-FvsCZ-Wl.mjs.map
