import { jsx } from 'react/jsx-runtime';
import { useQuery, queryOptions } from '@tanstack/react-query';
import { Outlet, Navigate } from '@tanstack/react-router';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import { _ as _t } from './runtimes-OMKR2jB5.mjs';
import { g as gt } from '../nitro/nitro.mjs';
import { n } from './effectErrors-BZsTgurj.mjs';
import 'effect';
import '@effect/platform';
import 'valibot';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:async_hooks';
import 'vinxi/lib/invariant';
import 'vinxi/lib/path';
import 'node:url';
import '@tanstack/router-core';
import 'tiny-invariant';
import '@tanstack/start-server-core';
import '@tanstack/start-client-core';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import '@tanstack/react-router-with-query';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'effect/Runtime';

const v = ({ auth: t }) => queryOptions({ queryKey: ["authenticated"], queryFn: () => _t.runPromise(t.getSession()) }), B = function() {
  const p = gt(), { isLoading: n$1, isSuccess: r, isError: i, error: e } = useQuery({ ...v(p) });
  return useEffect(() => {
    if (i) {
      const u = n(e);
      toast.error(u.error.message);
    }
  }, [i, e]), useEffect(() => {
    r && toast.success("Usuario autenticado");
  }, [r]), n$1 ? jsx("div", { children: "Verificando autenticaci\xF3n..." }) : r ? jsx(Outlet, {}) : jsx(Navigate, { to: "/login" });
};

export { B as component };
//# sourceMappingURL=route-B370gsWI.mjs.map
