import * as a from 'valibot';
import N from 'tiny-invariant';
import { createServerFn } from '@tanstack/start-client-core';
import { setCookie, getCookie } from '@tanstack/start-server-core';

function E(e) {
  return e.replace(/^\/|\/$/g, "");
}
const s = (e, r, _) => {
  N(_, "\u{1F6A8}splitImportFn required for the server functions server runtime, but was not provided.");
  const a = `/${E(r)}/${e}`;
  return Object.assign(_, { url: a, functionId: e });
}, i = { BASE_URL: "/_server", CWD: "/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend", DEV: false, DEVTOOLS: false, MANIFEST: globalThis.MANIFEST, MODE: "production", PROD: true, ROUTERS: ["public", "client", "ssr", "server", "api"], ROUTER_HANDLER: "node_modules/@tanstack/start-server-functions-handler/dist/esm/index.js", ROUTER_NAME: "server", ROUTER_TYPE: "http", SERVER_BASE_URL: "", SSR: true, TSS_API_BASE: "/api", TSS_CLIENT_BASE: "/_build", TSS_OUTPUT_PUBLIC_DIR: "/home/<USER>/Work/cudesi/fhyona-2/fhyona-v2-frontend/.output/public", TSS_PUBLIC_BASE: "/", TSS_SERVER_FN_BASE: "/_server", VITE_API_URL: "http://localhost:8000" }, h = a.object({ VITE_API_URL: a.string() }), u = a.parse(h, i), o = { API_URL: u.VITE_API_URL, UI_THEME_KEY: "ui-theme" }, T = s("app_modules_auth_server_theme_ts--getThemeServerFn_createServerFn_handler", "/_server", (e, r) => l.__executeServer(e, r)), m = s("app_modules_auth_server_theme_ts--setThemeServerFn_createServerFn_handler", "/_server", (e, r) => d.__executeServer(e, r)), l = createServerFn().handler(T, async () => getCookie(o.UI_THEME_KEY) || "light"), d = createServerFn({ method: "POST" }).validator((e) => e).handler(m, async ({ data: e }) => {
  setCookie(o.UI_THEME_KEY, e);
});

export { T as getThemeServerFn_createServerFn_handler, m as setThemeServerFn_createServerFn_handler };
//# sourceMappingURL=theme-hT9ohm61.mjs.map
