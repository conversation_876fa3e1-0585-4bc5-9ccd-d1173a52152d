import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import 'node:http';
import 'node:https';
export { n as default } from './chunks/nitro/nitro.mjs';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:async_hooks';
import 'vinxi/lib/invariant';
import 'vinxi/lib/path';
import 'node:url';
import '@tanstack/router-core';
import 'tiny-invariant';
import '@tanstack/start-server-core';
import '@tanstack/start-client-core';
import 'react/jsx-runtime';
import '@tanstack/react-router';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'react-toastify';
import 'react';
import 'effect';
import '@tanstack/react-query';
import '@tanstack/react-router-with-query';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
//# sourceMappingURL=index.mjs.map
