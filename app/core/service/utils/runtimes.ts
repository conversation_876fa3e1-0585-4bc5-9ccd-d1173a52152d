import { Layer, ManagedRuntime } from "effect";
import { authApiRepoLive } from "~/modules/auth/service/repo/api/auth-api";
import { authRepositoryLive } from "~/modules/auth/service/repository";
import { authUsecaseLive } from "~/modules/auth/service/usecase";
import { brandApiRepoLive } from "~/modules/brand/service/repo/api/brand-api";
import { BrandRepositoryLive } from "~/modules/brand/service/repository";
import { brandUsecaseLive } from "~/modules/brand/service/usecase";
import { clientApiRepoLive } from "~/modules/client/service/repo/api/client-api";
import { ClientRepositoryLive } from "~/modules/client/service/repository";
import { clientUsecaseLive } from "~/modules/client/service/usecase";
import { personApiRepoLive } from "~/modules/person/service/repo/api/person-api";
import { PersonRepositoryLive } from "~/modules/person/service/repository";
import { personUsecaseLive } from "~/modules/person/service/usecase";
import { workerApiRepoLive } from "~/modules/worker/service/repo/api/worker-api";
import { WorkerRepositoryLive } from "~/modules/worker/service/repository";
import { workerUsecaseLive } from "~/modules/worker/service/usecase";

const makeAuthUsecaseLive = authUsecaseLive.pipe(
	Layer.provide(authRepositoryLive),
	Layer.provide(authApiRepoLive),
);

const makeBrandUsecaseLive = brandUsecaseLive.pipe(
	Layer.provide(BrandRepositoryLive),
	Layer.provide(brandApiRepoLive),
);

const makeClientUsecaseLive = clientUsecaseLive.pipe(
	Layer.provide(ClientRepositoryLive),
	Layer.provide(clientApiRepoLive),
);

const makePersonUsecaseLive = personUsecaseLive.pipe(
	Layer.provide(PersonRepositoryLive),
	Layer.provide(personApiRepoLive),
);

const makeWorkerUsecaseLive = workerUsecaseLive.pipe(
	Layer.provide(WorkerRepositoryLive),
	Layer.provide(workerApiRepoLive),
);

const MainLayer = Layer.mergeAll(
	makeAuthUsecaseLive,
	makeBrandUsecaseLive,
	makeClientUsecaseLive,
	makePersonUsecaseLive,
	makeWorkerUsecaseLive,
);

export const AppRuntime = ManagedRuntime.make(MainLayer);
