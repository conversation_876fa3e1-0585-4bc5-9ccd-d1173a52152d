import { useNavigate } from "@tanstack/react-router";
import { User } from "lucide-react";
import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useLogin from "../../hooks/use-login";
import { authActions } from "../../store/auth";
import { LoginSchema } from "./schema";

export default function LoginForm() {
	const { mutateAsync } = useLogin();
	const navigate = useNavigate();

	const form = useAppForm({
		defaultValues: {
			username: "",
			password: "",
		},
		onSubmit: async ({ value }) => {
			mutateAsync(value, {
				onSuccess(result) {
					authActions.setUser(result.user);
					navigate({ to: "/admin" });
				},
				onError(error) {
					const errorResult = getErrorResult(error);
					toast.error(errorResult.error.message);
				},
			});
		},
		validators: {
			onChange: LoginSchema,
		},
	});

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<form.AppField
					name="username"
					children={({ FSTextField }) => (
						<FSTextField
							label="Usuario o Correo"
							placeholder="<EMAIL>"
							prefixComponent={<User size={16} />}
						/>
					)}
				/>
				<form.AppField
					name="password"
					children={({ FSPasswordField }) => (
						<FSPasswordField label="Contraseña" placeholder="Contraseña" />
					)}
				/>
				<form.SubscribeButton
					label="Iniciar sesión"
					className="btn btn-neutral mt-4 w-full"
				/>
			</form.AppForm>
		</form>
	);
}
