import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { CreateClient, UpdateClient, Client } from "./client";

export class ClientRepository extends Effect.Tag("ClientRepository")<
	ClientRepository,
	{
		readonly getAll: () => Effect.Effect<Client[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Client, AppError>;
		readonly create: (client: CreateClient) => Effect.Effect<string, AppError>;
		readonly update: (client: UpdateClient) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
	}
>() {}
