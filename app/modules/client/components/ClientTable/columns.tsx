import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import { DocumentType } from "~/person/service/model/person";
import { type Client } from "../../service/model/client";
import DeleteClientModal from "../DeleteClientModal";
import EditeClientModal from "../EditeClientModal";

const columnHelper = createColumnHelper<Client>();

export const columns = [
	columnHelper.accessor("person.name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("person.fatherLastName", {
		header: "Apellido Paterno",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("person.motherLastName", {
		header: "Apellido Materno",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("person.email", {
		header: "Email",
		cell: (info) => info.getValue() || "N/A",
	}),
	columnHelper.accessor("person.phone", {
		header: "Teléfono",
		cell: (info) => info.getValue() || "N/A",
	}),
	columnHelper.accessor("person.document", {
		header: "Documento",
		cell: (info) => {
			const documentType = info.row.original.person.documentType;
			const document = info.getValue();
			const typeLabel = documentType === DocumentType.DNI ? "DNI" : 
							 documentType === DocumentType.PASAPORTE ? "Pasaporte" : "RUC";
			return `${typeLabel}: ${document}`;
		},
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const client = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditeClientModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={client.id}
					/>
					<DeleteClientModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						client={client}
					/>
				</div>
			);
		},
	}),
];
