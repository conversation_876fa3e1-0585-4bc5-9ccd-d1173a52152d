import { <PERSON>ttp<PERSON><PERSON> } from "@effect/platform";
import { <PERSON>, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { Brand, CreateBrand, UpdateBrand } from "../../model/brand";
import { BrandRepository } from "../../model/repository";
import {
	Brand<PERSON>romApi,
	BrandListFromApi,
	CreateBrandApiFromCreateBrand,
	CreateBrandApiResponse,
	UpdateBrandApiFromUpdateBrand,
} from "./dto";

const baseUrl = "/v1/brands";

const makeBrandApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(BrandListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(BrandFromApi))),
		create: (brand: CreateBrand) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateBrandApiFromCreateBrand)(brand),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateBrandApiResponse))),
		update: (brand: UpdateBrand) =>
			httpClient
				.put(`${baseUrl}/${brand.id}`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateBrandApiFromUpdateBrand)(brand),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const brandApiRepoLive = Layer.effect(BrandRepository, makeBrandApiRepo);
