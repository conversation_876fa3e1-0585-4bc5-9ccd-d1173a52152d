import type { ColumnDef } from "@tanstack/react-table";
import type { Brand } from "../../service/model/brand";

export const columns: ColumnDef<Brand>[] = [
	{
		accessorKey: "id",
		header: "ID",
	},
	{
		accessorKey: "name",
		header: "Nombre",
	},
	{
		accessorKey: "code",
		header: "Código",
	},
	{
		accessorKey: "createdAt",
		header: "Fecha de Creación",
		cell: ({ row }) => {
			const date = row.getValue("createdAt") as string | null;
			return date ? new Date(date).toLocaleDateString() : "-";
		},
	},
	{
		accessorKey: "updatedAt",
		header: "Última Actualización",
		cell: ({ row }) => {
			const date = row.getValue("updatedAt") as string | null;
			return date ? new Date(date).toLocaleDateString() : "-";
		},
	},
];
