import { useState } from "react";
import { toast } from "react-toastify";
import useCreateBrand from "../../hooks/use-create-brand";

export default function CreateBrandForm() {
	const [name, setName] = useState("");
	const [code, setCode] = useState("");
	const { mutate: createBrand, isPending } = useCreateBrand();

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		if (!name.trim() || !code.trim()) {
			toast.error("Nombre y código son requeridos");
			return;
		}

		createBrand(
			{ name: name.trim(), code: code.trim() },
			{
				onSuccess: () => {
					toast.success("Marca creada exitosamente");
					setName("");
					setCode("");
				},
				onError: (error) => {
					console.error(error);
					toast.error("Error al crear la marca");
				},
			},
		);
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-4">
			<div className="form-control">
				<label className="label">
					<span className="label-text">Nombre</span>
				</label>
				<input
					type="text"
					value={name}
					onChange={(e) => setName(e.target.value)}
					className="input input-bordered"
					placeholder="Ingrese el nombre de la marca"
					disabled={isPending}
				/>
			</div>

			<div className="form-control">
				<label className="label">
					<span className="label-text">Código</span>
				</label>
				<input
					type="text"
					value={code}
					onChange={(e) => setCode(e.target.value)}
					className="input input-bordered"
					placeholder="Ingrese el código de la marca"
					disabled={isPending}
				/>
			</div>

			<button
				type="submit"
				className={`btn btn-primary ${isPending ? "loading" : ""}`}
				disabled={isPending}
			>
				{isPending ? "Creando..." : "Crear Marca"}
			</button>
		</form>
	);
}
