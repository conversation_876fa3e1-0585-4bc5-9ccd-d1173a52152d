/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as AuthedRouteImport } from './routes/_authed/route'
import { Route as IndexImport } from './routes/index'
import { Route as AuthedAdminRouteImport } from './routes/_authed/admin/route'
import { Route as AuthedAdminIndexImport } from './routes/_authed/admin/index'
import { Route as AuthedAdminClientsIndexImport } from './routes/_authed/admin/clients/index'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthedRouteRoute = AuthedRouteImport.update({
  id: '/_authed',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AuthedAdminRouteRoute = AuthedAdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthedRouteRoute,
} as any)

const AuthedAdminIndexRoute = AuthedAdminIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthedAdminRouteRoute,
} as any)

const AuthedAdminClientsIndexRoute = AuthedAdminClientsIndexImport.update({
  id: '/clients/',
  path: '/clients/',
  getParentRoute: () => AuthedAdminRouteRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_authed': {
      id: '/_authed'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthedRouteImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/_authed/admin': {
      id: '/_authed/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthedAdminRouteImport
      parentRoute: typeof AuthedRouteImport
    }
    '/_authed/admin/': {
      id: '/_authed/admin/'
      path: '/'
      fullPath: '/admin/'
      preLoaderRoute: typeof AuthedAdminIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
    '/_authed/admin/clients/': {
      id: '/_authed/admin/clients/'
      path: '/clients'
      fullPath: '/admin/clients'
      preLoaderRoute: typeof AuthedAdminClientsIndexImport
      parentRoute: typeof AuthedAdminRouteImport
    }
  }
}

// Create and export the route tree

interface AuthedAdminRouteRouteChildren {
  AuthedAdminIndexRoute: typeof AuthedAdminIndexRoute
  AuthedAdminClientsIndexRoute: typeof AuthedAdminClientsIndexRoute
}

const AuthedAdminRouteRouteChildren: AuthedAdminRouteRouteChildren = {
  AuthedAdminIndexRoute: AuthedAdminIndexRoute,
  AuthedAdminClientsIndexRoute: AuthedAdminClientsIndexRoute,
}

const AuthedAdminRouteRouteWithChildren =
  AuthedAdminRouteRoute._addFileChildren(AuthedAdminRouteRouteChildren)

interface AuthedRouteRouteChildren {
  AuthedAdminRouteRoute: typeof AuthedAdminRouteRouteWithChildren
}

const AuthedRouteRouteChildren: AuthedRouteRouteChildren = {
  AuthedAdminRouteRoute: AuthedAdminRouteRouteWithChildren,
}

const AuthedRouteRouteWithChildren = AuthedRouteRoute._addFileChildren(
  AuthedRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminRouteRouteWithChildren
  '/admin/': typeof AuthedAdminIndexRoute
  '/admin/clients': typeof AuthedAdminClientsIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminIndexRoute
  '/admin/clients': typeof AuthedAdminClientsIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_authed': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/_authed/admin': typeof AuthedAdminRouteRouteWithChildren
  '/_authed/admin/': typeof AuthedAdminIndexRoute
  '/_authed/admin/clients/': typeof AuthedAdminClientsIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '' | '/login' | '/admin' | '/admin/' | '/admin/clients'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '' | '/login' | '/admin' | '/admin/clients'
  id:
    | '__root__'
    | '/'
    | '/_authed'
    | '/login'
    | '/_authed/admin'
    | '/_authed/admin/'
    | '/_authed/admin/clients/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthedRouteRoute: typeof AuthedRouteRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthedRouteRoute: AuthedRouteRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_authed",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_authed": {
      "filePath": "_authed/route.tsx",
      "children": [
        "/_authed/admin"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_authed/admin": {
      "filePath": "_authed/admin/route.tsx",
      "parent": "/_authed",
      "children": [
        "/_authed/admin/",
        "/_authed/admin/clients/"
      ]
    },
    "/_authed/admin/": {
      "filePath": "_authed/admin/index.tsx",
      "parent": "/_authed/admin"
    },
    "/_authed/admin/clients/": {
      "filePath": "_authed/admin/clients/index.tsx",
      "parent": "/_authed/admin"
    }
  }
}
ROUTE_MANIFEST_END */
